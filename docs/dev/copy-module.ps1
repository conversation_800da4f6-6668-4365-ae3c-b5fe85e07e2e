#powershell -File replace-module.ps1 -newName "merchant-branch" -newClass "MerchantBranch"
#docs/dev/copy-module.ps1 -newName "tour-agency" -newClass "TourAgency" -oldName "tour-customer" -oldClass "TourCustomer"
#docs/dev/copy-module.ps1 -newName "tour-agency" -newClass "TourAgency"
#docs/dev/copy-module.ps1 -newName "crawl-dashboard" -newClass "CrawlDashboard" -oldName "module-template-cursor" -oldClass "ModuleTemplateCursor"
param (
    [string]$newName,
    [string]$newClass,
    [string]$oldName = "module-template",
    [string]$oldClass = "ModuleTemplate"
)

# Copy the directory
Copy-Item -Recurse -Path "src/pages/$oldName" -Destination "src/pages/$newName"

# Replace content within files and rename files
Get-ChildItem -Path "src/pages/$newName" -Recurse -File | ForEach-Object {
    $file = $_
    $fileContent = Get-Content $file.FullName

    # Replace content within the file
    if ($fileContent -match "$oldClass|$oldName|${oldName -replace '-', '_'}") {
        $newContent = $fileContent -replace $oldClass, $newClass -replace $oldName, $newName -replace "${oldName -replace '-', '_'}", "${newName -replace '-', '_'}"
        Set-Content $file.FullName -Value $newContent
    }

    # Replace the filename if it contains the specified strings
    if ($file.Name -match "$oldClass|$oldName|${oldName -replace '-', '_'}") {
        $newFileName = $file.Name -replace $oldClass, $newClass -replace $oldName, $newName -replace "${oldName -replace '-', '_'}", "${newName -replace '-', '_'}"
        $newFilePath = Join-Path $file.DirectoryName $newFileName
        Rename-Item -Path $file.FullName -NewName $newFilePath
    }
}

# Rename files if necessary
Get-ChildItem -Path "src/pages/$newName" -Recurse -File | ForEach-Object {
    if ($_.Name -match "$oldName|${oldName -replace '-', '_'}") {
        $newFileName = $_.Name -replace $oldName, $newName -replace "${oldName -replace '-', '_'}", "${newName -replace '-', '_'}"
        Rename-Item -Path $_.FullName -NewName $newFileName
    }
}

