     Dựa vào cấu trúc React app hiện tại, tôi sẽ thiết kế lại routing để sử dụng pattern /dashboard/:tenant_id/[router]. <PERSON><PERSON><PERSON> là một approach tốt cho multi-tenant frontend.

      {/* Dashboard routes - cần tenant_id */}
          <Route path="/dashboard/:tenant_id" element={<LayoutDefault />}>
            <Route element={<TenantRoute />}>
              <Route element={<ProtectedRoute />}>
                {renderRoutes(routes)}
              </Route>
            </Route>
          </Route>


          // Hook để lấy tenant ID từ URL params

export const useTenantId = (): string | undefined => {
const { tenant_id } = useParams<{ tenant_id: string }>();
return tenant_id;
};

private setupInterceptors() {
// Request interceptor
this.instance.interceptors.request.use(
(config) => {
// Add auth token
const token = localStorage.getItem('access_token');
if (token) {
config.headers.Authorization = `Bearer ${token}`;
}

        // Add tenant ID from URL or localStorage
        const tenantId = this.getCurrentTenantId();
        if (tenantId) {
          config.headers['X-Tenant-ID'] = tenantId;
        }
