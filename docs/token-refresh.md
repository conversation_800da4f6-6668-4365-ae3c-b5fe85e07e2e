# Automatic Token Refresh Implementation

## 📋 Overview

This document describes the automatic token refresh functionality implemented in the application. When an API call receives a 401 Unauthorized response due to an expired access token, the system automatically attempts to refresh the token and retry the original request.

## 🔧 Implementation Details

### Global Axios Interceptor

The token refresh logic is implemented as a global axios response interceptor in `src/services/api.service.ts`. This ensures that all API calls throughout the application benefit from automatic token refresh.

### Key Features

1. **Automatic Detection**: Detects 401 Unauthorized responses
2. **Smart Filtering**: Skips token refresh for auth endpoints (`/auth/*`)
3. **Token Refresh**: Automatically calls the refresh token API
4. **Request Retry**: Retries the original failed request with the new token
5. **Queue Management**: Queues multiple failed requests during token refresh
6. **Graceful Fallback**: Shows logout popup when refresh fails
7. **Prevents Loops**: Prevents infinite refresh loops with retry flags

## 🚀 How It Works

### Flow Diagram

```text
API Request → 401 Error → Is Auth Endpoint? → Yes → Skip Refresh
                                ↓ No
                         Check if refreshing → Queue or Refresh
                                ↓
                         Refresh Token API
                                ↓
                    Success → Update Tokens → Retry Original Request
                                ↓
                    Failure → Show Logout Popup
```

### Auth Endpoint Exclusion

The system automatically skips token refresh for authentication endpoints to prevent conflicts:

**Excluded Patterns:**

- `/auth/login`
- `/auth/register`
- `/auth/refresh-token`
- `/auth/verify-email`
- `/auth/otp-config`
- Any URL containing `/auth/`

**Reason:** Auth endpoints either don't require authentication or handle their own token logic.

### State Management

```typescript
// Variables for token refresh logic
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (reason?: any) => void;
}> = [];
```

### Request Queue Processing

When multiple requests fail simultaneously due to token expiration:

1. First request triggers token refresh (`isRefreshing = true`)
2. Subsequent requests are queued in `failedQueue`
3. After token refresh completes, all queued requests are processed
4. All requests retry with the new token

## 📤 API Specification

### Refresh Token Request

```bash
POST /api/admin/v1/auth/refresh-token
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

### Refresh Token Response

```json
{
  "status": {
    "code": 200,
    "message": "Token refreshed successfully",
    "success": true,
    "error_code": "",
    "path": "/api/admin/v1/auth/refresh-token",
    "timestamp": "2025-06-13T15:32:37+07:00"
  },
  "data": {
    "user_id": "123",
    "username": "admin",
    "access_token": "new_access_token",
    "refresh_token": "new_refresh_token",
    "access_token_expires_in": 3600,
    "refresh_token_expires_in": 86400
  }
}
```

## 🔍 Implementation Code

### Response Interceptor

```typescript
apiService.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    // Skip token refresh for auth endpoints
    const isAuthEndpoint = originalRequest.url?.includes('/auth/');

    if (isAuthEndpoint && error.response?.status === 401) {
      logger(
        '[Token Refresh] Skipping refresh for auth endpoint:',
        originalRequest.url,
      );
      return Promise.reject(error);
    }

    // Check if error is 401 and we haven't already tried to refresh
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !isAuthEndpoint
    ) {
      if (isRefreshing) {
        // Queue this request if already refreshing
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiService(originalRequest);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshToken = tokenService.get('refresh_token');

      if (!refreshToken) {
        processQueue(error, null);
        showLogoutPopup();
        return Promise.reject(error);
      }

      try {
        const refreshResponse = await refreshTokenRequest(refreshToken);

        if (refreshResponse.status.success) {
          const { access_token, refresh_token } = refreshResponse.data;

          // Update tokens in storage
          tokenService.saveToken(access_token);
          tokenService.saveRefreshToken(refresh_token);
          setAuthToken(access_token);

          // Update expiry dates
          if (refreshResponse.data.access_token_expires_in) {
            const expiryTime =
              Date.now() + refreshResponse.data.access_token_expires_in * 1000;
            tokenService.saveExpiryDate(expiryTime);
          }

          processQueue(null, access_token);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return apiService(originalRequest);
        } else {
          processQueue(error, null);
          showLogoutPopup();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        processQueue(refreshError, null);
        showLogoutPopup();
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  },
);
```

## 🎯 Usage Examples

### Before (Manual Token Refresh)

```typescript
export async function getProfile(): Promise<ApiResponse<User>> {
  try {
    const res = await apiService.get(`${url}/me`);
    return res.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      // Manual token refresh logic...
      const refreshToken = tokenService.get('refresh_token');
      // ... complex refresh logic
    }
    throw error;
  }
}
```

### After (Automatic Token Refresh)

```typescript
export async function getProfile(): Promise<ApiResponse<User>> {
  const res = await apiService.get(`${url}/me`);
  return res.data;
}
```

## 🚨 Error Handling

### Refresh Token Scenarios

1. **Success**: Token refreshed, original request retried
2. **No Refresh Token**: Immediate logout popup
3. **Refresh Token Expired**: Logout popup after failed refresh
4. **Network Error**: Logout popup after failed refresh
5. **Invalid Refresh Token**: Logout popup after failed refresh

### Logout Popup

When token refresh fails, users see a modal:

```text
Title: "Phiên đăng nhập hết hạn"
Content: "Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại."
Actions: [Đăng nhập lại] [Hủy]
```

## ✅ Benefits

1. **Seamless UX**: Users don't experience interruptions
2. **Automatic**: No manual token refresh code needed
3. **Global**: Works for all API calls automatically
4. **Efficient**: Queues multiple requests during refresh
5. **Safe**: Prevents infinite refresh loops
6. **Graceful**: Proper error handling and user feedback

## 🔧 Configuration

### Token Storage Keys

```typescript
// In tokenService
private readonly TOKEN_KEY = 'access_token';
private readonly REFRESH_TOKEN = 'refresh_token';
private readonly EXPIRE_DATE = 'expire_date';
private readonly REFRESH_EXPIRE_DATE = 'refresh_expire_date';
```

### API Endpoint

```typescript
const refreshTokenRequest = async (refreshToken: string) => {
  const response = await axios.post(
    `${config.API_URL}/api/admin/v1/auth/refresh-token`,
    { refresh_token: refreshToken },
  );
  return response.data;
};
```

## 🧪 Testing

### Manual Testing

1. Login to get tokens
2. Wait for access token to expire (or manually expire it)
3. Make any API call (e.g., get profile)
4. Verify token is automatically refreshed
5. Verify original request succeeds

### Test Scenarios

- ✅ Single request with expired token
- ✅ Multiple simultaneous requests with expired token
- ✅ Refresh token success
- ✅ Refresh token failure
- ✅ No refresh token available
- ✅ Network error during refresh

## 📚 Related Files

- `src/services/api.service.ts` - Main implementation
- `src/services/token.service.ts` - Token storage management
- `src/pages/auth/api.ts` - Authentication APIs
- `docs-api-mac/Auth Module/RefreshToken.bru` - API documentation

## 🔄 Migration Notes

### Removed Manual Refresh Code

The following manual token refresh implementations have been removed:

- `src/pages/user/api.ts` - `getProfile()` function simplified
- Other modules can remove similar manual refresh logic

### Simplified API Functions

All API functions can now be simplified to basic axios calls without manual token refresh handling.
