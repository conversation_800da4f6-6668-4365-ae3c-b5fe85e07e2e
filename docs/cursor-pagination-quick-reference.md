# Cursor Pagination Quick Reference

## 🚀 Quick Setup

### 1. State Setup

```typescript
const [cursorAfter, setCursorAfter] = useState<string | null>(null);
const [cursorHistory, setCursorHistory] = useState<string[]>([]);
const isFirstPage = cursorHistory.length === 0;
```

### 2. API Call

```typescript
const fetchData = useCallback(
  async (payload?: any) => {
    const params = { ...filters, limit: 10, ...payload };
    const response = await getItems(cleanParams(params));

    if (response.status.success) {
      setItems(response.data);
      setCursorAfter(response.meta.next_cursor ?? null);
    }
  },
  [filters, limit],
);
```

### 3. Navigation Handlers

```typescript
const handleNext = () => {
  if (cursorAfter) {
    setCursorHistory((prev) => [...prev, cursorAfter]);
    fetchData({ cursor: cursorAfter });
  }
};

const handleBack = () => {
  if (cursorHistory.length > 0) {
    setCursorHistory((prev) => prev.slice(0, -1));

    if (cursorHistory.length <= 1) {
      fetchData(); // First page
    } else {
      const previousCursor = cursorHistory[cursorHistory.length - 2];
      fetchData({ cursor: previousCursor });
    }
  }
};
```

### 4. Component Usage

```typescript
<CursorPaginationV2
  onBack={handleBack}
  onNext={handleNext}
  disabledBack={isFirstPage}
  disabledNext={!cursorAfter}
  loading={loading}
  isFirstPage={isFirstPage}
/>
```

## 📋 API Contract

### Request

```bash
GET /api/admin/v1/{endpoint}?limit=10&cursor=eyJ0eXBlIjoiaWQi...
```

### Response

```json
{
  "status": { "success": true },
  "data": [...],
  "meta": {
    "next_cursor": "eyJ0eXBlIjoiaWQi...",
    "has_more": true
  }
}
```

## ✅ Checklist

- [ ] Use `cursor` parameter (not `cursorAfter`)
- [ ] Read `response.meta.next_cursor`
- [ ] Manage cursor history for back navigation
- [ ] Set `disabledBack={isFirstPage}`
- [ ] Set `disabledNext={!cursorAfter}`
- [ ] Handle loading states
- [ ] Add error handling

## 🚨 Common Mistakes

1. ❌ `fetchData({ cursorAfter: cursor })`
   ✅ `fetchData({ cursor: cursorAfter })`

2. ❌ `response.meta.cursorAfter`
   ✅ `response.meta.next_cursor`

3. ❌ `disabledBack={!cursorBefore}`
   ✅ `disabledBack={isFirstPage}`

4. ❌ Relying on API's `cursorBefore`
   ✅ Managing cursor history in frontend

## 🔍 Debug Tips

Add these logs to track pagination:

```typescript
console.log('🔍 API Request params:', cleanedParams);
console.log('✅ API Response meta:', response.meta);
console.log('📚 Cursor history:', cursorHistory);
console.log('🔄 Next cursor:', cursorAfter);
```

## 📚 Examples

See working implementations:

- `src/pages/rbac-permission/list/index.tsx`
- `src/pages/rbac-role/list/index.tsx`
- `src/pages/rbac-permission-group/list/index.tsx`
- `src/pages/zrbac-resource/list/index.tsx`
