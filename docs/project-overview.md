# Tổng Quan Dự Án Blog UI V1

## <PERSON><PERSON>ả Dự Án

Đây là dự án Blog UI V1 được xây dựng bằng React 18, TypeScript và Rsbuild. Dự án cung cấp giao diện người dùng cho hệ thống quản lý blog với nhiều tính năng phong phú như quản lý bài vi<PERSON>, <PERSON><PERSON><PERSON>, t<PERSON><PERSON>, và hệ thống xác thực đa tenant.

## Công Nghệ Sử Dụng

### Frontend Framework

- **React 18.3.1** - Framework chính
- **TypeScript 5.6.3** - Ngôn ngữ lập trình
- **Rsbuild 1.0.17** - Build tool hiện đại thay thế Webpack

### UI & Styling

- **Ant Design 5.21.5** - Thư viện UI components
- **@ant-design/icons 5.5.1** - Icon package của Ant Design
- **@ant-design/plots 2.3.2** - Charts và graphs
- **Tailwind CSS 3.4.14** - CSS framework
- **Styled Components 6.1.17** - CSS-in-JS
- **Sass 1.80.4** - CSS preprocessor
- **PostCSS 8.4.47** - CSS post-processor
- **Autoprefixer 10.4.20** - CSS vendor prefixes

### State Management & Routing

- **Zustand 5.0.5** - State management
- **MobX 6.13.7** - State management bổ sung
- **React Router DOM 6.27.0** - Routing
- **Immer 9.0.12** - Immutable state updates

### Utilities & Tools

- **Axios 1.7.7** - HTTP client
- **Day.js 1.11.13** - Date manipulation
- **Moment.js 2.30.1** - Date/time library
- **Lodash 4.17.21** - Utility functions
- **Query String 9.1.1** - URL query string parser
- **Slugify 1.6.6** - URL slug generator
- **DOMPurify 3.1.7** - HTML sanitizer

### Internationalization

- **i18next 23.16.3** - Internationalization framework
- **react-i18next 15.1.0** - React integration cho i18next
- **i18next-http-backend 2.6.2** - HTTP backend cho i18next

### UI Components & Interactions

- **React Icons 5.5.0** - Icon library
- **React Responsive 10.0.0** - Responsive design utilities
- **React Beautiful DND 13.1.1** - Drag and drop
- **@dnd-kit/core 6.3.1** - Modern drag and drop toolkit
- **@dnd-kit/sortable 10.0.0** - Sortable components
- **React Slick 0.30.2** - Carousel component
- **Slick Carousel 1.8.1** - Carousel library
- **React Number Format 5.4.2** - Number formatting
- **React Use 17.5.1** - React hooks library

### Editor & JSON

- **ReactJS TipTap Editor 0.3.5** - Rich text editor
- **React JSON Editor AJRM 2.5.14** - JSON editor
- **React JSON Pretty 2.2.0** - JSON formatter

### Development Tools

- **ESLint 9.13.0** - Linting
- **@typescript-eslint/eslint-plugin 8.11.0** - TypeScript ESLint plugin
- **@typescript-eslint/parser 8.11.0** - TypeScript parser cho ESLint
- **Prettier 3.3.3** - Code formatting
- **Husky 9.1.6** - Git hooks
- **Lint-staged 15.2.10** - Pre-commit file linting
- **i18next-parser 9.1.0** - i18n key extraction

### Build & Bundling

- **@rsbuild/plugin-react 1.0.5** - React plugin cho Rsbuild
- **@rsbuild/plugin-sass 1.0.4** - Sass plugin cho Rsbuild
- **dotenv-webpack 8.1.0** - Environment variables

## Cấu Trúc Dự Án

```
blog-ui-v1/
├── docs/                      # Tài liệu dự án
├── public/                    # Static assets
├── src/
│   ├── components/           # React components
│   │   ├── button/
│   │   ├── form/
│   │   ├── layout/
│   │   ├── table/
│   │   └── ...
│   ├── pages/               # Các trang chính
│   │   ├── auth/           # Xác thực
│   │   ├── blog/           # Quản lý blog
│   │   ├── blog-author/    # Quản lý tác giả
│   │   ├── blog-category/  # Quản lý danh mục
│   │   └── ...
│   ├── hooks/              # Custom React hooks
│   ├── router/             # Routing configuration
│   ├── services/           # API services
│   ├── utils/              # Utility functions
│   └── i18n/              # Internationalization
├── scripts/                # Build và utility scripts
└── tmp/                   # Temporary files
```

## Quy Tắc Đặt Tên

### File và Folder

- **Viết thường**: Tất cả tên file và folder đều viết thường
- **Sử dụng dấu gạch ngang**: Cách nhau bằng dấu `-`
- **Ví dụ**:
  - `select-blog.tsx`
  - `rbac-resource`
  - `blog-category`

### Components

- **PascalCase** cho tên component
- **kebab-case** cho tên file

## Scripts Chính

```bash
# Development
npm run dev              # Khởi chạy development server

# Build
npm run build           # Build production

# Linting & Formatting
npm run lint            # Chạy ESLint
npm run format          # Format code với Prettier

# Preview
npm run preview         # Preview production build
```

## Tính Năng Chính

### 1. Hệ Thống Xác Thực

- **Multi-tenant architecture**: Hỗ trợ nhiều tenant
- **JWT Authentication**: Xác thực bằng token
- **Role-based access**: Phân quyền theo vai trò
- **Email verification**: Xác thực email

### 2. Quản Lý Blog

- **Bài viết (Posts)**: CRUD operations
- **Danh mục (Categories)**: Phân loại bài viết
- **Tags**: Gắn thẻ bài viết
- **Tác giả (Authors)**: Quản lý người viết
- **Timeline**: Quản lý thời gian xuất bản

### 3. Quản Lý Nội Dung

- **Rich Text Editor**: Editor WYSIWYG
- **Media Management**: Quản lý hình ảnh, file
- **SEO Optimization**: Tối ưu SEO

### 4. Hệ Thống Cloud

- **Cloudflare Integration**: Tích hợp Cloudflare
- **GoDaddy Integration**: Quản lý domain
- **Module Management**: Quản lý các module

### 5. E-commerce Features

- **Product Management**: Quản lý sản phẩm
- **Category Management**: Danh mục sản phẩm
- **Attribute Management**: Thuộc tính sản phẩm

### 6. CRM Features

- **Customer Management**: Quản lý khách hàng
- **Customer Groups**: Nhóm khách hàng

### 7. Restaurant Features (Dine-in)

- **Menu Management**: Quản lý thực đơn
- **Category Management**: Danh mục món ăn
- **Product & Toppings**: Sản phẩm và topping

## Internationalization (i18n)

Dự án hỗ trợ đa ngôn ngữ với:

- **i18next**: Framework i18n
- **React i18next**: React integration
- **HTTP Backend**: Load translations từ server

### Ngôn ngữ hỗ trợ:

- **Tiếng Việt** (vi)
- **Tiếng Anh** (en)

## Development Workflow

### 1. Copy Module

```bash
./docs/dev/copy-module.sh "blog-tag" "BlogTag"
```

### 2. Auto Fix ESLint

```bash
npx eslint --fix src/
```

### 3. Git Hooks

- **Pre-commit**: Tự động chạy lint-staged
- **Prettier + ESLint**: Auto format và fix trước khi commit

## Routing Structure

### Auth Routes (Không cần tenant_id)

- `/login` - Đăng nhập
- `/auth/login` - Đăng ký
- `/auth/verify-email` - Xác thực email
- `/auth/resend-verification` - Gửi lại email xác thực

### Dashboard Routes (Cần tenant_id)

- `/dashboard/:tenant_id/*` - Các trang quản lý chính

### Protection

- **ProtectedRoute**: Bảo vệ routes cần authentication
- **TenantRoute**: Bảo vệ routes cần tenant context

## Configuration Files

- **rsbuild.config.ts**: Cấu hình build tool
- **tailwind.config.js**: Cấu hình Tailwind CSS
- **eslint.config.mjs**: Cấu hình ESLint
- **tsconfig.json**: Cấu hình TypeScript
- **postcss.config.js**: Cấu hình PostCSS

## Environment Variables

Dự án sử dụng dotenv-webpack để quản lý environment variables.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance Features

- **Code Splitting**: Lazy loading components
- **Tree Shaking**: Loại bỏ code không sử dụng
- **Bundle Optimization**: Tối ưu bundle size
- **Responsive Design**: Hỗ trợ mobile với react-responsive

## Testing & Quality

- **TypeScript**: Type safety
- **ESLint**: Code quality
- **Prettier**: Code formatting
- **Husky**: Git hooks
- **Lint-staged**: Pre-commit checks

## Deployment

Dự án được build thành static files có thể deploy lên:

- **Vercel**
- **Netlify**
- **AWS S3 + CloudFront**
- **Any static hosting**

## Contributing

1. Fork dự án
2. Tạo feature branch
3. Commit changes với conventional commits
4. Push to branch
5. Tạo Pull Request

## License

Private project - All rights reserved.
