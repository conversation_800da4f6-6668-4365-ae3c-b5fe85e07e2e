# Plan Đơn Giản: <PERSON><PERSON><PERSON><PERSON> Back Button

## Tổng Quan
Thêm nút back button đơn giản với 3 yêu cầu cụ thể:
1. Trang list.tsx có nút back về dashboard
2. Trang form/detail có nút back về trang list
3. Ẩn nút back khi mode là popup
4. <PERSON><PERSON><PERSON> tra các title đã có, tr<PERSON><PERSON> bị duplicate
5. <PERSON><PERSON><PERSON> back phải nằm cùng title, tránh nằm riêng
### Ví dụ code hiện tại (BlogTopBar.tsx:134-142):
```tsx
<div className="">
  <ArrowLeftOutlined
    className="cursor-pointer"
    onClick={() => navigate('/blog')}
  />
  <div className="text-xl font-bold">
    {TITLE_HERE}
  </div>
</div>
```
