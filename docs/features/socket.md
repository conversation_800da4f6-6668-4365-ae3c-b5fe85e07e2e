# Thiết kế Hệ thống WebSocket cho Blog UI V1

## 🏗️ 1. Kiến trúc tổng quan

```
┌─────────────────────────────────────────────────────────────────────┐
│                          Frontend Layer                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────────┐ │
│  │ Blog Editor     │ │ Notification    │ │ Real-time Dashboard     │ │
│  │ (Collaboration) │ │ Center          │ │ (Analytics)             │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │            WebSocket Client Service                             │ │
│  │  • Connection Management  • Event Routing  • State Sync        │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────┬───────────────────────────────────────┘
                              │ WebSocket Connection
                              ▼
┌─────────────────────────────────────────────────────────────────────┐
│                      WebSocket Gateway                              │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │               Multi-Tenant Router                              │ │
│  │  /ws/:tenant_id  →  Tenant-specific namespace                  │ │
│  └─────────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │                Event Distribution Hub                          │ │
│  │  • Room Management        • Message Broadcasting              │ │
│  │  • Authentication         • Rate Limiting                     │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────┬───────────────────────────────────────┘
                              │ Event Publishing
                              ▼
┌─────────────────────────────────────────────────────────────────────┐
│                     Backend Services                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐ │
│  │ Blog        │ │ Notification│ │ Auth        │ │ Real-time       │ │
│  │ Service     │ │ Service     │ │ Service     │ │ Analytics       │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

## 📁 2. Cấu trúc thư mục

```
src/
├── websocket/
│   ├── client/
│   │   ├── websocket-client.ts         # Core WebSocket client
│   │   ├── websocket-context.tsx       # React Context Provider
│   │   ├── websocket-hook.ts           # Custom React hooks
│   │   ├── types.ts                    # TypeScript types
│   │   └── constants.ts                # WebSocket constants
│   ├── events/
│   │   ├── blog-events.ts              # Blog-related events
│   │   ├── notification-events.ts      # Notification events
│   │   ├── collaboration-events.ts     # Real-time collaboration
│   │   ├── system-events.ts            # System events
│   │   └── index.ts                    # Event exports
│   ├── handlers/
│   │   ├── blog-handler.ts             # Blog event handlers
│   │   ├── notification-handler.ts     # Notification handlers
│   │   ├── collaboration-handler.ts    # Collaboration handlers
│   │   └── system-handler.ts           # System event handlers
│   ├── services/
│   │   ├── event-service.ts            # Event management
│   │   ├── room-service.ts             # Room management
│   │   ├── subscription-service.ts     # Subscription management
│   │   └── sync-service.ts             # State synchronization
│   ├── stores/
│   │   ├── websocket-store.ts          # Zustand WebSocket store
│   │   ├── collaboration-store.ts      # Collaboration state
│   │   └── realtime-store.ts           # Real-time data store
│   └── components/
│       ├── websocket-provider.tsx      # Provider component
│       ├── connection-status.tsx       # Connection indicator
│       ├── online-users.tsx            # Online users display
│       └── realtime-cursor.tsx         # Collaboration cursors
```

## 💻 3. Core Implementation

### 3.1 WebSocket Client Service

```typescript
// src/websocket/client/websocket-client.ts
import { io, Socket } from 'socket.io-client';
import { getAuthToken } from '@/services/token.service';
import { getTenantId } from '@/services/tenant.service';
import { WebSocketEvents, WebSocketResponse, ConnectionStatus } from './types';

export class WebSocketClient {
  private socket: Socket | null = null;
  private tenantId: string | null = null;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventHandlers = new Map<string, Set<Function>>();
  private subscriptions = new Set<string>();

  constructor() {
    this.bindMethods();
  }

  private bindMethods(): void {
    this.handleConnect = this.handleConnect.bind(this);
    this.handleDisconnect = this.handleDisconnect.bind(this);
    this.handleReconnect = this.handleReconnect.bind(this);
    this.handleError = this.handleError.bind(this);
  }

  async connect(tenantId?: string): Promise<void> {
    const currentTenantId = tenantId || getTenantId();

    if (!currentTenantId) {
      throw new Error('Tenant ID is required for WebSocket connection');
    }

    if (this.socket?.connected && this.tenantId === currentTenantId) {
      return;
    }

    this.tenantId = currentTenantId;
    const token = getAuthToken();

    if (!token) {
      throw new Error('Authentication token required');
    }

    // Disconnect existing socket
    if (this.socket) {
      this.socket.disconnect();
    }

    this.connectionStatus = 'connecting';
    this.notifyStatusChange();

    this.socket = io(`${process.env.REACT_APP_WS_URL}/ws/${currentTenantId}`, {
      auth: { token },
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
    });

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', this.handleConnect);
    this.socket.on('disconnect', this.handleDisconnect);
    this.socket.on('reconnect', this.handleReconnect);
    this.socket.on('connect_error', this.handleError);

    // Application events
    this.socket.on('blog:updated', this.handleEvent);
    this.socket.on('blog:collaborator-joined', this.handleEvent);
    this.socket.on('blog:collaborator-left', this.handleEvent);
    this.socket.on('blog:cursor-moved', this.handleEvent);

    this.socket.on('notification:new', this.handleEvent);
    this.socket.on('notification:read', this.handleEvent);
    this.socket.on('notification:deleted', this.handleEvent);

    this.socket.on('system:maintenance', this.handleEvent);
    this.socket.on('system:broadcast', this.handleEvent);
  }

  private handleConnect = (): void => {
    console.log('✅ WebSocket connected to tenant:', this.tenantId);
    this.connectionStatus = 'connected';
    this.reconnectAttempts = 0;
    this.notifyStatusChange();

    // Re-subscribe to previous subscriptions
    this.resubscribe();
  };

  private handleDisconnect = (reason: string): void => {
    console.log('❌ WebSocket disconnected:', reason);
    this.connectionStatus = 'disconnected';
    this.notifyStatusChange();

    if (reason === 'io server disconnect') {
      // Server initiated disconnect, don't reconnect automatically
      return;
    }

    this.attemptReconnect();
  };

  private handleReconnect = (): void => {
    console.log('🔄 WebSocket reconnected');
    this.connectionStatus = 'connected';
    this.reconnectAttempts = 0;
    this.notifyStatusChange();
  };

  private handleError = (error: Error): void => {
    console.error('💥 WebSocket error:', error);
    this.connectionStatus = 'error';
    this.notifyStatusChange();
  };

  private handleEvent = (eventName: string, data: any): void => {
    const handlers = this.eventHandlers.get(eventName);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${eventName}:`, error);
        }
      });
    }
  };

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.connectionStatus = 'failed';
      this.notifyStatusChange();
      return;
    }

    this.reconnectAttempts++;
    this.connectionStatus = 'reconnecting';
    this.notifyStatusChange();

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    setTimeout(() => {
      if (this.socket && !this.socket.connected) {
        this.socket.connect();
      }
    }, delay);
  }

  private resubscribe(): void {
    this.subscriptions.forEach((subscription) => {
      this.socket?.emit('subscribe', { room: subscription });
    });
  }

  private notifyStatusChange(): void {
    this.emit('connection:status-changed', {
      status: this.connectionStatus,
      tenantId: this.tenantId,
    });
  }

  // Public API
  on(eventName: string, handler: Function): () => void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Set());
    }

    this.eventHandlers.get(eventName)!.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(eventName);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.eventHandlers.delete(eventName);
        }
      }
    };
  }

  emit(eventName: string, data: any): void {
    if (this.socket?.connected) {
      this.socket.emit(eventName, data);
    } else {
      console.warn('Cannot emit event: WebSocket not connected');
    }
  }

  subscribe(room: string): void {
    if (this.subscriptions.has(room)) {
      return;
    }

    this.subscriptions.add(room);

    if (this.socket?.connected) {
      this.socket.emit('subscribe', { room });
    }
  }

  unsubscribe(room: string): void {
    this.subscriptions.delete(room);

    if (this.socket?.connected) {
      this.socket.emit('unsubscribe', { room });
    }
  }

  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  isConnected(): boolean {
    return this.connectionStatus === 'connected';
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.connectionStatus = 'disconnected';
    this.notifyStatusChange();
  }
}

// Singleton instance
export const webSocketClient = new WebSocketClient();
```

### 3.2 TypeScript Types

```typescript
// src/websocket/client/types.ts
export type ConnectionStatus =
  | 'disconnected'
  | 'connecting'
  | 'connected'
  | 'reconnecting'
  | 'error'
  | 'failed';

export interface WebSocketConfig {
  url: string;
  tenantId: string;
  token: string;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
}

export interface WebSocketState {
  status: ConnectionStatus;
  tenantId: string | null;
  connectedAt: Date | null;
  lastError: Error | null;
  reconnectAttempts: number;
}

// Blog Events
export interface BlogCollaborationEvent {
  type: 'blog:collaboration';
  data: {
    blogId: string;
    userId: string;
    action: 'join' | 'leave' | 'edit' | 'cursor-move';
    position?: number;
    content?: string;
    cursor?: CursorPosition;
  };
}

export interface CursorPosition {
  userId: string;
  userName: string;
  position: number;
  selection?: {
    start: number;
    end: number;
  };
  color: string;
}

export interface BlogUpdateEvent {
  type: 'blog:updated';
  data: {
    blogId: string;
    updates: {
      title?: string;
      content?: string;
      status?: string;
      updatedBy: string;
      updatedAt: string;
    };
    operation: 'insert' | 'delete' | 'replace';
    position?: number;
    length?: number;
  };
}

// Notification Events
export interface NotificationEvent {
  type: 'notification:new' | 'notification:read' | 'notification:deleted';
  data: {
    id: string;
    userId: string;
    title: string;
    message: string;
    type: 'info' | 'success' | 'warning' | 'error';
    createdAt: string;
    readAt?: string;
    data?: Record<string, any>;
  };
}

// System Events
export interface SystemEvent {
  type: 'system:maintenance' | 'system:broadcast';
  data: {
    message: string;
    severity: 'info' | 'warning' | 'critical';
    scheduledAt?: string;
    duration?: number;
    affectedServices?: string[];
  };
}

export type WebSocketEvent =
  | BlogCollaborationEvent
  | BlogUpdateEvent
  | NotificationEvent
  | SystemEvent;
```

### 3.3 React Context & Hooks

```typescript
// src/websocket/client/websocket-context.tsx
import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { webSocketClient } from './websocket-client';
import { ConnectionStatus, WebSocketState } from './types';
import { useTenantId } from '@/hooks/useTenantId';

interface WebSocketContextValue {
  state: WebSocketState;
  connect: () => Promise<void>;
  disconnect: () => void;
  subscribe: (room: string) => void;
  unsubscribe: (room: string) => void;
  emit: (eventName: string, data: any) => void;
  on: (eventName: string, handler: Function) => () => void;
}

const WebSocketContext = createContext<WebSocketContextValue | null>(null);

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const tenantId = useTenantId();
  const [state, setState] = useState<WebSocketState>({
    status: 'disconnected',
    tenantId: null,
    connectedAt: null,
    lastError: null,
    reconnectAttempts: 0,
  });

  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Listen to connection status changes
    const unsubscribe = webSocketClient.on('connection:status-changed', (data: any) => {
      setState(prev => ({
        ...prev,
        status: data.status,
        tenantId: data.tenantId,
        connectedAt: data.status === 'connected' ? new Date() : prev.connectedAt,
      }));
    });

    unsubscribeRef.current = unsubscribe;

    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    // Auto-connect when tenant ID is available
    if (tenantId && state.status === 'disconnected') {
      webSocketClient.connect(tenantId);
    }
  }, [tenantId, state.status]);

  const connect = async (): Promise<void> => {
    if (tenantId) {
      await webSocketClient.connect(tenantId);
    }
  };

  const disconnect = (): void => {
    webSocketClient.disconnect();
  };

  const subscribe = (room: string): void => {
    webSocketClient.subscribe(room);
  };

  const unsubscribe = (room: string): void => {
    webSocketClient.unsubscribe(room);
  };

  const emit = (eventName: string, data: any): void => {
    webSocketClient.emit(eventName, data);
  };

  const on = (eventName: string, handler: Function): (() => void) => {
    return webSocketClient.on(eventName, handler);
  };

  const contextValue: WebSocketContextValue = {
    state,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    emit,
    on,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = (): WebSocketContextValue => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};
```

### 3.4 Custom React Hooks

```typescript
// src/websocket/client/websocket-hook.ts
import { useEffect, useRef, useCallback } from 'react';
import { useWebSocket } from './websocket-context';

// Generic event listener hook
export const useWebSocketEvent = <T = any>(
  eventName: string,
  handler: (data: T) => void,
  deps: React.DependencyList = [],
): void => {
  const { on } = useWebSocket();
  const handlerRef = useRef(handler);

  // Update handler ref when dependencies change
  useEffect(() => {
    handlerRef.current = handler;
  }, deps);

  useEffect(() => {
    const unsubscribe = on(eventName, (data: T) => {
      handlerRef.current(data);
    });

    return unsubscribe;
  }, [eventName, on]);
};

// Blog collaboration hook
export const useBlogCollaboration = (blogId: string) => {
  const { subscribe, unsubscribe, emit } = useWebSocket();
  const roomName = `blog:${blogId}`;

  useEffect(() => {
    subscribe(roomName);

    return () => {
      unsubscribe(roomName);
    };
  }, [blogId, subscribe, unsubscribe, roomName]);

  const joinCollaboration = useCallback(
    (userId: string) => {
      emit('blog:join-collaboration', { blogId, userId });
    },
    [blogId, emit],
  );

  const leaveCollaboration = useCallback(
    (userId: string) => {
      emit('blog:leave-collaboration', { blogId, userId });
    },
    [blogId, emit],
  );

  const updateContent = useCallback(
    (content: string, position: number) => {
      emit('blog:content-update', {
        blogId,
        content,
        position,
        timestamp: Date.now(),
      });
    },
    [blogId, emit],
  );

  const moveCursor = useCallback(
    (position: number, selection?: { start: number; end: number }) => {
      emit('blog:cursor-move', {
        blogId,
        position,
        selection,
        timestamp: Date.now(),
      });
    },
    [blogId, emit],
  );

  return {
    joinCollaboration,
    leaveCollaboration,
    updateContent,
    moveCursor,
  };
};

// Notification hook
export const useNotifications = () => {
  const { subscribe, unsubscribe, emit } = useWebSocket();
  const roomName = 'notifications';

  useEffect(() => {
    subscribe(roomName);

    return () => {
      unsubscribe(roomName);
    };
  }, [subscribe, unsubscribe, roomName]);

  const markAsRead = useCallback(
    (notificationId: string) => {
      emit('notification:mark-read', { notificationId });
    },
    [emit],
  );

  const markAllAsRead = useCallback(() => {
    emit('notification:mark-all-read', {});
  }, [emit]);

  const deleteNotification = useCallback(
    (notificationId: string) => {
      emit('notification:delete', { notificationId });
    },
    [emit],
  );

  return {
    markAsRead,
    markAllAsRead,
    deleteNotification,
  };
};

// Connection status hook
export const useConnectionStatus = () => {
  const { state } = useWebSocket();

  return {
    status: state.status,
    isConnected: state.status === 'connected',
    isConnecting: state.status === 'connecting',
    isReconnecting: state.status === 'reconnecting',
    hasError: state.status === 'error' || state.status === 'failed',
    connectedAt: state.connectedAt,
    lastError: state.lastError,
  };
};

// Online users hook
export const useOnlineUsers = (roomId: string) => {
  const { subscribe, unsubscribe } = useWebSocket();
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);

  useEffect(() => {
    const roomName = `presence:${roomId}`;
    subscribe(roomName);

    return () => {
      unsubscribe(roomName);
    };
  }, [roomId, subscribe, unsubscribe]);

  useWebSocketEvent(
    'presence:user-joined',
    (data: { userId: string; roomId: string }) => {
      if (data.roomId === roomId) {
        setOnlineUsers((prev) => [
          ...prev.filter((id) => id !== data.userId),
          data.userId,
        ]);
      }
    },
  );

  useWebSocketEvent(
    'presence:user-left',
    (data: { userId: string; roomId: string }) => {
      if (data.roomId === roomId) {
        setOnlineUsers((prev) => prev.filter((id) => id !== data.userId));
      }
    },
  );

  useWebSocketEvent(
    'presence:users-list',
    (data: { users: string[]; roomId: string }) => {
      if (data.roomId === roomId) {
        setOnlineUsers(data.users);
      }
    },
  );

  return onlineUsers;
};
```

### 3.5 Zustand Store Integration

```typescript
// src/websocket/stores/websocket-store.ts
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  ConnectionStatus,
  NotificationEvent,
  BlogUpdateEvent,
} from '../client/types';

interface WebSocketStore {
  // Connection state
  connectionStatus: ConnectionStatus;
  connectedAt: Date | null;

  // Notifications
  notifications: NotificationEvent['data'][];
  unreadCount: number;

  // Blog collaboration
  activeBlogSessions: Map<string, string[]>; // blogId -> userIds
  blogCursors: Map<string, CursorPosition[]>; // blogId -> cursors

  // Actions
  setConnectionStatus: (status: ConnectionStatus) => void;
  setConnectedAt: (date: Date | null) => void;
  addNotification: (notification: NotificationEvent['data']) => void;
  markNotificationAsRead: (notificationId: string) => void;
  removeNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  updateBlogSession: (blogId: string, userIds: string[]) => void;
  updateBlogCursors: (blogId: string, cursors: CursorPosition[]) => void;
}

export const useWebSocketStore = create<WebSocketStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    connectionStatus: 'disconnected',
    connectedAt: null,
    notifications: [],
    unreadCount: 0,
    activeBlogSessions: new Map(),
    blogCursors: new Map(),

    // Actions
    setConnectionStatus: (status) => {
      set({ connectionStatus: status });
    },

    setConnectedAt: (date) => {
      set({ connectedAt: date });
    },

    addNotification: (notification) => {
      set((state) => ({
        notifications: [notification, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      }));
    },

    markNotificationAsRead: (notificationId) => {
      set((state) => ({
        notifications: state.notifications.map((n) =>
          n.id === notificationId
            ? { ...n, readAt: new Date().toISOString() }
            : n,
        ),
        unreadCount: Math.max(0, state.unreadCount - 1),
      }));
    },

    removeNotification: (notificationId) => {
      set((state) => {
        const notification = state.notifications.find(
          (n) => n.id === notificationId,
        );
        const wasUnread = notification && !notification.readAt;

        return {
          notifications: state.notifications.filter(
            (n) => n.id !== notificationId,
          ),
          unreadCount: wasUnread
            ? Math.max(0, state.unreadCount - 1)
            : state.unreadCount,
        };
      });
    },

    clearAllNotifications: () => {
      set({ notifications: [], unreadCount: 0 });
    },

    updateBlogSession: (blogId, userIds) => {
      set((state) => {
        const newSessions = new Map(state.activeBlogSessions);
        newSessions.set(blogId, userIds);
        return { activeBlogSessions: newSessions };
      });
    },

    updateBlogCursors: (blogId, cursors) => {
      set((state) => {
        const newCursors = new Map(state.blogCursors);
        newCursors.set(blogId, cursors);
        return { blogCursors: newCursors };
      });
    },
  })),
);

// Selectors
export const useConnectionStatus = () =>
  useWebSocketStore((state) => state.connectionStatus);
export const useNotifications = () =>
  useWebSocketStore((state) => state.notifications);
export const useUnreadCount = () =>
  useWebSocketStore((state) => state.unreadCount);
export const useBlogSession = (blogId: string) =>
  useWebSocketStore((state) => state.activeBlogSessions.get(blogId) || []);
export const useBlogCursors = (blogId: string) =>
  useWebSocketStore((state) => state.blogCursors.get(blogId) || []);
```

## 🎨 4. React Components

### 4.1 WebSocket Provider

```typescript
// src/websocket/components/websocket-provider.tsx
import React, { useEffect } from 'react';
import { WebSocketProvider as Provider, useWebSocket } from '../client/websocket-context';
import { useWebSocketStore } from '../stores/websocket-store';
import { useWebSocketEvent } from '../client/websocket-hook';
import { NotificationEvent, BlogCollaborationEvent, SystemEvent } from '../client/types';

const WebSocketEventHandler: React.FC = () => {
  const {
    setConnectionStatus,
    setConnectedAt,
    addNotification,
    markNotificationAsRead,
    removeNotification,
    updateBlogSession,
    updateBlogCursors,
  } = useWebSocketStore();

  // Connection status events
  useWebSocketEvent('connection:status-changed', (data: { status: string }) => {
    setConnectionStatus(data.status as any);
    if (data.status === 'connected') {
      setConnectedAt(new Date());
    }
  });

  // Notification events
  useWebSocketEvent<NotificationEvent['data']>('notification:new', (notification) => {
    addNotification(notification);

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
      });
    }
  });

  useWebSocketEvent<{ notificationId: string }>('notification:read', (data) => {
    markNotificationAsRead(data.notificationId);
  });

  useWebSocketEvent<{ notificationId: string }>('notification:deleted', (data) => {
    removeNotification(data.notificationId);
  });

  // Blog collaboration events
  useWebSocketEvent<BlogCollaborationEvent['data']>('blog:collaborator-joined', (data) => {
    // Handle user joining blog session
    console.log('User joined blog collaboration:', data);
  });

  useWebSocketEvent<BlogCollaborationEvent['data']>('blog:collaborator-left', (data) => {
    // Handle user leaving blog session
    console.log('User left blog collaboration:', data);
  });

  useWebSocketEvent<BlogCollaborationEvent['data']>('blog:cursor-moved', (data) => {
    if (data.cursor) {
      // Update cursor positions for blog
      console.log('Cursor moved:', data);
    }
  });

  // System events
  useWebSocketEvent<SystemEvent['data']>('system:maintenance', (data) => {
    // Show system maintenance notification
    console.warn('System maintenance:', data);
  });

  return null;
};

export const WebSocketAppProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  useEffect(() => {
    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  return (
    <Provider>
      <WebSocketEventHandler />
      {children}
    </Provider>
  );
};
```

### 4.2 Connection Status Component

```typescript
// src/websocket/components/connection-status.tsx
import React from 'react';
import { Badge, Tooltip } from 'antd';
import { WifiOutlined, DisconnectOutlined, LoadingOutlined } from '@ant-design/icons';
import { useConnectionStatus } from '../client/websocket-hook';

export const ConnectionStatus: React.FC = () => {
  const { status, isConnected, isConnecting, isReconnecting, connectedAt } = useConnectionStatus();

  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          status: 'success' as const,
          icon: <WifiOutlined />,
          text: 'Connected',
          color: '#52c41a',
        };
      case 'connecting':
      case 'reconnecting':
        return {
          status: 'processing' as const,
          icon: <LoadingOutlined spin />,
          text: isReconnecting ? 'Reconnecting...' : 'Connecting...',
          color: '#1890ff',
        };
      case 'disconnected':
      case 'error':
      case 'failed':
        return {
          status: 'error' as const,
          icon: <DisconnectOutlined />,
          text: 'Disconnected',
          color: '#ff4d4f',
        };
      default:
        return {
          status: 'default' as const,
          icon: <DisconnectOutlined />,
          text: 'Unknown',
          color: '#d9d9d9',
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Tooltip
      title={
        <div>
          <div>Status: {config.text}</div>
          {connectedAt && <div>Connected at: {connectedAt.toLocaleTimeString()}</div>}
        </div>
      }
    >
      <Badge
        status={config.status}
        text={
          <span style={{ color: config.color, display: 'flex', alignItems: 'center', gap: 4 }}>
            {config.icon}
            {config.text}
          </span>
        }
      />
    </Tooltip>
  );
};
```

### 4.3 Online Users Component

```typescript
// src/websocket/components/online-users.tsx
import React from 'react';
import { Avatar, Tooltip, Space } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { useOnlineUsers } from '../client/websocket-hook';

interface OnlineUsersProps {
  roomId: string;
  maxDisplay?: number;
}

export const OnlineUsers: React.FC<OnlineUsersProps> = ({
  roomId,
  maxDisplay = 5
}) => {
  const onlineUsers = useOnlineUsers(roomId);

  if (onlineUsers.length === 0) {
    return null;
  }

  const displayUsers = onlineUsers.slice(0, maxDisplay);
  const remainingCount = onlineUsers.length - maxDisplay;

  return (
    <Space size="small">
      <span style={{ fontSize: '12px', color: '#666' }}>
        Online ({onlineUsers.length}):
      </span>

      <Avatar.Group maxCount={maxDisplay} size="small">
        {displayUsers.map((userId, index) => (
          <Tooltip key={userId} title={`User ${userId}`}>
            <Avatar
              size="small"
              icon={<UserOutlined />}
              style={{ backgroundColor: `hsl(${index * 137.5 % 360}, 70%, 50%)` }}
            />
          </Tooltip>
        ))}

        {remainingCount > 0 && (
          <Tooltip title={`${remainingCount} more users online`}>
            <Avatar size="small" style={{ backgroundColor: '#f56a00' }}>
              +{remainingCount}
            </Avatar>
          </Tooltip>
        )}
      </Avatar.Group>
    </Space>
  );
};
```

### 4.4 Real-time Collaboration Cursors

```typescript
// src/websocket/components/realtime-cursor.tsx
import React, { useState, useEffect } from 'react';
import { useBlogCursors } from '../stores/websocket-store';
import { CursorPosition } from '../client/types';

interface RealtimeCursorsProps {
  blogId: string;
  editorRef: React.RefObject<HTMLElement>;
}

export const RealtimeCursors: React.FC<RealtimeCursorsProps> = ({
  blogId,
  editorRef,
}) => {
  const cursors = useBlogCursors(blogId);
  const [cursorElements, setCursorElements] = useState<
    Map<string, HTMLElement>
  >(new Map());

  useEffect(() => {
    if (!editorRef.current) return;

    const editor = editorRef.current;
    const newElements = new Map<string, HTMLElement>();

    // Remove old cursor elements
    cursorElements.forEach((element) => {
      element.remove();
    });

    // Create new cursor elements
    cursors.forEach((cursor: CursorPosition) => {
      const cursorElement = createCursorElement(cursor);

      // Position cursor based on text position
      const textNode = getTextNodeAtPosition(editor, cursor.position);
      if (textNode) {
        const range = document.createRange();
        range.setStart(textNode.node, textNode.offset);
        range.setEnd(textNode.node, textNode.offset);

        const rect = range.getBoundingClientRect();
        const editorRect = editor.getBoundingClientRect();

        cursorElement.style.left = `${rect.left - editorRect.left}px`;
        cursorElement.style.top = `${rect.top - editorRect.top}px`;

        editor.appendChild(cursorElement);
        newElements.set(cursor.userId, cursorElement);
      }
    });

    setCursorElements(newElements);

    return () => {
      newElements.forEach((element) => element.remove());
    };
  }, [cursors, editorRef]);

  return null; // Cursors are rendered directly in the DOM
};

const createCursorElement = (cursor: CursorPosition): HTMLElement => {
  const element = document.createElement('div');
  element.className = 'realtime-cursor';
  element.style.cssText = `
    position: absolute;
    width: 2px;
    height: 20px;
    background-color: ${cursor.color};
    pointer-events: none;
    z-index: 1000;
    transition: all 0.1s ease;
  `;

  // Add user label
  const label = document.createElement('div');
  label.textContent = cursor.userName;
  label.style.cssText = `
    position: absolute;
    top: -25px;
    left: 0;
    background-color: ${cursor.color};
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    font-family: system-ui, -apple-system, sans-serif;
  `;

  element.appendChild(label);
  return element;
};

const getTextNodeAtPosition = (
  element: HTMLElement,
  position: number,
): { node: Text; offset: number } | null => {
  const walker = document.createTreeWalker(
    element,
    NodeFilter.SHOW_TEXT,
    null,
    false,
  );

  let currentPosition = 0;
  let node = walker.nextNode() as Text;

  while (node) {
    const nodeLength = node.textContent?.length || 0;

    if (currentPosition + nodeLength >= position) {
      return {
        node,
        offset: position - currentPosition,
      };
    }

    currentPosition += nodeLength;
    node = walker.nextNode() as Text;
  }

  return null;
};
```

## 🔧 5. Integration Examples

### 5.1 Blog Editor Integration

```typescript
// src/pages/blog/form/form.tsx (Updated)
import React, { useRef, useEffect } from 'react';
import { Form, Input, Button } from 'antd';
import { useBlogCollaboration, useWebSocketEvent } from '@/websocket/client/websocket-hook';
import { RealtimeCursors } from '@/websocket/components/realtime-cursor';
import { OnlineUsers } from '@/websocket/components/online-users';
import { BlogUpdateEvent } from '@/websocket/client/types';

interface BlogFormProps {
  blogId?: string;
  initialData?: any;
}

export const BlogForm: React.FC<BlogFormProps> = ({ blogId, initialData }) => {
  const [form] = Form.useForm();
  const editorRef = useRef<HTMLTextAreaElement>(null);
  const lastUpdateRef = useRef<number>(0);

  const {
    joinCollaboration,
    leaveCollaboration,
    updateContent,
    moveCursor,
  } = useBlogCollaboration(blogId || '');

  // Join collaboration when component mounts
  useEffect(() => {
    if (blogId) {
      joinCollaboration('current-user-id');

      return () => {
        leaveCollaboration('current-user-id');
      };
    }
  }, [blogId, joinCollaboration, leaveCollaboration]);

  // Listen for content updates from other users
  useWebSocketEvent<BlogUpdateEvent['data']>('blog:updated', (data) => {
    if (data.blogId === blogId && data.updatedBy !== 'current-user-id') {
      // Apply remote changes to editor
      applyRemoteUpdate(data);
    }
  });

  // Handle local content changes
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value;
    const position = e.target.selectionStart;

    // Debounce updates
    const now = Date.now();
    if (now - lastUpdateRef.current > 300) {
      updateContent(content, position);
      lastUpdateRef.current = now;
    }
  };

  // Handle cursor movement
  const handleSelectionChange = () => {
    if (editorRef.current) {
      const position = editorRef.current.selectionStart;
      const selectionEnd = editorRef.current.selectionEnd;

      moveCursor(position, position !== selectionEnd ? {
        start: position,
        end: selectionEnd,
      } : undefined);
    }
  };

  const applyRemoteUpdate = (update: BlogUpdateEvent['data']) => {
    if (!editorRef.current) return;

    const textarea = editorRef.current;
    const currentValue = textarea.value;

    // Apply the update based on operation type
    switch (update.operation) {
      case 'insert':
        if (update.position !== undefined) {
          const newValue =
            currentValue.slice(0, update.position) +
            update.updates.content +
            currentValue.slice(update.position);

          form.setFieldValue('content', newValue);
        }
        break;

      case 'delete':
        if (update.position !== undefined && update.length !== undefined) {
          const newValue =
            currentValue.slice(0, update.position) +
            currentValue.slice(update.position + update.length);

          form.setFieldValue('content', newValue);
        }
        break;

      case 'replace':
        form.setFieldValue('content', update.updates.content);
        break;
    }
  };

  return (
    <div style={{ position: 'relative' }}>
      {blogId && (
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <OnlineUsers roomId={`blog:${blogId}`} />
        </div>
      )}

      <Form form={form} layout="vertical" initialValues={initialData}>
        <Form.Item
          name="title"
          label="Title"
          rules={[{ required: true, message: 'Please enter title' }]}
        >
          <Input placeholder="Enter blog title" />
        </Form.Item>

        <Form.Item
          name="content"
          label="Content"
          rules={[{ required: true, message: 'Please enter content' }]}
        >
          <div style={{ position: 'relative' }}>
            <Input.TextArea
              ref={editorRef}
              rows={20}
              placeholder="Start writing your blog post..."
              onChange={handleContentChange}
              onSelect={handleSelectionChange}
              onKeyUp={handleSelectionChange}
              onMouseUp={handleSelectionChange}
            />

            {blogId && (
              <RealtimeCursors
                blogId={blogId}
                editorRef={editorRef as any}
              />
            )}
          </div>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit">
            Save Blog Post
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
```

### 5.2 Notification Center Integration

```typescript
// src/components/notification/notification-center.tsx (Updated)
import React, { useEffect } from 'react';
import { List, Badge, Button, Empty, Spin } from 'antd';
import { BellOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNotifications } from '@/websocket/client/websocket-hook';
import { useNotifications as useNotificationStore, useUnreadCount } from '@/websocket/stores/websocket-store';

export const NotificationCenter: React.FC = () => {
  const notifications = useNotificationStore();
  const unreadCount = useUnreadCount();
  const { markAsRead, markAllAsRead, deleteNotification } = useNotifications();

  const handleNotificationClick = (notificationId: string, isRead: boolean) => {
    if (!isRead) {
      markAsRead(notificationId);
    }
  };

  const handleDeleteNotification = (notificationId: string) => {
    deleteNotification(notificationId);
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead();
  };

  return (
    <div style={{ width: 400, maxHeight: 500, overflow: 'auto' }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '12px 16px',
        borderBottom: '1px solid #f0f0f0'
      }}>
        <span style={{ fontWeight: 600 }}>
          <BellOutlined /> Notifications
          {unreadCount > 0 && (
            <Badge count={unreadCount} style={{ marginLeft: 8 }} />
          )}
        </span>

        {unreadCount > 0 && (
          <Button size="small" type="link" onClick={handleMarkAllAsRead}>
            Mark all as read
          </Button>
        )}
      </div>

      {notifications.length === 0 ? (
        <Empty
          description="No notifications"
          style={{ padding: '40px 20px' }}
        />
      ) : (
        <List
          dataSource={notifications}
          renderItem={(notification) => {
            const isUnread = !notification.readAt;

            return (
              <List.Item
                style={{
                  backgroundColor: isUnread ? '#f6ffed' : 'transparent',
                  cursor: 'pointer',
                  borderLeft: isUnread ? '3px solid #52c41a' : '3px solid transparent',
                }}
                onClick={() => handleNotificationClick(notification.id, !isUnread)}
                actions={[
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteNotification(notification.id);
                    }}
                  />
                ]}
              >
                <List.Item.Meta
                  title={
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span style={{ fontWeight: isUnread ? 600 : 400 }}>
                        {notification.title}
                      </span>
                      {isUnread && (
                        <Badge status="processing" />
                      )}
                    </div>
                  }
                  description={
                    <div>
                      <div style={{ marginBottom: 4 }}>
                        {notification.message}
                      </div>
                      <div style={{ fontSize: '12px', color: '#999' }}>
                        {new Date(notification.createdAt).toLocaleString()}
                      </div>
                    </div>
                  }
                />
              </List.Item>
            );
          }}
        />
      )}
    </div>
  );
};
```

### 5.3 App.tsx Integration

```typescript
// src/App.tsx (Updated)
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { WebSocketAppProvider } from '@/websocket/components/websocket-provider';
import { ConnectionStatus } from '@/websocket/components/connection-status';
import AppRouter from '@/router';

const App: React.FC = () => {
  return (
    <ConfigProvider>
      <BrowserRouter>
        <WebSocketAppProvider>
          <div className="app">
            {/* Connection status in header */}
            <div style={{
              position: 'fixed',
              top: 10,
              right: 10,
              zIndex: 1000
            }}>
              <ConnectionStatus />
            </div>

            <AppRouter />
          </div>
        </WebSocketAppProvider>
      </BrowserRouter>
    </ConfigProvider>
  );
};

export default App;
```

## 🚀 6. Performance & Optimization

### 6.1 Connection Management

```typescript
// src/websocket/services/connection-manager.ts
export class ConnectionManager {
  private connections = new Map<string, WebSocketClient>();
  private heartbeatInterval: NodeJS.Timeout | null = null;

  startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.connections.forEach((client, tenantId) => {
        if (client.isConnected()) {
          client.emit('ping', { timestamp: Date.now() });
        }
      });
    }, 30000); // Ping every 30 seconds
  }

  stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  getOrCreateConnection(tenantId: string): WebSocketClient {
    if (!this.connections.has(tenantId)) {
      const client = new WebSocketClient();
      this.connections.set(tenantId, client);
    }

    return this.connections.get(tenantId)!;
  }

  closeConnection(tenantId: string): void {
    const client = this.connections.get(tenantId);
    if (client) {
      client.disconnect();
      this.connections.delete(tenantId);
    }
  }

  closeAllConnections(): void {
    this.connections.forEach((client) => client.disconnect());
    this.connections.clear();
    this.stopHeartbeat();
  }
}

export const connectionManager = new ConnectionManager();
```

### 6.2 Event Debouncing

```typescript
// src/websocket/utils/debounce.ts
export class EventDebouncer {
  private timers = new Map<string, NodeJS.Timeout>();

  debounce<T extends any[]>(
    key: string,
    fn: (...args: T) => void,
    delay: number,
  ): (...args: T) => void {
    return (...args: T) => {
      const existingTimer = this.timers.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      const timer = setTimeout(() => {
        fn(...args);
        this.timers.delete(key);
      }, delay);

      this.timers.set(key, timer);
    };
  }

  cancel(key: string): void {
    const timer = this.timers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(key);
    }
  }

  cancelAll(): void {
    this.timers.forEach((timer) => clearTimeout(timer));
    this.timers.clear();
  }
}

export const eventDebouncer = new EventDebouncer();
```

## 🔐 7. Security Considerations

### 7.1 Authentication Middleware

```typescript
// src/websocket/middleware/auth-middleware.ts
import { getAuthToken, validateToken } from '@/services/token.service';

export const authenticateSocket = async (
  socket: any,
  next: (err?: Error) => void,
): Promise<void> => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.query.token;

    if (!token) {
      return next(new Error('Authentication token required'));
    }

    const decoded = await validateToken(token);

    if (!decoded || !decoded.userId) {
      return next(new Error('Invalid authentication token'));
    }

    // Attach user info to socket
    socket.userId = decoded.userId;
    socket.tenantId = decoded.tenantId;
    socket.websiteId = decoded.websiteId;

    next();
  } catch (error) {
    next(new Error('Authentication failed'));
  }
};
```

### 7.2 Rate Limiting

```typescript
// src/websocket/middleware/rate-limit-middleware.ts
export class RateLimiter {
  private userRequests = new Map<
    string,
    { count: number; resetTime: number }
  >();
  private maxRequests = 100; // per minute
  private windowMs = 60000; // 1 minute

  isAllowed(userId: string): boolean {
    const now = Date.now();
    const userRequest = this.userRequests.get(userId);

    if (!userRequest || now > userRequest.resetTime) {
      this.userRequests.set(userId, {
        count: 1,
        resetTime: now + this.windowMs,
      });
      return true;
    }

    if (userRequest.count >= this.maxRequests) {
      return false;
    }

    userRequest.count++;
    return true;
  }

  cleanup(): void {
    const now = Date.now();
    for (const [userId, request] of this.userRequests.entries()) {
      if (now > request.resetTime) {
        this.userRequests.delete(userId);
      }
    }
  }
}

export const rateLimiter = new RateLimiter();

// Cleanup old entries every 5 minutes
setInterval(() => rateLimiter.cleanup(), 5 * 60 * 1000);
```

## 📋 8. Implementation Roadmap

### Phase 1: Core Infrastructure (Week 1-2)

- [ ] WebSocket client service
- [ ] React context and hooks
- [ ] Basic connection management
- [ ] Authentication integration
- [ ] Connection status UI

### Phase 2: Notification System (Week 3)

- [ ] Notification WebSocket events
- [ ] Zustand store integration
- [ ] Notification center UI
- [ ] Browser notification support
- [ ] Mark as read functionality

### Phase 3: Blog Collaboration (Week 4-5)

- [ ] Real-time blog editing
- [ ] Cursor synchronization
- [ ] Online users display
- [ ] Conflict resolution
- [ ] Collaborative cursors UI

### Phase 4: Performance & Polish (Week 6)

- [ ] Connection pooling
- [ ] Event debouncing
- [ ] Rate limiting
- [ ] Error handling improvements
- [ ] Performance monitoring

### Phase 5: Advanced Features (Week 7-8)

- [ ] System maintenance notifications
- [ ] Analytics real-time updates
- [ ] Advanced collaboration features
- [ ] Mobile optimization
- [ ] Offline support

## 🧪 9. Testing Strategy

### 9.1 Unit Tests

```typescript
// src/websocket/client/__tests__/websocket-client.test.ts
import { WebSocketClient } from '../websocket-client';

describe('WebSocketClient', () => {
  let client: WebSocketClient;

  beforeEach(() => {
    client = new WebSocketClient();
  });

  afterEach(() => {
    client.disconnect();
  });

  test('should connect with valid tenant ID and token', async () => {
    // Mock authentication
    jest
      .spyOn(require('@/services/token.service'), 'getAuthToken')
      .mockReturnValue('valid-token');

    await client.connect('tenant-123');

    expect(client.getConnectionStatus()).toBe('connecting');
  });

  test('should handle connection errors gracefully', async () => {
    // Mock authentication failure
    jest
      .spyOn(require('@/services/token.service'), 'getAuthToken')
      .mockReturnValue(null);

    await expect(client.connect('tenant-123')).rejects.toThrow();
  });
});
```

### 9.2 Integration Tests

```typescript
// src/websocket/__tests__/notification-integration.test.tsx
import { render, screen } from '@testing-library/react';
import { WebSocketAppProvider } from '../components/websocket-provider';
import { NotificationCenter } from '@/components/notification/notification-center';

describe('Notification Integration', () => {
  test('should display notifications in real-time', async () => {
    render(
      <WebSocketAppProvider>
        <NotificationCenter />
      </WebSocketAppProvider>
    );

    // Simulate receiving a notification
    // Test implementation here
  });
});
```

## 🏁 Conclusion

Hệ thống WebSocket này cung cấp:

✅ **Real-time Communication**: Blog collaboration, notifications, system updates
