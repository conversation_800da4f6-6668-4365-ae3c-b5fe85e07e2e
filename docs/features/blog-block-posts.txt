
Mỗi block là 1 tab
<PERSON><PERSON> sách các bài post có thể kéo được (draggable)
Block/khu vực để thả bài post (drop zones)
Tương tác kéo thả mượt mà với feedback trực quan

Chức năng cần thiết:

Drag & Drop API để xử lý việc kéo thả
Visual feedback khi đang kéo (highlight drop zones)
Cập nhật giao diện sau khi thả thành công
Có thể xóa post đã kéo

Thiết kế giao diện:

Sidebar chứa danh sách posts, co thể tìm kiếm hoặc lọc
Styling rõ ràng để phân biệt posts và drop zones
