# Tài Liệu API Module Onboarding

## Tổng Quan

Module Onboarding cung cấp quy trình hướng dẫn người dùng hoàn chỉnh để tạo tenant (tổ chức) và website trong hệ thống blog đa tenant. Module này xử lý từng bước thiết lập tổ chức mới và website đầu tiên của họ.

## Base URL

```
http://localhost:9033/api/admin/v1/onboarding
```

## Xác Thực

Tất cả các endpoint yêu cầu xác thực JWT qua Bearer token trong header Authorization:

```
Authorization: Bearer <your_jwt_token>
```

## Headers Đa Tenant

Đối với các endpoint yêu cầu ngữ cảnh tenant, bao gồm các headers này:

```
X-Tenant-ID: <tenant_id>
X-Website-ID: <website_id>  // Tùy chọn cho một số endpoint
```

## 🚀 Quy Trình Onboarding Hoàn Chỉnh

Quy trình onboarding được khu<PERSON>ến nghị cho việc triển khai UI:

### Bước 1: Đăng Ký/Đăng Nhập Người Dùng
Đầu tiên, đảm bảo người dùng đã được xác thực và có JWT token hợp lệ.

**API:** `POST /api/admin/v1/auth/login`

```bash
curl -X POST http://localhost:9033/api/admin/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "12345678"
  }'
```

**Mục đích:** Lấy JWT token để xác thực các API calls tiếp theo.

### Bước 2: Tạo Mã Tenant (Tùy Chọn)
Giúp người dùng tạo mã tenant duy nhất dựa trên tên công ty:

**API:** `GET /generate-tenant-code?tenant_name=My Company`

```bash
curl -X GET "http://localhost:9033/api/admin/v1/onboarding/generate-tenant-code?tenant_name=TechBlog" \
  -H "Authorization: Bearer <token>"
```

**Mục đích:** Tạo và kiểm tra tính khả dụng của mã tenant.

### Bước 3: Tạo Tenant
Tạo tổ chức/tenant:

**API:** `POST /tenant`

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/tenant \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_type": "company",
    "tenant_name": "TechBlog Corp",
    "company_name": "TechBlog Corporation",
    "tax_code": "*********",
    "legal_representative": "John Doe"
  }'
```

**Mục đích:** Tạo tenant mới và lấy tenant_id.

### Bước 4: Tạo Website
Tạo website đầu tiên cho tenant:

**API:** `POST /website`

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/website \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Blog",
    "subdomain": "myblog",
    "description": "My awesome blog website"
  }'
```

**Mục đích:** Tạo website đầu tiên và lấy website_id.

### Bước 5: Khởi Tạo Theo Dõi Tiến Độ Onboarding
Thiết lập theo dõi tiến độ onboarding:

**API:** `POST /initialize`

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/initialize \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>"
```

**Mục đích:** Khởi tạo các bước onboarding cho user.

### Bước 6: Theo Dõi Tiến Độ
Giám sát và cập nhật tiến độ onboarding:

**API:** `GET /status` và `POST /steps/complete`

```bash
# Kiểm tra trạng thái
curl -X GET http://localhost:9033/api/admin/v1/onboarding/status \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>"

# Hoàn thành bước
curl -X POST http://localhost:9033/api/admin/v1/onboarding/steps/complete \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "step_name": "email_verification"
  }'
```

**Mục đích:** Theo dõi và cập nhật tiến độ từng bước.

## 📋 Chi Tiết Các API Endpoints

### Kiểm Tra Sức Khỏe
- **GET** `/health`
- **Mô tả**: Kiểm tra xem module onboarding có đang chạy không
- **Xác thực**: Không yêu cầu
- **Response**: Thông tin trạng thái module

```bash
curl -X GET http://localhost:9033/api/admin/v1/onboarding/health
```

### Tạo Mã Tenant
- **GET** `/generate-tenant-code`
- **Mô tả**: Tạo mã tenant duy nhất từ tên tenant
- **Xác thực**: Yêu cầu
- **Query Parameters**:
  - `tenant_name` (string, bắt buộc): Tên để tạo mã
- **Response**: Mã tenant được tạo

```bash
curl -X GET "http://localhost:9033/api/admin/v1/onboarding/generate-tenant-code?tenant_name=TechBlog" \
  -H "Authorization: Bearer <token>"
```

### Tạo Tenant
- **POST** `/tenant`
- **Mô tả**: Tạo tenant mới (tổ chức)
- **Xác thực**: Yêu cầu
- **Body**: CreateTenantOnboardingRequest
- **Response**: Thông tin tenant đã tạo

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/tenant \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_type": "company",
    "tenant_name": "TechBlog Corp",
    "company_name": "TechBlog Corporation"
  }'
```

### Tạo Website
- **POST** `/website`
- **Mô tả**: Tạo website mới cho tenant
- **Xác thực**: Yêu cầu
- **Headers**: `X-Tenant-ID` bắt buộc
- **Body**: CreateWebsiteOnboardingRequest
- **Response**: Thông tin website đã tạo

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/website \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Blog",
    "subdomain": "myblog"
  }'
```

### Khởi Tạo Onboarding
- **POST** `/initialize`
- **Mô tả**: Khởi tạo theo dõi tiến độ onboarding
- **Xác thực**: Yêu cầu
- **Headers**: `X-Tenant-ID` và `X-Website-ID` bắt buộc
- **Response**: Trạng thái onboarding ban đầu

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/initialize \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>"
```

### Lấy Trạng Thái Onboarding
- **GET** `/status`
- **Mô tả**: Lấy tiến độ onboarding hiện tại
- **Xác thực**: Yêu cầu
- **Headers**: `X-Tenant-ID` và `X-Website-ID` bắt buộc
- **Response**: Tiến độ onboarding chi tiết

```bash
curl -X GET http://localhost:9033/api/admin/v1/onboarding/status \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>"
```

### Hoàn Thành Bước Onboarding
- **POST** `/steps/complete`
- **Mô tả**: Đánh dấu một bước onboarding đã hoàn thành
- **Xác thực**: Yêu cầu
- **Headers**: `X-Tenant-ID` và `X-Website-ID` bắt buộc
- **Body**: CompleteOnboardingStepRequest
- **Response**: Thông tin bước tiếp theo

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/steps/complete \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "step_name": "email_verification"
  }'
```

### Bỏ Qua Bước Onboarding
- **POST** `/steps/skip`
- **Mô tả**: Bỏ qua một bước onboarding
- **Xác thực**: Yêu cầu
- **Headers**: `X-Tenant-ID` và `X-Website-ID` bắt buộc
- **Body**: SkipOnboardingStepRequest
- **Response**: Thông tin bước tiếp theo

```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/steps/skip \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "step_name": "tutorial"
  }'
```

### Lấy Bước Tiếp Theo
- **GET** `/next-step`
- **Mô tả**: Lấy bước onboarding tiếp theo
- **Xác thực**: Yêu cầu
- **Headers**: `X-Tenant-ID` và `X-Website-ID` bắt buộc
- **Response**: Thông tin bước tiếp theo

```bash
curl -X GET http://localhost:9033/api/admin/v1/onboarding/next-step \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>"
```

### Lấy Templates Website
- **GET** `/templates`
- **Mô tả**: Lấy danh sách templates website có sẵn
- **Xác thực**: Yêu cầu
- **Response**: Danh sách templates website

```bash
curl -X GET http://localhost:9033/api/admin/v1/onboarding/templates \
  -H "Authorization: Bearer <token>"
```

### Lấy Gợi Ý Tên Tenant
- **GET** `/suggestions/tenant-name`
- **Mô tả**: Lấy gợi ý tên tenant dựa trên từ khóa
- **Xác thực**: Yêu cầu
- **Query Parameters**:
  - `input` (string, bắt buộc): Từ khóa để tạo gợi ý
- **Response**: Danh sách tên tenant được gợi ý

```bash
curl -X GET "http://localhost:9033/api/admin/v1/onboarding/suggestions/tenant-name?input=tech" \
  -H "Authorization: Bearer <token>"
```

## 📊 Mô Hình Dữ Liệu

### CreateTenantOnboardingRequest
```json
{
  "tenant_type": "company|individual",
  "tenant_name": "string (bắt buộc, 2-255 ký tự)",
  "company_name": "string (tùy chọn, 2-255 ký tự)",
  "tax_code": "string (tùy chọn, 5-50 ký tự)",
  "legal_representative": "string (tùy chọn, 2-255 ký tự)",
  "company_address": "string (tùy chọn)",
  "company_phone": "string (tùy chọn)",
  "company_email": "string (tùy chọn, email hợp lệ)"
}
```

### CreateWebsiteOnboardingRequest
```json
{
  "name": "string (bắt buộc, 2-100 ký tự)",
  "subdomain": "string (tùy chọn, 3-50 ký tự, chỉ chữ và số)",
  "custom_domain": "string (tùy chọn, FQDN hợp lệ)",
  "description": "string (tùy chọn)",
  "theme_id": "number (tùy chọn)"
}
```

### CompleteOnboardingStepRequest
```json
{
  "step_name": "email_verification|tenant_setup|website_setup|template_selection|tutorial",
  "data": "object (tùy chọn, dữ liệu cụ thể cho từng bước)"
}
```

## 📝 Các Bước Onboarding

Quy trình onboarding bao gồm 5 bước chính:

1. **email_verification** (bắt buộc): Xác thực địa chỉ email của người dùng
2. **tenant_setup** (bắt buộc): Tạo và cấu hình tenant
3. **website_setup** (bắt buộc): Tạo và cấu hình website đầu tiên
4. **template_selection** (tùy chọn): Chọn template cho website
5. **tutorial** (tùy chọn): Hoàn thành hướng dẫn onboarding

### Trạng Thái Các Bước
- `not_started`: Chưa bắt đầu
- `in_progress`: Đang thực hiện
- `completed`: Đã hoàn thành
- `skipped`: Đã bỏ qua (chỉ cho các bước tùy chọn)

## ⚠️ Xử Lý Lỗi

Tất cả các endpoint trả về response lỗi chuẩn hóa:

```json
{
  "status": {
    "code": 400,
    "message": "Mô tả lỗi",
    "success": false,
    "error_code": "MÃ_LỖI",
    "path": "/api/admin/v1/onboarding/endpoint"
  }
}
```

Các mã lỗi phổ biến:
- `INVALID_REQUEST`: Request body hoặc parameters không hợp lệ
- `TENANT_ID_REQUIRED`: Thiếu header X-Tenant-ID
- `USER_NOT_AUTHENTICATED`: Thiếu hoặc JWT token không hợp lệ
- `CREATE_TENANT_ERROR`: Không thể tạo tenant
- `CREATE_WEBSITE_ERROR`: Không thể tạo website
- `STEP_CANNOT_BE_SKIPPED`: Không thể bỏ qua bước bắt buộc
- `ONBOARDING_ALREADY_INITIALIZED`: Onboarding đã được khởi tạo

## 🧪 Kiểm Thử với Bruno

Sử dụng bộ sưu tập Bruno được cung cấp trong thư mục này để kiểm thử tất cả các endpoint. Bộ sưu tập bao gồm:

1. Thiết lập xác thực
2. Ví dụ quy trình onboarding hoàn chỉnh
3. Kiểm thử các trường hợp lỗi
4. Xác thực response

## 🎨 Hướng Dẫn Triển Khai UI

### 1. Hiển Thị Từng Bước
Hiển thị các bước onboarding từng bước một, đừng làm choáng ngợp người dùng với tất cả các bước cùng lúc.

### 2. Chỉ Báo Tiến Độ
Sử dụng tỷ lệ hoàn thành và trạng thái bước để hiển thị tiến độ một cách trực quan.

### 3. Xử Lý Lỗi
Triển khai xử lý lỗi phù hợp cho tất cả các API calls với thông báo thân thiện với người dùng.

### 4. Xác Thực
Xác thực đầu vào form ở phía client trước khi gửi đến API.

### 5. Quản Lý Trạng Thái
Theo dõi trạng thái onboarding trong ứng dụng để cho phép người dùng tiếp tục từ nơi họ đã dừng lại.

### 6. Thiết Kế Responsive
Đảm bảo quy trình onboarding hoạt động tốt trên tất cả các kích thước thiết bị.

## 💡 Ví Dụ Triển Khai Quy Trình

```javascript
// 1. Đăng nhập và lấy token
const loginResponse = await fetch('/api/admin/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: '12345678'
  })
});
const { data: { access_token } } = await loginResponse.json();

// 2. Tạo mã tenant
const tenantCodeResponse = await fetch('/api/admin/v1/onboarding/generate-tenant-code?tenant_name=' + encodeURIComponent(companyName), {
  headers: { 'Authorization': `Bearer ${access_token}` }
});
const { data: { tenant_code } } = await tenantCodeResponse.json();

// 3. Tạo tenant
const tenantResponse = await fetch('/api/admin/v1/onboarding/tenant', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    tenant_type: 'company',
    tenant_name: companyName,
    company_name: companyName,
    tax_code: '*********',
    legal_representative: 'John Doe'
  })
});
const tenant = await tenantResponse.json();

// 4. Tạo website
const websiteResponse = await fetch('/api/admin/v1/onboarding/website', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'X-Tenant-ID': tenant.data.tenant_id,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: websiteName,
    subdomain: subdomain,
    description: 'Website mô tả'
  })
});
const website = await websiteResponse.json();

// 5. Khởi tạo onboarding
const initResponse = await fetch('/api/admin/v1/onboarding/initialize', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'X-Tenant-ID': tenant.data.tenant_id,
    'X-Website-ID': website.data.website_id
  }
});

// 6. Hoàn thành các bước
const completeStep = async (stepName) => {
  return await fetch('/api/admin/v1/onboarding/steps/complete', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${access_token}`,
      'X-Tenant-ID': tenant.data.tenant_id,
      'X-Website-ID': website.data.website_id,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ step_name: stepName })
  });
};

// Hoàn thành từng bước
await completeStep('email_verification');
await completeStep('tenant_setup');
await completeStep('website_setup');
await completeStep('template_selection');

// 7. Kiểm tra trạng thái cuối cùng
const statusResponse = await fetch('/api/admin/v1/onboarding/status', {
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'X-Tenant-ID': tenant.data.tenant_id,
    'X-Website-ID': website.data.website_id
  }
});
const finalStatus = await statusResponse.json();
console.log('Onboarding hoàn thành:', finalStatus.data.is_completed);
```

## 🔗 Quy Trình Hoàn Chỉnh Theo Thứ Tự

1. **Đăng nhập** → Lấy JWT token
2. **Tạo tenant** → Lấy tenant_id
3. **Tạo website** → Lấy website_id
4. **Khởi tạo onboarding** → Thiết lập tracking
5. **Hoàn thành email_verification** → Bước bắt buộc
6. **Hoàn thành tenant_setup** → Bước bắt buộc
7. **Hoàn thành website_setup** → Bước bắt buộc
8. **Chọn template** → Bước tùy chọn
9. **Hoàn thành/bỏ qua tutorial** → Bước tùy chọn
10. **Kiểm tra trạng thái cuối** → Xác nhận hoàn thành

## 📞 Hỗ Trợ

Đối với các câu hỏi hoặc vấn đề với API Module Onboarding, vui lòng tham khảo bộ sưu tập Bruno để có các ví dụ hoạt động hoặc liên hệ với nhóm phát triển.
