import { DeleteOutlined, UploadOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Space,
  Spin,
  Switch,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { useNavigateTenant } from '../../../hooks';
import EditorMedia from '../../editor-media/components/editor-media';
import { MediaType } from '../../editor-media/type';
import {
  createTopping,
  getCategoryTree,
  getTopping,
  updateTopping,
} from '../api';
import { formItemLayout, ROUTES, tailFormItemLayout } from '../config';
import { DineinTopping } from '../type';

const { TextArea } = Input;

interface CategoryOption {
  value: number;
  label: string;
  children?: CategoryOption[];
}

const ToppingForm: React.FC = () => {
  const { t } = useTranslation(['dinein-product-topping', 'common']);
  const navigate = useNavigateTenant();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [showEditorMedia, setShowEditorMedia] = useState(false);
  const isEdit = !!id;

  useEffect(() => {
    fetchCategories();
    if (isEdit) {
      fetchTopping();
    }
  }, [isEdit]);

  const fetchCategories = async () => {
    try {
      const response = await getCategoryTree();
      if (response.data) {
        const transformCategories = (categories: any[]): CategoryOption[] => {
          return categories.map((category) => ({
            value: category.id,
            label: category.name,
            children: category.children
              ? transformCategories(category.children)
              : undefined,
          }));
        };
        setCategoryOptions(transformCategories(response.data));
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      message.error(t('dinein-product-topping:messages.fetchFailed'));
    }
  };

  const fetchTopping = async () => {
    setLoading(true);
    try {
      const response = await getTopping(id!);
      if (response.data) {
        const topping = response.data;
        form.setFieldsValue({
          name: topping.name,
          description: topping.description,
          price: topping.price,
          category_id: topping.category_id,
          is_active: topping.is_active,
          image_url: topping.image_url,
        });

        if (topping.image_url) {
          setImageUrl(topping.image_url);
        }
      }
    } catch (error) {
      console.error('Failed to fetch topping:', error);
      message.error(t('dinein-product-topping:messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const toppingData: DineinTopping = {
        ...values,
        image_url: imageUrl,
      };

      if (isEdit) {
        await updateTopping(id!, toppingData);
        message.success(t('dinein-product-topping:messages.updateSuccess'));
      } else {
        await createTopping(toppingData);
        message.success(t('dinein-product-topping:messages.createSuccess'));
      }
      navigate(ROUTES.LIST);
    } catch (error) {
      console.error('Failed to save topping:', error);
      message.error(
        isEdit
          ? t('dinein-product-topping:messages.updateFailed')
          : t('dinein-product-topping:messages.createFailed'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.LIST);
  };

  const handleMediaSelect = (selected: MediaType[]) => {
    if (selected && selected.length > 0) {
      const url = selected[0].public_url || selected[0].url;
      form.setFieldsValue({
        image_url: url,
      });
      setImageUrl(url || '');
    }
  };

  const handleRemoveImage = () => {
    form.setFieldsValue({
      image_url: '',
    });
    setImageUrl('');
  };

  return (
    <Card
      title={
        isEdit
          ? t('dinein-product-topping:edit.title')
          : t('dinein-product-topping:create.title')
      }
      bordered={false}
    >
      <Spin spinning={loading}>
        <Form
          {...formItemLayout}
          form={form}
          onFinish={handleSubmit}
          initialValues={{ is_active: true }}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('dinein-product-topping:form.name')}
            rules={[
              {
                required: true,
                message: t('dinein-product-topping:validation.nameRequired'),
              },
            ]}
          >
            <Input
              placeholder={t('dinein-product-topping:form.namePlaceholder')}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={t('dinein-product-topping:form.description')}
          >
            <TextArea
              placeholder={t(
                'dinein-product-topping:form.descriptionPlaceholder',
              )}
              rows={4}
            />
          </Form.Item>

          <Form.Item
            name="price"
            label={t('dinein-product-topping:form.price')}
            rules={[
              {
                required: true,
                message: t('dinein-product-topping:validation.priceRequired'),
              },
            ]}
          >
            <InputNumber<number>
              min={0}
              step={0.01}
              style={{ width: '100%' }}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
              parser={(value) => {
                const parsed = value ? value.replace(/\$\s?|(,*)/g, '') : '0';
                return Number(parsed);
              }}
              placeholder={t('dinein-product-topping:form.pricePlaceholder')}
            />
          </Form.Item>

          <Form.Item
            name="category_id"
            label={t('dinein-product-topping:form.category')}
          >
            <Select
              showSearch
              placeholder={t('dinein-product-topping:form.categoryPlaceholder')}
              options={categoryOptions}
            />
          </Form.Item>

          <Form.Item
            label={t('dinein-product-topping:form.image')}
            name="image_url"
          >
            <Input
              placeholder={t('dinein-product-topping:form.imageUrl')}
              readOnly
              hidden
            />
            <div className="image-upload-container">
              {imageUrl ? (
                <div className="image-preview-container">
                  <div
                    className="image-preview-square"
                    style={{ backgroundImage: `url(${imageUrl})` }}
                  />
                  <Space className="image-actions">
                    <Button
                      icon={<UploadOutlined />}
                      onClick={() => setShowEditorMedia(true)}
                    >
                      {t('dinein-product-topping:buttons.change')}
                    </Button>
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      onClick={handleRemoveImage}
                    >
                      {t('dinein-product-topping:buttons.delete')}
                    </Button>
                  </Space>
                </div>
              ) : (
                <Button
                  icon={<UploadOutlined />}
                  onClick={() => setShowEditorMedia(true)}
                >
                  {t('dinein-product-topping:buttons.selectImage')}
                </Button>
              )}
            </div>
          </Form.Item>

          <Form.Item
            name="is_active"
            label={t('dinein-product-topping:form.status')}
            valuePropName="checked"
          >
            <Switch
              checkedChildren={t('common:status.active')}
              unCheckedChildren={t('common:status.inactive')}
            />
          </Form.Item>

          <Form.Item {...tailFormItemLayout}>
            <Button onClick={handleCancel} style={{ marginRight: 8 }}>
              {t('dinein-product-topping:buttons.cancel')}
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit
                ? t('dinein-product-topping:buttons.update')
                : t('dinein-product-topping:buttons.create')}
            </Button>
          </Form.Item>
        </Form>
      </Spin>

      <EditorMedia
        showMedia={showEditorMedia}
        onClose={() => setShowEditorMedia(false)}
        onChange={handleMediaSelect}
        initialMediaType="image"
      />
    </Card>
  );
};

export default ToppingForm;
