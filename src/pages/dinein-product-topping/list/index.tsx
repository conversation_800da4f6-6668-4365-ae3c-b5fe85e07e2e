// filepath: /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/web/cms/src/pages/dinein-product-topping/list/index.tsx
import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, message, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useNavigateTenant } from '../../../hooks';
import { deleteTopping, getToppings } from '../api';
import { ROUTES } from '../config';
import { DineinTopping, transformTopping } from '../type';
import ToppingList from './list';
import ToppingSearch from './search';

const DineinProductToppingList: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigateTenant();
  const [loading, setLoading] = useState(false);
  const [toppings, setToppings] = useState<DineinTopping[]>([]);
  const [searchParams, setSearchParams] = useState<any>({
    page: 1,
    per_page: 10,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchToppings = async (params: any = {}) => {
    setLoading(true);
    try {
      const response = await getToppings({
        ...params,
      });

      if (response.data) {
        setToppings(response.data.map((item: any) => transformTopping(item)));
        setPagination({
          ...pagination,
          current: params.page || 1,
          total: response.meta?.total || 0,
        });
      }
    } catch (error) {
      console.error('Failed to fetch toppings:', error);
      message.error(t('common:messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchToppings(searchParams);
  }, [searchParams]);

  const handleTableChange = (pagination: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
    });
  };

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      ...values,
      page: 1,
    });
  };

  const handleEdit = (id: number) => {
    navigate(`${ROUTES.LIST}/edit/${id}`);
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteTopping(String(id));
      message.success(t('common:messages.deleteSuccess'));
      fetchToppings(searchParams);
    } catch (error) {
      console.error('Failed to delete topping:', error);
      message.error(t('common:messages.deleteFailed'));
    }
  };

  const handleCreateTopping = () => {
    navigate(`${ROUTES.LIST}/create`);
  };

  return (
    <div>
      <Card
        title={t('dinein-product-topping:title')}
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateTopping}
          >
            {t('common:buttons.create')}
          </Button>
        }
      >
        <ToppingSearch onSearch={handleSearch} />
        <Spin spinning={loading}>
          <ToppingList
            toppings={toppings}
            pagination={pagination}
            onChange={handleTableChange}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default DineinProductToppingList;
