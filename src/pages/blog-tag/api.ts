import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { BlogTag } from './type';

const url = `/api/admin/v1/blog/tags`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<BlogTag[]>> {
  const response = await apiService.get<ApiResponsePagination<BlogTag[]>>(url, {
    params,
  });
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<BlogTag>> {
  const response = await apiService.get<ApiResponse<BlogTag>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<BlogTag>> {
  const response = await apiService.post<ApiResponse<BlogTag>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<BlogTag>> {
  const response = await apiService.put<ApiResponse<BlogTag>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<BlogTag>> {
  const response = await apiService.delete<ApiResponse<BlogTag>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

// export async function getSearch(params: any): Promise<ApiResponse<BlogTag[]>> {
//   const response = await apiService.get<ApiResponse<BlogTag[]>>(
//     `${url}/search`,
//     { params },
//   );
//   return response.data;
// }

export async function getAll(): Promise<ApiResponse<BlogTag[]>> {
  const response = await apiService.get<ApiResponse<BlogTag[]>>(`${url}/all`);
  return response.data;
}
