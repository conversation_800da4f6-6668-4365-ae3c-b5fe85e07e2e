import { Select, SelectProps, Spin, notification } from 'antd';
import debounce from 'lodash/debounce';
import { useCallback, useState } from 'react';
import ConsoleService from '../../../services/console.service';
import { SelectOption } from '../../../types.global';
import { getOptions } from '../api';
import { MODULE } from '../config';

interface Props extends SelectProps {
  isOptionAll?: boolean;
}
const SearchBlogTag: React.FC<Props> = (props) => {
  const logger = ConsoleService.register(MODULE);
  const [items, setItems] = useState<SelectOption[]>([]);
  const [loading, setLoading] = useState(false);

  // Debounced search handler
  const onSearch = useCallback(
    debounce((query) => {
      console.log(query);
      setLoading(true);
      getOptions(query)
        .then((res) => {
          setItems(res.data);
          setLoading(false);
          logger(res.data);
        })
        .catch((err) => {
          notification.error({
            message: err.message,
          });
          setLoading(false);
        });
    }, 800),
    [],
  );

  return (
    <Select
      showSearch
      {...props}
      placeholder="Nhập để tìm kiếm..."
      style={{ width: '100%' }}
      defaultActiveFirstOption={false}
      filterOption={false}
      notFoundContent={loading ? <Spin size="small" /> : null}
      onSearch={onSearch}
      loading={loading}
      options={items}
      mode="tags"
    ></Select>
  );
};

export default SearchBlogTag;
