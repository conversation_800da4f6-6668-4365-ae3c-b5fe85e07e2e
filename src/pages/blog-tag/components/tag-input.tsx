import { PlusOutlined } from '@ant-design/icons';
import { Button, Input, message, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItems } from '../api';
import { MODULE } from '../config';
import { BlogTag } from '../type';

interface TagInputProps {
  tags: string[];
  onChange: (tags: string[]) => void;
  maxTags?: number;
}

export const TagInput: React.FC<TagInputProps> = ({
  tags = [],
  onChange,
  maxTags = 30,
}) => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation('blog-tag');

  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [tagNames, setTagNames] = useState<Record<string, string>>({});

  // Lấy tên các tag dựa trên ID
  useEffect(() => {
    if (tags.length > 0) {
      const fetchTagNames = async () => {
        try {
          // Tạo query params để lấy danh sách tag dựa trên IDs
          const tagsParam = tags.join(',');
          const response = await getItems({ ids: tagsParam });

          if (response.status.success && response.data) {
            const newTagNames: Record<string, string> = {};
            response.data.forEach((tag: BlogTag) => {
              newTagNames[String(tag.id)] = tag.name;
            });
            setTagNames(newTagNames);
          }
        } catch (error) {
          console.error('Lỗi khi lấy thông tin tag:', error);
        }
      };

      fetchTagNames();
    }
  }, [tags]);

  const handleClose = (removedTag: string) => {
    const newTags = tags.filter((tag) => tag !== removedTag);
    logger('[TagInput] handleClose', {
      tags,
      newTags,
    });
    onChange(newTags);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = async () => {
    if (!inputValue.trim()) {
      setInputVisible(false);
      setInputValue('');
      return;
    }

    if (tags.length >= maxTags) {
      message.warning(t('maxTagsReached', { max: maxTags }));
      setInputVisible(false);
      setInputValue('');
      return;
    }

    try {
      // Trực tiếp tạo tag với tên đã nhập
      const createResponse = await createItem({
        name: inputValue,
        is_active: true,
      });

      if (createResponse.status.success && createResponse.data) {
        // Tag được tạo thành công
        const tagId = String(createResponse.data.id);

        // Cập nhật danh sách tên tag
        setTagNames((prev) => ({
          ...prev,
          [tagId]: inputValue,
        }));

        // Thêm tag ID vào danh sách
        const newTags = [...tags, tagId];
        onChange(newTags);

        logger('[TagInput] Tag created', { tagId, name: inputValue });
      }
    } catch (error: any) {
      // Xử lý trường hợp tag đã tồn tại
      if (error.response?.data?.status?.error_code === 'TAG_SLUG_EXISTS') {
        // Tag đã tồn tại, tìm kiếm tag với tên tương tự
        try {
          const searchResponse = await getItems({ name: inputValue });

          if (
            searchResponse.status.success &&
            searchResponse.data &&
            searchResponse.data.length > 0
          ) {
            // Tìm tag phù hợp nhất
            const matchingTag =
              searchResponse.data.find(
                (tag: BlogTag) =>
                  tag.name.toLowerCase() === inputValue.toLowerCase(),
              ) || searchResponse.data[0];

            const tagId = String(matchingTag.id);

            // Kiểm tra xem tag đã có trong danh sách chưa
            if (!tags.includes(tagId)) {
              // Cập nhật danh sách tên tag
              setTagNames((prev) => ({
                ...prev,
                [tagId]: matchingTag.name,
              }));

              // Thêm tag ID vào danh sách
              const newTags = [...tags, tagId];
              onChange(newTags);

              logger('[TagInput] Existing tag found', {
                tagId,
                name: matchingTag.name,
              });
            } else {
              message.info(t('tagAlreadySelected'));
            }
          }
        } catch (searchError) {
          console.error('Lỗi khi tìm kiếm tag:', searchError);
          message.error(t('errorProcessingTag'));
        }
      } else {
        console.error('Lỗi khi xử lý tag:', error);
        message.error(t('errorProcessingTag'));
      }
    }

    setInputVisible(false);
    setInputValue('');
  };

  const showInput = () => {
    setInputVisible(true);
  };

  return (
    <div className="tag-container">
      <div className="tag-list">
        {tags.map((tagId) => (
          <Tag
            key={tagId}
            closable
            onClose={() => handleClose(tagId)}
            style={{ marginBottom: '8px' }}
          >
            {tagNames[tagId] || tagId}
          </Tag>
        ))}
      </div>
      <div className="tag-input">
        {inputVisible ? (
          <Input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputConfirm}
            onPressEnter={handleInputConfirm}
            style={{
              width: '100px',
              marginRight: '8px',
              verticalAlign: 'top',
            }}
            autoFocus
          />
        ) : (
          <Button
            type="dashed"
            onClick={showInput}
            icon={<PlusOutlined />}
            style={{ marginBottom: '8px' }}
          />
        )}
      </div>
      <div className="tag-count">
        {tags.length}/{maxTags}
      </div>
    </div>
  );
};

export default TagInput;
