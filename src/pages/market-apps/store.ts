import { create } from 'zustand';
import { deleteItem, getOptions, getPublic } from './api';
import { MarketApps } from './type';

export interface StoreState {
  loading: boolean;
  items: MarketApps[];
  options: any[];
  apps: MarketApps[];
  setLoading: (loading: boolean) => void;
  delete: (id: string) => void;
  setOptions: (options: any) => void;
  deleteItem: (id: string) => Promise<void>;
  getOptions: () => Promise<void>;
  getAppsPublic: (params: any) => Promise<void>;
}

const useMarketAppsStore = create<StoreState>((set) => ({
  items: [],
  item: undefined,
  options: [],
  loading: false,
  apps: [],
  setLoading: (loading: boolean) => {
    set({ loading });
  },
  delete: (id: string) => {
    set((state) => {
      const itemsNew = state.items.filter((b) => b.id !== id);
      return { items: itemsNew };
    });
  },
  setOptions: (options: any) => {
    set({ options });
  },
  deleteItem: async (id: string) => {
    set({ loading: true });
    await deleteItem(id);
    set((state) => {
      const itemsNew = state.items.filter((b) => b.id !== id);
      return { items: itemsNew, loading: false };
    });
  },
  getOptions: async () => {
    const response = await getOptions();
    set({ options: response.data });
  },
  getAppsPublic: async (params: any) => {
    const response = await getPublic(params);
    set({ apps: response.data });
  },
}));

export default useMarketAppsStore;
