import { create } from 'zustand';
import ConsoleService from '../../services/console.service';
import { tenantOnboardingApi } from './api';
import {
  OnboardingProgress,
  OnboardingStatus,
  OnboardingStep,
  ProfileSetup,
  TenantOnboarding,
  WebsiteSetup,
} from './type';

const logger = ConsoleService.register('tenant-onboarding.store');

export interface OnboardingState {
  // Current step and status
  currentStep: OnboardingStep;
  onboardingStatus: OnboardingStatus;
  onboardingProgress: OnboardingProgress[];
  isCompleted: boolean;
  completionPercentage: number;

  // Loading states
  loading: boolean;
  submitting: boolean;

  // Form data for each step
  tenantData: TenantOnboarding;
  websiteData: WebsiteSetup;
  profileData: ProfileSetup;
  selectedTemplateId: number | null;

  // Created IDs
  createdTenantId: string | null;
  createdWebsiteId: string | null;
  userId: string | null;

  // Email verification
  isEmailVerified: boolean;
  userEmail: string | null;

  // Actions
  setCurrentStep: (step: OnboardingStep) => void;
  setOnboardingStatus: (status: OnboardingStatus) => void;
  setLoading: (loading: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  setEmailVerified: (verified: boolean) => void;
  setSelectedTemplateId: (id: number | null) => void;

  // Form data setters
  setTenantData: (data: Partial<TenantOnboarding>) => void;
  setWebsiteData: (data: Partial<WebsiteSetup>) => void;
  setProfileData: (data: Partial<ProfileSetup>) => void;

  // ID setters
  setCreatedTenantId: (id: string) => void;
  setCreatedWebsiteId: (id: string) => void;

  // API functions
  generateTenantCode: (tenantName: string) => Promise<any>;
  submitTenantSetup: (data: TenantOnboarding) => Promise<any>;
  submitWebsiteSetup: (data: WebsiteSetup) => Promise<any>;
  initializeOnboardingTracking: () => Promise<any>;
  completeOnboardingStep: (stepName: string, data?: any) => Promise<any>;
  skipOnboardingStep: (stepName: string, reason?: string) => Promise<any>;
  getNextStep: () => Promise<any>;
  submitProfileSetup: (data: ProfileSetup) => Promise<any>;
  initializeOnboarding: () => Promise<void>;
  skipStep: (step: OnboardingStep) => Promise<any>;

  // Reset functions
  resetTenantData: () => void;
  resetWebsiteData: () => void;
  resetProfileData: () => void;
  resetAll: () => void;
}

const initialTenantData: TenantOnboarding = {
  tenant_name: '',
  tenant_code: '',
  tenant_type: 'individual',
  company_name: '',
  tax_code: '',
  legal_representative: '',
  company_address: '',
  company_phone: '',
  company_email: '',
};

const initialWebsiteData: WebsiteSetup = {
  name: '',
  subdomain: '',
  description: '',
};

const initialProfileData: ProfileSetup = {
  full_name: '',
  phone: '',
  address: '',
  avatar: '',
};

export const useTenantOnboardingStore = create<OnboardingState>((set, get) => ({
  // Initial state
  currentStep: OnboardingStep.EMAIL_VERIFICATION,
  onboardingStatus: OnboardingStatus.NOT_STARTED,
  onboardingProgress: [],
  isCompleted: false,
  completionPercentage: 0,
  loading: false,
  submitting: false,

  tenantData: initialTenantData,
  websiteData: initialWebsiteData,
  profileData: initialProfileData,
  selectedTemplateId: null,

  // Created IDs
  createdTenantId: null,
  createdWebsiteId: null,
  userId: null,

  // Email verification
  isEmailVerified: false,
  userEmail: null,

  // Actions
  setCurrentStep: (step: OnboardingStep) => {
    logger.info('Setting current step:', step);
    set({ currentStep: step });
  },

  setOnboardingStatus: (status: OnboardingStatus) => {
    logger.info('Setting onboarding status:', status);
    set({ onboardingStatus: status });
  },

  setLoading: (loading: boolean) => set({ loading }),
  setSubmitting: (submitting: boolean) => set({ submitting }),
  setEmailVerified: (verified: boolean) => set({ isEmailVerified: verified }),
  setSelectedTemplateId: (id: number | null) => set({ selectedTemplateId: id }),

  // Form data setters
  setTenantData: (data: Partial<TenantOnboarding>) => {
    const currentData = get().tenantData;
    set({ tenantData: { ...currentData, ...data } });
  },

  setWebsiteData: (data: Partial<WebsiteSetup>) => {
    const currentData = get().websiteData;
    set({ websiteData: { ...currentData, ...data } });
  },

  setProfileData: (data: Partial<ProfileSetup>) => {
    const currentData = get().profileData;
    set({ profileData: { ...currentData, ...data } });
  },

  // ID setters
  setCreatedTenantId: (id: string) => {
    logger.info('Setting created tenant ID:', id);
    set({ createdTenantId: id });
  },

  setCreatedWebsiteId: (id: string) => {
    logger.info('Setting created website ID:', id);
    set({ createdWebsiteId: id });
  },

  // API functions
  generateTenantCode: async (tenantName: string) => {
    try {
      const response = await tenantOnboardingApi.generateTenantCode(tenantName);
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to generate tenant code:', error);
      return {
        success: false,
        message: error.message || 'Failed to generate tenant code',
      };
    }
  },

  submitTenantSetup: async (data: TenantOnboarding) => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.setupTenant(data);
      if (response.data && response.data.tenant_id) {
        get().setCreatedTenantId(response.data.tenant_id);
        get().setTenantData(data);
      }
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to setup tenant:', error);
      return {
        success: false,
        message: error.message || 'Failed to setup tenant',
      };
    } finally {
      set({ submitting: false });
    }
  },

  submitWebsiteSetup: async (data: WebsiteSetup) => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.setupWebsite(data);
      if (response.data && response.data.website_id) {
        get().setCreatedWebsiteId(response.data.website_id);
        get().setWebsiteData(data);
      }
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to setup website:', error);
      return {
        success: false,
        message: error.message || 'Failed to setup website',
      };
    } finally {
      set({ submitting: false });
    }
  },

  initializeOnboardingTracking: async () => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.initializeOnboarding();
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to initialize onboarding:', error);
      return {
        success: false,
        message: error.message || 'Failed to initialize onboarding',
      };
    } finally {
      set({ submitting: false });
    }
  },

  completeOnboardingStep: async (stepName: string, data?: any) => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.completeOnboardingStep({
        step_name: stepName as any,
        data,
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to complete onboarding step:', error);
      return {
        success: false,
        message: error.message || 'Failed to complete onboarding step',
      };
    } finally {
      set({ submitting: false });
    }
  },

  skipOnboardingStep: async (stepName: string, reason?: string) => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.skipOnboardingStep({
        step_name: stepName as any,
        reason,
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to skip onboarding step:', error);
      return {
        success: false,
        message: error.message || 'Failed to skip onboarding step',
      };
    } finally {
      set({ submitting: false });
    }
  },

  getNextStep: async () => {
    try {
      const response = await tenantOnboardingApi.getNextStep();
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to get next step:', error);
      return {
        success: false,
        message: error.message || 'Failed to get next step',
      };
    }
  },

  submitProfileSetup: async (data: ProfileSetup) => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.setupProfile(data);
      get().setProfileData(data);
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to setup profile:', error);
      return {
        success: false,
        message: error.message || 'Failed to setup profile',
      };
    } finally {
      set({ submitting: false });
    }
  },

  completeOnboarding: async () => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.completeOnboarding();
      get().setOnboardingStatus(OnboardingStatus.COMPLETED);
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to complete onboarding:', error);
      return {
        success: false,
        message: error.message || 'Failed to complete onboarding',
      };
    } finally {
      set({ submitting: false });
    }
  },

  initializeOnboarding: async () => {
    set({ loading: true });
    try {
      const response = await tenantOnboardingApi.getOnboardingStatus();
      if (response.data) {
        get().setOnboardingStatus(
          response.data.onboarding_status as OnboardingStatus,
        );
        // Set current step based on progress
        if (response.data.progress && response.data.progress.length > 0) {
          const lastCompletedStep = response.data.progress
            .filter((p: any) => p.status === 'completed')
            .pop();

          if (lastCompletedStep) {
            switch (lastCompletedStep.step) {
              case 'tenant_setup':
                get().setCurrentStep(OnboardingStep.WEBSITE_SETUP);
                break;
              case 'website_setup':
                get().setCurrentStep(OnboardingStep.PROFILE_SETUP);
                break;
              case 'profile_setup':
                get().setCurrentStep(OnboardingStep.TUTORIAL_COMPLETE);
                break;
              default:
                get().setCurrentStep(OnboardingStep.TENANT_SETUP);
            }
          }
        }
      }
    } catch (error: any) {
      logger.error('Failed to initialize onboarding:', error);
    } finally {
      set({ loading: false });
    }
  },

  skipStep: async (step: OnboardingStep) => {
    set({ submitting: true });
    try {
      const response = await tenantOnboardingApi.skipStep(step);
      return { success: true, data: response.data };
    } catch (error: any) {
      logger.error('Failed to skip step:', error);
      return {
        success: false,
        message: error.message || 'Failed to skip step',
      };
    } finally {
      set({ submitting: false });
    }
  },

  // Reset functions
  resetTenantData: () => set({ tenantData: initialTenantData }),
  resetWebsiteData: () => set({ websiteData: initialWebsiteData }),
  resetProfileData: () => set({ profileData: initialProfileData }),

  resetAll: () => {
    set({
      currentStep: OnboardingStep.TENANT_SETUP,
      onboardingStatus: OnboardingStatus.NOT_STARTED,
      loading: false,
      submitting: false,
      tenantData: initialTenantData,
      websiteData: initialWebsiteData,
      profileData: initialProfileData,
      createdTenantId: null,
      createdWebsiteId: null,
    });
  },
}));
