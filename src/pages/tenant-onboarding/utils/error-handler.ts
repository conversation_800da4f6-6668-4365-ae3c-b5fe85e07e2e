import { message } from 'antd';

// Error codes from API documentation
export enum OnboardingErrorCode {
  INVALID_REQUEST = 'INVALID_REQUEST',
  TENANT_ID_REQUIRED = 'TENANT_ID_REQUIRED',
  USER_NOT_AUTHENTICATED = 'USER_NOT_AUTHENTICATED',
  CREATE_TENANT_ERROR = 'CREATE_TENANT_ERROR',
  CREATE_WEBSITE_ERROR = 'CREATE_WEBSITE_ERROR',
  STEP_CANNOT_BE_SKIPPED = 'STEP_CANNOT_BE_SKIPPED',
  ONBOARDING_ALREADY_INITIALIZED = 'ONBOARDING_ALREADY_INITIALIZED',
  TENANT_CODE_ALREADY_EXISTS = 'TENANT_CODE_ALREADY_EXISTS',
  SUBDOMAIN_ALREADY_EXISTS = 'SUBDOMAIN_ALREADY_EXISTS',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  INVALID_TEMPLATE_ID = 'INVALID_TEMPLATE_ID',
}

interface ApiError {
  status: {
    code: number;
    message: string;
    success: boolean;
    error_code?: string;
    path?: string;
  };
  errors?: string[] | Record<string, string | string[]>;
  error_details?: Array<{ field?: string; message: string }>;
}

export class OnboardingErrorHandler {
  static getErrorMessage(error: any): string {
    // Handle API response errors
    if (error.response?.data) {
      const apiError: ApiError = error.response.data;
      
      // Check for specific error codes
      if (apiError.status?.error_code) {
        return this.getErrorMessageByCode(apiError.status.error_code);
      }
      
      // Return API message if available
      if (apiError.status?.message) {
        return apiError.status.message;
      }
      
      // Handle validation errors
      if (apiError.errors) {
        return this.formatValidationErrors(apiError.errors);
      }
      
      // Handle error details
      if (apiError.error_details && apiError.error_details.length > 0) {
        return apiError.error_details.map(detail => detail.message).join(', ');
      }
    }
    
    // Handle network errors
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      return 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.';
    }
    
    // Handle timeout errors
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return 'Yêu cầu bị timeout. Vui lòng thử lại.';
    }
    
    // Default error message
    return error.message || 'Có lỗi không xác định xảy ra';
  }

  static getErrorMessageByCode(errorCode: string): string {
    const errorMessages: Record<string, string> = {
      [OnboardingErrorCode.INVALID_REQUEST]: 'Dữ liệu yêu cầu không hợp lệ',
      [OnboardingErrorCode.TENANT_ID_REQUIRED]: 'Thiếu thông tin tenant ID',
      [OnboardingErrorCode.USER_NOT_AUTHENTICATED]: 'Bạn cần đăng nhập để tiếp tục',
      [OnboardingErrorCode.CREATE_TENANT_ERROR]: 'Không thể tạo tenant. Vui lòng thử lại.',
      [OnboardingErrorCode.CREATE_WEBSITE_ERROR]: 'Không thể tạo website. Vui lòng thử lại.',
      [OnboardingErrorCode.STEP_CANNOT_BE_SKIPPED]: 'Không thể bỏ qua bước này',
      [OnboardingErrorCode.ONBOARDING_ALREADY_INITIALIZED]: 'Quá trình onboarding đã được khởi tạo',
      [OnboardingErrorCode.TENANT_CODE_ALREADY_EXISTS]: 'Mã tenant đã tồn tại. Vui lòng chọn mã khác.',
      [OnboardingErrorCode.SUBDOMAIN_ALREADY_EXISTS]: 'Subdomain đã tồn tại. Vui lòng chọn subdomain khác.',
      [OnboardingErrorCode.EMAIL_NOT_VERIFIED]: 'Email chưa được xác thực',
      [OnboardingErrorCode.INVALID_TEMPLATE_ID]: 'Template ID không hợp lệ',
    };

    return errorMessages[errorCode] || `Lỗi: ${errorCode}`;
  }

  static formatValidationErrors(errors: string[] | Record<string, string | string[]>): string {
    if (Array.isArray(errors)) {
      return errors.join(', ');
    }

    const errorMessages: string[] = [];
    Object.entries(errors).forEach(([field, fieldErrors]) => {
      const fieldName = this.getFieldDisplayName(field);
      if (Array.isArray(fieldErrors)) {
        fieldErrors.forEach(error => {
          errorMessages.push(`${fieldName}: ${error}`);
        });
      } else {
        errorMessages.push(`${fieldName}: ${fieldErrors}`);
      }
    });

    return errorMessages.join(', ');
  }

  static getFieldDisplayName(field: string): string {
    const fieldNames: Record<string, string> = {
      tenant_name: 'Tên tenant',
      tenant_code: 'Mã tenant',
      tenant_type: 'Loại tenant',
      company_name: 'Tên công ty',
      tax_code: 'Mã số thuế',
      legal_representative: 'Người đại diện pháp luật',
      company_address: 'Địa chỉ công ty',
      company_phone: 'Số điện thoại công ty',
      company_email: 'Email công ty',
      name: 'Tên website',
      subdomain: 'Subdomain',
      custom_domain: 'Tên miền riêng',
      description: 'Mô tả',
      theme_id: 'Theme ID',
      email: 'Email',
      password: 'Mật khẩu',
    };

    return fieldNames[field] || field;
  }

  static showError(error: any): void {
    const errorMessage = this.getErrorMessage(error);
    message.error(errorMessage);
  }

  static showValidationError(field: string, errorMessage: string): void {
    const fieldName = this.getFieldDisplayName(field);
    message.error(`${fieldName}: ${errorMessage}`);
  }

  static isRetryableError(error: any): boolean {
    // Network errors and timeouts are retryable
    if (error.code === 'NETWORK_ERROR' || 
        error.code === 'ECONNABORTED' || 
        error.message?.includes('Network Error') ||
        error.message?.includes('timeout')) {
      return true;
    }

    // Server errors (5xx) are retryable
    if (error.response?.status >= 500) {
      return true;
    }

    return false;
  }

  static shouldRedirectToLogin(error: any): boolean {
    if (error.response?.data?.status?.error_code === OnboardingErrorCode.USER_NOT_AUTHENTICATED) {
      return true;
    }

    if (error.response?.status === 401) {
      return true;
    }

    return false;
  }
}

// Validation utilities
export class OnboardingValidator {
  static validateTenantName(name: string): string | null {
    if (!name || name.trim().length === 0) {
      return 'Tên tenant là bắt buộc';
    }
    if (name.length < 2) {
      return 'Tên tenant phải có ít nhất 2 ký tự';
    }
    if (name.length > 255) {
      return 'Tên tenant không được vượt quá 255 ký tự';
    }
    return null;
  }

  static validateTenantCode(code: string): string | null {
    if (!code || code.trim().length === 0) {
      return 'Mã tenant là bắt buộc';
    }
    if (!/^[a-z0-9-]+$/.test(code)) {
      return 'Mã tenant chỉ được chứa chữ thường, số và dấu gạch ngang';
    }
    if (code.length < 3) {
      return 'Mã tenant phải có ít nhất 3 ký tự';
    }
    if (code.length > 50) {
      return 'Mã tenant không được vượt quá 50 ký tự';
    }
    return null;
  }

  static validateWebsiteName(name: string): string | null {
    if (!name || name.trim().length === 0) {
      return 'Tên website là bắt buộc';
    }
    if (name.length < 2) {
      return 'Tên website phải có ít nhất 2 ký tự';
    }
    if (name.length > 100) {
      return 'Tên website không được vượt quá 100 ký tự';
    }
    return null;
  }

  static validateSubdomain(subdomain: string): string | null {
    if (!subdomain) {
      return null; // Subdomain is optional
    }
    if (!/^[a-z0-9]+$/.test(subdomain)) {
      return 'Subdomain chỉ được chứa chữ thường và số';
    }
    if (subdomain.length < 3) {
      return 'Subdomain phải có ít nhất 3 ký tự';
    }
    if (subdomain.length > 50) {
      return 'Subdomain không được vượt quá 50 ký tự';
    }
    return null;
  }

  static validateEmail(email: string): string | null {
    if (!email) {
      return null; // Email might be optional in some contexts
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return 'Email không hợp lệ';
    }
    return null;
  }

  static validateTaxCode(taxCode: string): string | null {
    if (!taxCode) {
      return null; // Tax code is optional
    }
    if (taxCode.length < 5) {
      return 'Mã số thuế phải có ít nhất 5 ký tự';
    }
    if (taxCode.length > 50) {
      return 'Mã số thuế không được vượt quá 50 ký tự';
    }
    return null;
  }
}
