// Request types based on API documentation
export interface CreateTenantOnboardingRequest {
  tenant_type: 'company' | 'individual';
  tenant_name: string; // 2-255 characters, required
  company_name?: string; // 2-255 characters, optional
  tax_code?: string; // 5-50 characters, optional
  legal_representative?: string; // 2-255 characters, optional
  company_address?: string; // optional
  company_phone?: string; // optional
  company_email?: string; // valid email, optional
}

export interface CreateWebsiteOnboardingRequest {
  name: string; // 2-100 characters, required
  subdomain?: string; // 3-50 characters, alphanumeric only, optional
  custom_domain?: string; // valid FQDN, optional
  description?: string; // optional
  theme_id?: number; // optional
}

export interface CompleteOnboardingStepRequest {
  step_name:
    | 'email_verification'
    | 'tenant_setup'
    | 'website_setup'
    | 'template_selection'
    | 'tutorial';
  data?: any; // optional, step-specific data
}

export interface SkipOnboardingStepRequest {
  step_name: 'template_selection' | 'tutorial'; // only optional steps can be skipped
  reason?: string; // optional reason for skipping
}

// Legacy interfaces for backward compatibility
export interface TenantOnboarding extends CreateTenantOnboardingRequest {
  tenant_code?: string; // for backward compatibility
}

export interface WebsiteSetup extends CreateWebsiteOnboardingRequest {}

export interface ProfileSetup {
  full_name: string;
  phone?: string;
  address?: string;
  avatar?: string;
}

export interface OnboardingProgress {
  step_name:
    | 'email_verification'
    | 'tenant_setup'
    | 'website_setup'
    | 'template_selection'
    | 'tutorial';
  step_title: string;
  step_description: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  is_required: boolean;
  completed_at?: string;
  data?: any;
}

export enum OnboardingStep {
  EMAIL_VERIFICATION = 'email_verification',
  TENANT_SETUP = 'tenant_setup',
  WEBSITE_SETUP = 'website_setup',
  TEMPLATE_SELECTION = 'template_selection',
  TUTORIAL = 'tutorial',
  COMPLETED = 'completed',
}

export enum OnboardingStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
}

export enum OnboardingStepStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
}
