'use client';

import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { Button, Card, Layout, Space, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  OnboardingComplete,
  OnboardingProgress,
  ProfileSetupForm,
  TenantSetupForm,
  WebsiteSetupForm
} from '../components';
import { useTenantOnboardingStore } from '../store';
import { OnboardingStep } from '../type';

const { Content } = Layout;

const TenantOnboardingForm: React.FC = () => {
  const navigate = useNavigate();
  const {
    currentStep,
    onboardingStatus,
    loading,
    submitting,
    tenantData,
    websiteData,
    profileData,
    setCurrentStep,
    initializeOnboarding,
    submitTenantSetup,
    submitWebsiteSetup,
    submitProfileSetup,
    completeOnboarding,
    skipStep,
  } = useTenantOnboardingStore();

  const [currentFormData, setCurrentFormData] = useState<any>({});

  useEffect(() => {
    initializeOnboarding();
  }, [initializeOnboarding]);

  const handleNext = async () => {
    try {
      let response;

      switch (currentStep) {
        case OnboardingStep.TENANT_SETUP:
          response = await submitTenantSetup(currentFormData);
          if (response.success) {
            setCurrentStep(OnboardingStep.WEBSITE_SETUP);
            message.success('Thiết lập tenant thành công!');
          } else {
            message.error(response.message || 'Có lỗi xảy ra');
          }
          break;

        case OnboardingStep.WEBSITE_SETUP:
          response = await submitWebsiteSetup(currentFormData);
          if (response.success) {
            setCurrentStep(OnboardingStep.PROFILE_SETUP);
            message.success('Tạo website thành công!');
          } else {
            message.error(response.message || 'Có lỗi xảy ra');
          }
          break;

        case OnboardingStep.PROFILE_SETUP:
          response = await submitProfileSetup(currentFormData);
          if (response.success) {
            const completeResponse = await completeOnboarding();
            if (completeResponse.success) {
              setCurrentStep(OnboardingStep.TUTORIAL_COMPLETE);
              message.success('Hoàn thành onboarding!');
            }
          } else {
            message.error(response.message || 'Có lỗi xảy ra');
          }
          break;

        default:
          break;
      }
    } catch (error) {
      message.error('Có lỗi xảy ra, vui lòng thử lại');
    }
  };

  const handleSkip = async () => {
    try {
      const response = await skipStep(currentStep);
      if (response.success) {
        switch (currentStep) {
          case OnboardingStep.TENANT_SETUP:
            setCurrentStep(OnboardingStep.WEBSITE_SETUP);
            break;
          case OnboardingStep.WEBSITE_SETUP:
            setCurrentStep(OnboardingStep.PROFILE_SETUP);
            break;
          case OnboardingStep.PROFILE_SETUP: {
            const completeResponse = await completeOnboarding();
            if (completeResponse.success) {
              setCurrentStep(OnboardingStep.TUTORIAL_COMPLETE);
            }
            break;
          }
        }
        message.info('Đã bỏ qua bước này');
      }
    } catch (error) {
      message.error('Có lỗi xảy ra');
    }
  };

  const handleBack = () => {
    switch (currentStep) {
      case OnboardingStep.WEBSITE_SETUP:
        setCurrentStep(OnboardingStep.TENANT_SETUP);
        break;
      case OnboardingStep.PROFILE_SETUP:
        setCurrentStep(OnboardingStep.WEBSITE_SETUP);
        break;
      default:
        break;
    }
  };

  const handleFormChange = (data: any) => {
    setCurrentFormData(data);
  };

  const renderCurrentForm = () => {
    switch (currentStep) {
      case OnboardingStep.TENANT_SETUP:
        return (
          <TenantSetupForm
            initialData={tenantData}
            onSubmit={handleFormChange}
            loading={submitting}
          />
        );

      case OnboardingStep.WEBSITE_SETUP:
        return (
          <WebsiteSetupForm
            initialData={websiteData}
            onSubmit={handleFormChange}
            loading={submitting}
          />
        );

      case OnboardingStep.PROFILE_SETUP:
        return (
          <ProfileSetupForm
            initialData={profileData}
            onChange={handleFormChange}
            loading={submitting}
          />
        );

      case OnboardingStep.TUTORIAL_COMPLETE:
        return (
          <OnboardingComplete
            tenantData={tenantData}
            websiteData={websiteData}
            onNavigateToDashboard={() => navigate('/auth/tenant-selection')}
            onNavigateToWebsite={() => {
              if (websiteData.subdomain) {
                window.open(
                  `https://${websiteData.subdomain}.yourdomain.com`,
                  '_blank',
                );
              }
            }}
          />
        );

      default:
        return null;
    }
  };

  const canGoNext = () => {
    return Object.keys(currentFormData).length > 0 && !submitting;
  };

  const canGoBack = () => {
    return (
      currentStep !== OnboardingStep.TENANT_SETUP &&
      currentStep !== OnboardingStep.TUTORIAL_COMPLETE
    );
  };

  const canSkip = () => {
    return currentStep !== OnboardingStep.TUTORIAL_COMPLETE;
  };

  if (loading) {
    return (
      <Layout className="min-h-screen bg-gray-50">
        <Content className="flex items-center justify-center">
          <div>Đang tải...</div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Content className="py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Progress */}
          <OnboardingProgress
            currentStep={currentStep}
            completedSteps={[]}
            status={onboardingStatus}
          />

          {/* Main Content */}
          <Card className="shadow-lg">
            <div className="min-h-[500px]">{renderCurrentForm()}</div>

            {/* Navigation */}
            {currentStep !== OnboardingStep.TUTORIAL_COMPLETE && (
              <div className="flex justify-between items-center mt-8 pt-6 border-t">
                <div>
                  {canGoBack() && (
                    <Button
                      icon={<ArrowLeftOutlined />}
                      onClick={handleBack}
                      disabled={submitting}
                    >
                      Quay lại
                    </Button>
                  )}
                </div>

                <Space>
                  {canSkip() && (
                    <Button onClick={handleSkip} disabled={submitting}>
                      Bỏ qua
                    </Button>
                  )}

                  <Button
                    type="primary"
                    icon={<ArrowRightOutlined />}
                    onClick={handleNext}
                    disabled={!canGoNext()}
                    loading={submitting}
                  >
                    {currentStep === OnboardingStep.PROFILE_SETUP
                      ? 'Hoàn thành'
                      : 'Tiếp tục'}
                  </Button>
                </Space>
              </div>
            )}
          </Card>
        </div>
      </Content>
    </Layout>
  );
};

export default TenantOnboardingForm;
