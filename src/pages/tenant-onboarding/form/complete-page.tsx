'use client';

import { <PERSON><PERSON>eftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { Button, Card, Layout, Space, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  EmailVerificationForm,
  OnboardingComplete,
  OnboardingProgress,
  ProfileSetupForm,
  TenantSetupForm,
  TemplateSelectionForm,
  TutorialForm,
  WebsiteSetupForm,
} from '../components';
import { useTenantOnboardingStore } from '../store';
import { OnboardingStep } from '../type';

const { Content } = Layout;

const TenantOnboardingForm: React.FC = () => {
  const navigate = useNavigate();
  const {
    currentStep,
    onboardingStatus,
    loading,
    submitting,
    tenantData,
    websiteData,
    profileData,
    isEmailVerified,
    selectedTemplateId,
    setCurrentStep,
    setEmailVerified,
    setSelectedTemplateId,
    initializeOnboarding,
    generateTenantCode,
    submitTenantSetup,
    submitWebsiteSetup,
    initializeOnboardingTracking,
    completeOnboardingStep,
    skipOnboardingStep,
    submitProfileSetup,
  } = useTenantOnboardingStore();

  const [currentFormData, setCurrentFormData] = useState<any>({});

  useEffect(() => {
    initializeOnboarding();
  }, [initializeOnboarding]);

  const handleNext = async () => {
    try {
      let response;

      switch (currentStep) {
        case OnboardingStep.EMAIL_VERIFICATION:
          if (isEmailVerified) {
            await completeOnboardingStep('email_verification');
            setCurrentStep(OnboardingStep.TENANT_SETUP);
            message.success('Email đã được xác thực!');
          } else {
            message.warning('Vui lòng xác thực email trước khi tiếp tục');
          }
          break;

        case OnboardingStep.TENANT_SETUP:
          response = await submitTenantSetup(currentFormData);
          if (response.success) {
            await completeOnboardingStep('tenant_setup', currentFormData);
            setCurrentStep(OnboardingStep.WEBSITE_SETUP);
            message.success('Thiết lập tenant thành công!');
          } else {
            message.error(response.message || 'Có lỗi xảy ra');
          }
          break;

        case OnboardingStep.WEBSITE_SETUP:
          response = await submitWebsiteSetup(currentFormData);
          if (response.success) {
            await completeOnboardingStep('website_setup', currentFormData);
            // Initialize onboarding tracking after website is created
            await initializeOnboardingTracking();
            setCurrentStep(OnboardingStep.TEMPLATE_SELECTION);
            message.success('Tạo website thành công!');
          } else {
            message.error(response.message || 'Có lỗi xảy ra');
          }
          break;

        case OnboardingStep.TEMPLATE_SELECTION:
          if (selectedTemplateId) {
            await completeOnboardingStep('template_selection', { template_id: selectedTemplateId });
          } else {
            await skipOnboardingStep('template_selection', 'User chose to skip template selection');
          }
          setCurrentStep(OnboardingStep.TUTORIAL);
          message.success('Đã hoàn thành chọn template!');
          break;

        case OnboardingStep.TUTORIAL:
          await completeOnboardingStep('tutorial');
          setCurrentStep(OnboardingStep.COMPLETED);
          message.success('Hoàn thành onboarding!');
          break;

        default:
          break;
      }
    } catch (error) {
      message.error('Có lỗi xảy ra, vui lòng thử lại');
    }
  };

  const handleSkip = async () => {
    try {
      switch (currentStep) {
        case OnboardingStep.TEMPLATE_SELECTION:
          await skipOnboardingStep('template_selection', 'User chose to skip');
          setCurrentStep(OnboardingStep.TUTORIAL);
          message.info('Đã bỏ qua chọn template');
          break;
        case OnboardingStep.TUTORIAL:
          await skipOnboardingStep('tutorial', 'User chose to skip');
          setCurrentStep(OnboardingStep.COMPLETED);
          message.info('Đã bỏ qua hướng dẫn');
          break;
        default:
          break;
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi bỏ qua bước');
    }
  };

  const handleBack = () => {
    switch (currentStep) {
      case OnboardingStep.TENANT_SETUP:
        setCurrentStep(OnboardingStep.EMAIL_VERIFICATION);
        break;
      case OnboardingStep.WEBSITE_SETUP:
        setCurrentStep(OnboardingStep.TENANT_SETUP);
        break;
      case OnboardingStep.TEMPLATE_SELECTION:
        setCurrentStep(OnboardingStep.WEBSITE_SETUP);
        break;
      case OnboardingStep.TUTORIAL:
        setCurrentStep(OnboardingStep.TEMPLATE_SELECTION);
        break;
      default:
        break;
    }
  };

  const handleFormChange = (data: any) => {
    setCurrentFormData(data);
  };

  const handleEmailVerificationComplete = () => {
    setEmailVerified(true);
    handleNext();
  };

  const handleTemplateSelect = (templateId: number | null) => {
    setSelectedTemplateId(templateId);
    handleNext();
  };

  const handleTutorialComplete = () => {
    handleNext();
  };

  const handleNavigateToDashboard = () => {
    navigate('/dashboard');
  };

  const canGoBack = () => {
    return currentStep !== OnboardingStep.EMAIL_VERIFICATION && currentStep !== OnboardingStep.COMPLETED;
  };

  const canSkip = () => {
    return currentStep === OnboardingStep.TEMPLATE_SELECTION || currentStep === OnboardingStep.TUTORIAL;
  };

  const renderCurrentForm = () => {
    switch (currentStep) {
      case OnboardingStep.EMAIL_VERIFICATION:
        return (
          <EmailVerificationForm
            isVerified={isEmailVerified}
            onVerificationComplete={handleEmailVerificationComplete}
            loading={submitting}
          />
        );

      case OnboardingStep.TENANT_SETUP:
        return (
          <TenantSetupForm
            initialData={tenantData}
            onSubmit={handleFormChange}
            loading={submitting}
          />
        );

      case OnboardingStep.WEBSITE_SETUP:
        return (
          <WebsiteSetupForm
            initialData={websiteData}
            onSubmit={handleFormChange}
            loading={submitting}
          />
        );

      case OnboardingStep.TEMPLATE_SELECTION:
        return (
          <TemplateSelectionForm
            onSubmit={handleTemplateSelect}
            onSkip={handleSkip}
            loading={submitting}
          />
        );

      case OnboardingStep.TUTORIAL:
        return (
          <TutorialForm
            onComplete={handleTutorialComplete}
            onSkip={handleSkip}
            loading={submitting}
          />
        );

      case OnboardingStep.COMPLETED:
        return (
          <OnboardingComplete
            tenantData={tenantData}
            websiteData={websiteData}
            onNavigateToDashboard={handleNavigateToDashboard}
          />
        );

      default:
        return <div>Unknown step</div>;
    }
  };

  if (loading) {
    return (
      <Layout>
        <Content style={{ padding: '24px' }}>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <div>Đang tải...</div>
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout>
      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          {/* Progress indicator */}
          <Card style={{ marginBottom: '24px' }}>
            <OnboardingProgress currentStep={currentStep} />
          </Card>

          {/* Main form */}
          {renderCurrentForm()}

          {/* Navigation buttons */}
          {currentStep !== OnboardingStep.COMPLETED && (
            <Card style={{ marginTop: '24px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={handleBack}
                  disabled={!canGoBack() || submitting}
                >
                  Quay lại
                </Button>

                <Space>
                  {canSkip() && (
                    <Button onClick={handleSkip} disabled={submitting}>
                      Bỏ qua
                    </Button>
                  )}
                  
                  <Button
                    type="primary"
                    icon={<ArrowRightOutlined />}
                    onClick={handleNext}
                    loading={submitting}
                    disabled={currentStep === OnboardingStep.EMAIL_VERIFICATION && !isEmailVerified}
                  >
                    {currentStep === OnboardingStep.TUTORIAL ? 'Hoàn thành' : 'Tiếp tục'}
                  </Button>
                </Space>
              </div>
            </Card>
          )}
        </div>
      </Content>
    </Layout>
  );
};

export default TenantOnboardingForm;
