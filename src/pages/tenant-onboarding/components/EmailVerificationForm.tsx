import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Button, 
  Space, 
  Result, 
  Spin, 
  message,
  Alert 
} from 'antd';
import { 
  CheckCircleOutlined, 
  MailOutlined, 
  ReloadOutlined 
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface EmailVerificationFormProps {
  userEmail?: string;
  isVerified?: boolean;
  onVerificationComplete: () => void;
  onResendEmail?: () => void;
  loading?: boolean;
}

const EmailVerificationForm: React.FC<EmailVerificationFormProps> = ({
  userEmail = '<EMAIL>',
  isVerified = false,
  onVerificationComplete,
  onResendEmail,
  loading = false,
}) => {
  const [isChecking, setIsChecking] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  const handleCheckVerification = async () => {
    setIsChecking(true);
    try {
      // Simulate API call to check verification status
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For demo purposes, randomly determine if verified
      const verified = Math.random() > 0.5;
      
      if (verified) {
        message.success('Email đã được xác thực thành công!');
        onVerificationComplete();
      } else {
        message.info('Email chưa được xác thực. Vui lòng kiểm tra hộp thư của bạn.');
      }
    } catch (error) {
      message.error('Có lỗi xảy ra khi kiểm tra xác thực email');
    } finally {
      setIsChecking(false);
    }
  };

  const handleResendEmail = async () => {
    if (resendCooldown > 0) return;
    
    try {
      if (onResendEmail) {
        await onResendEmail();
      }
      message.success('Email xác thực đã được gửi lại!');
      setResendCooldown(60); // 60 seconds cooldown
    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi lại email');
    }
  };

  if (isVerified) {
    return (
      <Card>
        <Result
          status="success"
          icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          title="Email đã được xác thực"
          subTitle={`Email ${userEmail} đã được xác thực thành công.`}
          extra={
            <Button type="primary" onClick={onVerificationComplete}>
              Tiếp tục
            </Button>
          }
        />
      </Card>
    );
  }

  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <MailOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '24px' }} />
        
        <Title level={3}>Xác thực địa chỉ email</Title>
        
        <Paragraph style={{ fontSize: '16px', marginBottom: '24px' }}>
          Chúng tôi đã gửi một email xác thực đến địa chỉ:
        </Paragraph>
        
        <Text strong style={{ fontSize: '18px', color: '#1890ff' }}>
          {userEmail}
        </Text>
        
        <Alert
          message="Kiểm tra hộp thư của bạn"
          description="Vui lòng kiểm tra hộp thư (bao gồm cả thư mục spam) và nhấp vào liên kết xác thực trong email."
          type="info"
          showIcon
          style={{ margin: '24px 0', textAlign: 'left' }}
        />
        
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Button
            type="primary"
            size="large"
            loading={isChecking}
            onClick={handleCheckVerification}
            style={{ minWidth: '200px' }}
          >
            {isChecking ? 'Đang kiểm tra...' : 'Tôi đã xác thực email'}
          </Button>
          
          <Space>
            <Text>Không nhận được email?</Text>
            <Button
              type="link"
              icon={<ReloadOutlined />}
              onClick={handleResendEmail}
              disabled={resendCooldown > 0}
              loading={loading}
            >
              {resendCooldown > 0 
                ? `Gửi lại sau ${resendCooldown}s` 
                : 'Gửi lại email'
              }
            </Button>
          </Space>
        </Space>
      </div>
    </Card>
  );
};

export default EmailVerificationForm;
