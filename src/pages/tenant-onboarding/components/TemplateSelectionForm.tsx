import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Spin,
  message,
  Image,
  Tag,
  Empty,
} from 'antd';
import {
  EyeOutlined,
  CheckOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { tenantOnboardingApi } from '../api';

const { Title, Text, Paragraph } = Typography;

interface WebsiteTemplate {
  id: number;
  name: string;
  description: string;
  preview_url?: string;
  thumbnail?: string;
  category: string;
}

interface TemplateSelectionFormProps {
  onSubmit: (templateId: number | null) => void;
  onSkip?: () => void;
  loading?: boolean;
}

const TemplateSelectionForm: React.FC<TemplateSelectionFormProps> = ({
  onSubmit,
  onSkip,
  loading = false,
}) => {
  const [templates, setTemplates] = useState<WebsiteTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [loadingTemplates, setLoadingTemplates] = useState(true);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoadingTemplates(true);
      const response = await tenantOnboardingApi.getWebsiteTemplates();
      
      // Mock data if API doesn't return templates
      const mockTemplates: WebsiteTemplate[] = [
        {
          id: 1,
          name: 'Blog Cá Nhân',
          description: 'Template đơn giản và thanh lịch cho blog cá nhân',
          category: 'Personal',
          thumbnail: 'https://via.placeholder.com/300x200/1890ff/ffffff?text=Personal+Blog',
          preview_url: '#',
        },
        {
          id: 2,
          name: 'Blog Công Nghệ',
          description: 'Template hiện đại cho blog công nghệ và lập trình',
          category: 'Technology',
          thumbnail: 'https://via.placeholder.com/300x200/52c41a/ffffff?text=Tech+Blog',
          preview_url: '#',
        },
        {
          id: 3,
          name: 'Blog Doanh Nghiệp',
          description: 'Template chuyên nghiệp cho blog doanh nghiệp',
          category: 'Business',
          thumbnail: 'https://via.placeholder.com/300x200/722ed1/ffffff?text=Business+Blog',
          preview_url: '#',
        },
        {
          id: 4,
          name: 'Blog Du Lịch',
          description: 'Template sống động cho blog du lịch và khám phá',
          category: 'Travel',
          thumbnail: 'https://via.placeholder.com/300x200/fa8c16/ffffff?text=Travel+Blog',
          preview_url: '#',
        },
      ];

      setTemplates(response.data?.data || mockTemplates);
    } catch (error) {
      console.error('Failed to load templates:', error);
      message.error('Không thể tải danh sách template');
    } finally {
      setLoadingTemplates(false);
    }
  };

  const handleTemplateSelect = (templateId: number) => {
    setSelectedTemplate(templateId);
  };

  const handleSubmit = () => {
    onSubmit(selectedTemplate);
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      Personal: 'blue',
      Technology: 'green',
      Business: 'purple',
      Travel: 'orange',
      Fashion: 'pink',
      Food: 'red',
    };
    return colors[category] || 'default';
  };

  if (loadingTemplates) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '60px 0' }}>
          <Spin size="large" />
          <Paragraph style={{ marginTop: '16px' }}>
            Đang tải danh sách template...
          </Paragraph>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>
          <GlobalOutlined style={{ marginRight: '8px' }} />
          Chọn Template Website
        </Title>
        <Paragraph>
          Chọn một template phù hợp cho website của bạn. Bạn có thể thay đổi template sau này.
        </Paragraph>
      </div>

      {templates.length === 0 ? (
        <Empty
          description="Không có template nào"
          style={{ margin: '40px 0' }}
        />
      ) : (
        <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>
          {templates.map((template) => (
            <Col xs={24} sm={12} lg={8} key={template.id}>
              <Card
                hoverable
                className={selectedTemplate === template.id ? 'selected-template' : ''}
                style={{
                  border: selectedTemplate === template.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  position: 'relative',
                }}
                cover={
                  <div style={{ position: 'relative' }}>
                    <Image
                      alt={template.name}
                      src={template.thumbnail}
                      height={150}
                      style={{ objectFit: 'cover' }}
                      preview={false}
                    />
                    {selectedTemplate === template.id && (
                      <div
                        style={{
                          position: 'absolute',
                          top: '8px',
                          right: '8px',
                          background: '#1890ff',
                          borderRadius: '50%',
                          width: '24px',
                          height: '24px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <CheckOutlined style={{ color: 'white', fontSize: '12px' }} />
                      </div>
                    )}
                  </div>
                }
                actions={[
                  <Button
                    type="link"
                    icon={<EyeOutlined />}
                    onClick={() => window.open(template.preview_url, '_blank')}
                  >
                    Xem trước
                  </Button>,
                  <Button
                    type={selectedTemplate === template.id ? 'primary' : 'default'}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    {selectedTemplate === template.id ? 'Đã chọn' : 'Chọn'}
                  </Button>,
                ]}
                onClick={() => handleTemplateSelect(template.id)}
              >
                <Card.Meta
                  title={
                    <Space>
                      {template.name}
                      <Tag color={getCategoryColor(template.category)}>
                        {template.category}
                      </Tag>
                    </Space>
                  }
                  description={template.description}
                />
              </Card>
            </Col>
          ))}
        </Row>
      )}

      <div style={{ textAlign: 'center' }}>
        <Space size="large">
          <Button onClick={handleSkip} disabled={loading}>
            Bỏ qua
          </Button>
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={loading}
            disabled={!selectedTemplate}
          >
            {selectedTemplate ? 'Sử dụng template này' : 'Chọn một template'}
          </Button>
        </Space>
      </div>

      <style jsx>{`
        .selected-template {
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }
      `}</style>
    </Card>
  );
};

export default TemplateSelectionForm;
