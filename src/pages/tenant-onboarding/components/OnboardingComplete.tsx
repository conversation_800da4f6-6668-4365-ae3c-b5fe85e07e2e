import {
  CheckCircleOutlined,
  DashboardOutlined,
  EditOutlined,
  GlobalOutlined,
  SettingOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Button, Card, Col, Result, Row, Space, Typography } from 'antd';
import React from 'react';
import { TenantOnboarding, WebsiteSetup } from '../type';

const { Title, Text, Paragraph } = Typography;

interface OnboardingCompleteProps {
  tenantData: TenantOnboarding;
  websiteData: WebsiteSetup;
  onNavigateToDashboard: () => void;
  onNavigateToWebsite?: () => void;
}

const OnboardingComplete: React.FC<OnboardingCompleteProps> = ({
  tenantData,
  websiteData,
  onNavigateToDashboard,
  onNavigateToWebsite,
}) => {
  const { createdTenantId, createdWebsiteId } = useTenantOnboardingStore();

  useEffect(() => {
    const autoSelectTenantAndWebsite = async () => {
      try {
        // Auto select tenant if we have created tenant ID
        if (createdTenantId) {
          tenantService.setCurrentTenantIdFromUrl(createdTenantId);
          console.log('Auto selected tenant:', createdTenantId);
        }

        // Auto select website if we have created website ID
        if (createdWebsiteId) {
          // You can store website ID in localStorage or context as needed
          localStorage.setItem('selectedWebsiteId', createdWebsiteId);
          console.log('Auto selected website:', createdWebsiteId);
        }

        // Optionally fetch and set the first website if no specific website was created
        if (!createdWebsiteId && createdTenantId) {
          try {
            const websitesResponse = await tenantOnboardingApi.listWebsites();
            if (websitesResponse.data && websitesResponse.data.length > 0) {
              const firstWebsite = websitesResponse.data[0];
              localStorage.setItem('selectedWebsiteId', firstWebsite.website_id.toString());
              console.log('Auto selected first website:', firstWebsite.website_id);
            }
          } catch (error) {
            console.error('Failed to fetch websites:', error);
          }
        }
      } catch (error) {
        console.error('Failed to auto select tenant/website:', error);
      }
    };

    autoSelectTenantAndWebsite();
  }, [createdTenantId, createdWebsiteId]);
  const quickActions = [
    {
      title: 'Viết bài đầu tiên',
      description: 'Tạo nội dung cho website của bạn',
      icon: <EditOutlined />,
      action: () => console.log('Navigate to create post'),
    },
    {
      title: 'Mời thành viên',
      description: 'Thêm đồng nghiệp vào workspace',
      icon: <TeamOutlined />,
      action: () => console.log('Navigate to invite members'),
    },
    {
      title: 'Tùy chỉnh website',
      description: 'Thay đổi giao diện và cài đặt',
      icon: <SettingOutlined />,
      action: () => console.log('Navigate to website settings'),
    },
  ];

  return (
    <div className="max-w-4xl mx-auto text-center">
      <Result
        icon={<CheckCircleOutlined className="text-green-500" />}
        title={
          <Title level={2} className="text-green-600">
            🎉 Chúc mừng! Bạn đã hoàn thành thiết lập
          </Title>
        }
        subTitle={
          <div className="max-w-2xl mx-auto">
            <Paragraph className="text-lg text-gray-600 mb-6">
              Workspace <strong>{tenantData.tenant_name}</strong> và website{' '}
              <strong>{websiteData.name}</strong> đã được tạo thành công. Bạn đã
              sẵn sàng để bắt đầu hành trình tạo nội dung!
            </Paragraph>
          </div>
        }
      />

      {/* Summary Cards */}
      <Row gutter={[24, 24]} className="mb-8">
        <Col xs={24} md={12}>
          <Card className="h-full">
            <Space direction="vertical" size="small" className="w-full">
              <Title level={4} className="mb-2">
                🏢 Workspace
              </Title>
              <Text strong>{tenantData.tenant_name}</Text>
              <Text type="secondary">Mã: {tenantData.tenant_code}</Text>
              {tenantData.type === 'company' && tenantData.company_info && (
                <>
                  <Text type="secondary">
                    Công ty: {tenantData.company_info.company_name}
                  </Text>
                  <Text type="secondary">
                    MST: {tenantData.company_info.tax_code}
                  </Text>
                </>
              )}
            </Space>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card className="h-full">
            <Space direction="vertical" size="small" className="w-full">
              <Title level={4} className="mb-2">
                🌐 Website
              </Title>
              <Text strong>{websiteData.name}</Text>
              <Text type="secondary">
                URL: {websiteData.subdomain}.yourdomain.com
              </Text>
              {websiteData.description && (
                <Text type="secondary">{websiteData.description}</Text>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Action Buttons */}
      <Space size="large" className="mb-8">
        <Button
          type="primary"
          size="large"
          icon={<DashboardOutlined />}
          onClick={onNavigateToDashboard}
          className="px-8"
        >
          Vào Dashboard
        </Button>
        {onNavigateToWebsite && (
          <Button
            size="large"
            icon={<GlobalOutlined />}
            onClick={onNavigateToWebsite}
            className="px-8"
          >
            Xem Website
          </Button>
        )}
      </Space>

      {/* Quick Actions */}
      <Card title="🚀 Bước tiếp theo" className="text-left">
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} md={8} key={index}>
              <Card
                size="small"
                hoverable
                onClick={action.action}
                className="h-full cursor-pointer transition-all hover:shadow-md"
              >
                <Space direction="vertical" size="small" className="w-full">
                  <div className="text-2xl text-blue-500">{action.icon}</div>
                  <Title level={5} className="mb-1">
                    {action.title}
                  </Title>
                  <Text type="secondary" className="text-sm">
                    {action.description}
                  </Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* Tips */}
      <div className="bg-blue-50 p-6 rounded-lg mt-8">
        <Space direction="vertical" size="middle" className="w-full">
          <Title level={4} className="text-blue-700 mb-0">
            💡 Mẹo để bắt đầu
          </Title>
          <div className="text-left">
            <ul className="text-blue-600 space-y-2">
              <li>
                <strong>Khám phá Dashboard:</strong> Làm quen với các tính năng
                chính
              </li>
              <li>
                <strong>Tạo nội dung đầu tiên:</strong> Viết bài blog hoặc tạo
                trang giới thiệu
              </li>
              <li>
                <strong>Tùy chỉnh website:</strong> Thay đổi theme và cài đặt
                theo ý muốn
              </li>
              <li>
                <strong>Mời đồng nghiệp:</strong> Cộng tác cùng team để tạo nội
                dung tốt hơn
              </li>
            </ul>
          </div>
        </Space>
      </div>
    </div>
  );
};

export default OnboardingComplete;
