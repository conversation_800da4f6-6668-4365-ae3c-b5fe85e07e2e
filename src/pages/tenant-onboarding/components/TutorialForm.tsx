import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Steps,
  Row,
  Col,
  Alert,
  Divider,
} from 'antd';
import {
  RocketOutlined,
  EditOutlined,
  SettingOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface TutorialFormProps {
  onComplete: () => void;
  onSkip?: () => void;
  loading?: boolean;
}

const TutorialForm: React.FC<TutorialFormProps> = ({
  onComplete,
  onSkip,
  loading = false,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const tutorialSteps = [
    {
      title: 'Tạo bài viết đầu tiên',
      description: '<PERSON>ọ<PERSON> cách tạo và xuất bản bài viết đầu tiên của bạn',
      icon: <EditOutlined />,
      content: (
        <div>
          <Paragraph>
            Bài viết là nội dung chính của blog. H<PERSON>y tạo bài viết đầu tiên để làm quen với trình soạn thảo.
          </Paragraph>
          <Alert
            message="Mẹo"
            description="Sử dụng tiêu đề hấp dẫn và nội dung chất lượng để thu hút độc giả."
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        </div>
      ),
    },
    {
      title: 'Tùy chỉnh giao diện',
      description: 'Cá nhân hóa giao diện website theo phong cách của bạn',
      icon: <SettingOutlined />,
      content: (
        <div>
          <Paragraph>
            Thay đổi màu sắc, font chữ, và bố cục để website phản ánh phong cách cá nhân của bạn.
          </Paragraph>
          <Alert
            message="Lưu ý"
            description="Giữ giao diện đơn giản và dễ đọc để mang lại trải nghiệm tốt cho người dùng."
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        </div>
      ),
    },
    {
      title: 'Quản lý người dùng',
      description: 'Thêm thành viên và phân quyền cho team của bạn',
      icon: <TeamOutlined />,
      content: (
        <div>
          <Paragraph>
            Mời các thành viên tham gia và phân quyền phù hợp để cùng nhau xây dựng nội dung.
          </Paragraph>
          <Alert
            message="Bảo mật"
            description="Chỉ cấp quyền cần thiết cho từng thành viên để đảm bảo an toàn."
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        </div>
      ),
    },
    {
      title: 'Hoàn thành thiết lập',
      description: 'Kiểm tra và hoàn thiện các cài đặt cuối cùng',
      icon: <CheckCircleOutlined />,
      content: (
        <div>
          <Paragraph>
            Xem lại tất cả các cài đặt và đảm bảo website đã sẵn sàng để ra mắt.
          </Paragraph>
          <Alert
            message="Chúc mừng!"
            description="Bạn đã hoàn thành quá trình thiết lập. Website của bạn đã sẵn sàng!"
            type="success"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        </div>
      ),
    },
  ];

  const handleStepComplete = (stepIndex: number) => {
    if (!completedSteps.includes(stepIndex)) {
      setCompletedSteps([...completedSteps, stepIndex]);
    }
    
    if (stepIndex < tutorialSteps.length - 1) {
      setCurrentStep(stepIndex + 1);
    }
  };

  const handleComplete = () => {
    onComplete();
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    }
  };

  const isAllStepsCompleted = completedSteps.length === tutorialSteps.length;

  return (
    <Card>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>
          <RocketOutlined style={{ marginRight: '8px' }} />
          Hướng dẫn sử dụng
        </Title>
        <Paragraph>
          Hoàn thành các bước hướng dẫn để làm quen với hệ thống và tận dụng tối đa các tính năng.
        </Paragraph>
      </div>

      <Row gutter={24}>
        <Col xs={24} lg={8}>
          <Steps
            direction="vertical"
            current={currentStep}
            style={{ height: '400px' }}
          >
            {tutorialSteps.map((step, index) => (
              <Step
                key={index}
                title={step.title}
                description={step.description}
                icon={completedSteps.includes(index) ? <CheckCircleOutlined /> : step.icon}
                status={
                  completedSteps.includes(index)
                    ? 'finish'
                    : index === currentStep
                    ? 'process'
                    : 'wait'
                }
              />
            ))}
          </Steps>
        </Col>

        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                {tutorialSteps[currentStep]?.icon}
                {tutorialSteps[currentStep]?.title}
              </Space>
            }
            style={{ minHeight: '400px' }}
          >
            {tutorialSteps[currentStep]?.content}

            <Divider />

            <div style={{ textAlign: 'center' }}>
              <Space>
                {currentStep > 0 && (
                  <Button onClick={() => setCurrentStep(currentStep - 1)}>
                    Quay lại
                  </Button>
                )}
                
                {!completedSteps.includes(currentStep) && (
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={() => handleStepComplete(currentStep)}
                  >
                    Hoàn thành bước này
                  </Button>
                )}

                {currentStep < tutorialSteps.length - 1 && completedSteps.includes(currentStep) && (
                  <Button
                    type="primary"
                    onClick={() => setCurrentStep(currentStep + 1)}
                  >
                    Bước tiếp theo
                  </Button>
                )}
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      <Divider />

      <div style={{ textAlign: 'center' }}>
        <Space size="large">
          <Button onClick={handleSkip} disabled={loading}>
            Bỏ qua hướng dẫn
          </Button>
          
          <Button
            type="primary"
            onClick={handleComplete}
            loading={loading}
            disabled={!isAllStepsCompleted}
          >
            {isAllStepsCompleted ? 'Hoàn thành onboarding' : 'Hoàn thành tất cả bước trước'}
          </Button>
        </Space>
      </div>
    </Card>
  );
};

export default TutorialForm;
