# Tenant Onboarding Testing Guide

## Overview
This guide provides comprehensive testing instructions for the tenant onboarding module implementation.

## Test Environment Setup

### Prerequisites
1. Backend API server running on `http://localhost:9033`
2. Valid admin credentials:
   - Email: `<EMAIL>`
   - Password: `12345678`

### Test Data
```json
{
  "tenant": {
    "tenant_name": "Test Company",
    "tenant_type": "company",
    "company_name": "Test Company Ltd",
    "tax_code": "*********",
    "legal_representative": "John Doe"
  },
  "website": {
    "name": "Test Blog",
    "subdomain": "testblog",
    "description": "A test blog website"
  }
}
```

## Test Cases

### 1. Complete Onboarding Flow Test

#### Step 1: Email Verification
- **Action**: Navigate to `/tenant-onboarding/setup`
- **Expected**: Email verification form is displayed
- **Test**: 
  - Click "Tôi đã xác thực email" button
  - Verify success message appears
  - Verify progression to tenant setup step

#### Step 2: Tenant Setup
- **Action**: Fill out tenant information form
- **Test Data**: Use tenant data from above
- **Expected**: 
  - Form validation works correctly
  - Auto-generation of tenant code
  - Successful submission moves to website setup

#### Step 3: Website Setup
- **Action**: Fill out website information form
- **Test Data**: Use website data from above
- **Expected**:
  - Form validation works correctly
  - Subdomain validation
  - Successful submission moves to template selection

#### Step 4: Template Selection
- **Action**: Select a website template
- **Expected**:
  - Templates are loaded and displayed
  - Template selection works
  - Can skip this step
  - Moves to tutorial step

#### Step 5: Tutorial
- **Action**: Complete or skip tutorial
- **Expected**:
  - Tutorial steps are displayed
  - Can complete individual steps
  - Can skip entire tutorial
  - Moves to completion

#### Step 6: Completion
- **Action**: View completion screen
- **Expected**:
  - Success message displayed
  - Summary of created resources
  - Navigation to dashboard available

### 2. Error Handling Tests

#### Invalid Data Test
- **Action**: Submit forms with invalid data
- **Expected**: 
  - Proper validation messages
  - Form prevents submission
  - User-friendly error messages

#### Network Error Test
- **Action**: Disconnect network during API calls
- **Expected**:
  - Proper error handling
  - Retry mechanisms where appropriate
  - User feedback about network issues

#### API Error Test
- **Action**: Trigger API errors (duplicate tenant code, etc.)
- **Expected**:
  - Specific error messages based on error codes
  - Proper error recovery options

### 3. Navigation Tests

#### Back Navigation
- **Action**: Use back button between steps
- **Expected**:
  - Can navigate backwards through completed steps
  - Form data is preserved
  - Cannot go back from email verification

#### Skip Functionality
- **Action**: Skip optional steps
- **Expected**:
  - Only template selection and tutorial can be skipped
  - Required steps cannot be skipped
  - Proper progression after skipping

### 4. State Management Tests

#### Form Data Persistence
- **Action**: Fill forms and navigate between steps
- **Expected**:
  - Form data is preserved when navigating
  - State is maintained across component re-renders

#### Progress Tracking
- **Action**: Complete steps and check progress
- **Expected**:
  - Progress indicator updates correctly
  - Completion percentage is accurate
  - Step status is properly tracked

## API Integration Tests

### 1. Health Check
```bash
curl -X GET http://localhost:9033/api/admin/v1/onboarding/health
```

### 2. Generate Tenant Code
```bash
curl -X GET "http://localhost:9033/api/admin/v1/onboarding/generate-tenant-code?tenant_name=Test%20Company" \
  -H "Authorization: Bearer <token>"
```

### 3. Create Tenant
```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/tenant \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_type": "company",
    "tenant_name": "Test Company",
    "company_name": "Test Company Ltd",
    "tax_code": "*********",
    "legal_representative": "John Doe"
  }'
```

### 4. Create Website
```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/website \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Blog",
    "subdomain": "testblog",
    "description": "A test blog website"
  }'
```

### 5. Initialize Onboarding
```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/initialize \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>"
```

### 6. Complete Step
```bash
curl -X POST http://localhost:9033/api/admin/v1/onboarding/steps/complete \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "step_name": "email_verification"
  }'
```

### 7. Get Status
```bash
curl -X GET http://localhost:9033/api/admin/v1/onboarding/status \
  -H "Authorization: Bearer <token>" \
  -H "X-Tenant-ID: <tenant_id>" \
  -H "X-Website-ID: <website_id>"
```

## Performance Tests

### Load Time
- **Metric**: Initial page load time
- **Target**: < 2 seconds
- **Test**: Measure time from navigation to first meaningful paint

### API Response Time
- **Metric**: API call response times
- **Target**: < 1 second for most calls
- **Test**: Monitor network tab during onboarding flow

### Memory Usage
- **Metric**: Browser memory consumption
- **Target**: No memory leaks during flow
- **Test**: Monitor memory usage through complete flow

## Accessibility Tests

### Keyboard Navigation
- **Test**: Complete entire flow using only keyboard
- **Expected**: All interactive elements are accessible

### Screen Reader
- **Test**: Use screen reader to navigate
- **Expected**: Proper ARIA labels and descriptions

### Color Contrast
- **Test**: Check color contrast ratios
- **Expected**: WCAG AA compliance

## Browser Compatibility

### Desktop Browsers
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

### Mobile Browsers
- Chrome Mobile
- Safari Mobile
- Samsung Internet

## Reporting Issues

When reporting issues, include:
1. Browser and version
2. Steps to reproduce
3. Expected vs actual behavior
4. Console errors (if any)
5. Network requests (if relevant)

## Success Criteria

The onboarding implementation is considered successful when:
1. All test cases pass
2. Error handling works correctly
3. Performance targets are met
4. Accessibility requirements are satisfied
5. Cross-browser compatibility is confirmed
