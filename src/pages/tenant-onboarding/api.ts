import { apiService } from '../../services/api.service';
import {
  OnboardingProgress,
  ProfileSetup,
  TenantOnboarding,
  WebsiteSetup,
} from './type';

interface StatusResponse {
  code: number;
  message: string;
  success: boolean;
  path: string;
  timestamp: string;
}

interface TenantOnboardingResponseData {
  tenant_id: string;
  tenant_name: string;
  tenant_code: string;
  tenant_type: 'individual' | 'company';
  status: string;
}

interface WebsiteSetupResponseData {
  website_id: string;
  name: string;
  subdomain: string;
  tenant_id: string;
  status: string;
}

interface ProfileSetupResponseData {
  user_id: string;
  full_name: string;
  phone?: string;
  address?: string;
  avatar?: string;
}

interface OnboardingStatusResponseData {
  user_id: string;
  onboarding_status: 'not_started' | 'in_progress' | 'completed';
  onboarding_step: string | null;
  progress: OnboardingProgress[];
}

interface TenantOnboardingResponse extends StatusResponse {
  data: TenantOnboardingResponseData;
}

interface WebsiteSetupResponse extends StatusResponse {
  data: WebsiteSetupResponseData;
}

interface ProfileSetupResponse extends StatusResponse {
  data: ProfileSetupResponseData;
}

interface OnboardingStatusResponse extends StatusResponse {
  data: OnboardingStatusResponseData;
}

export const tenantOnboardingApi = {
  // Tenant setup
  setupTenant: (data: TenantOnboarding) =>
    apiService.post<TenantOnboardingResponse>(
      '/api/admin/v1/onboarding/tenant',
      data,
    ),

  // Website setup
  setupWebsite: (data: WebsiteSetup) =>
    apiService.post<WebsiteSetupResponse>(
      '/api/admin/v1/onboarding/website',
      data,
    ),

  // Profile setup
  setupProfile: (data: ProfileSetup) =>
    apiService.post<ProfileSetupResponse>(
      '/api/admin/v1/onboarding/profile',
      data,
    ),

  // Get onboarding status
  getOnboardingStatus: () =>
    apiService.get<OnboardingStatusResponse>('/api/admin/v1/onboarding/status'),

  // Update onboarding step
  updateOnboardingStep: (
    step: string,
    status: 'completed' | 'skipped',
    data?: any,
  ) =>
    apiService.post<StatusResponse>('/api/admin/v1/onboarding/progress', {
      step,
      status,
      data,
    }),

  // Skip step
  skipStep: (step: string) =>
    apiService.post<StatusResponse>('/api/admin/v1/onboarding/skip', { step }),

  // Complete onboarding
  completeOnboarding: () =>
    apiService.post<StatusResponse>('/api/admin/v1/onboarding/complete'),

  // Get tenant name suggestions
  getTenantNameSuggestions: (query: string) =>
    apiService.get<any>(
      `/api/admin/v1/onboarding/tenant-suggestions?q=${query}`,
    ),

  // Get website templates
  getWebsiteTemplates: () =>
    apiService.get<any>('/api/admin/v1/onboarding/website-templates'),

  // List websites for current tenant
  listWebsites: () => apiService.get<any>('/api/admin/v1/website/websites'),

  // Legacy endpoints (for backward compatibility)
  getTenantStatus: (tenantId: string) =>
    apiService.get<TenantOnboardingResponse>(`/api/tenant/${tenantId}/status`),
};
