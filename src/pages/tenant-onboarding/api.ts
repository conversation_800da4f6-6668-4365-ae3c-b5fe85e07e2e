import { apiService } from '../../services/api.service';
import {
  OnboardingProgress,
  ProfileSetup,
  TenantOnboarding,
  WebsiteSetup,
} from './type';

interface StatusResponse {
  code: number;
  message: string;
  success: boolean;
  path: string;
  timestamp: string;
}

interface TenantOnboardingResponseData {
  tenant_id: string;
  tenant_name: string;
  tenant_code: string;
  tenant_type: 'individual' | 'company';
  status: string;
}

interface WebsiteSetupResponseData {
  website_id: string;
  name: string;
  subdomain: string;
  tenant_id: string;
  status: string;
}

interface ProfileSetupResponseData {
  user_id: string;
  full_name: string;
  phone?: string;
  address?: string;
  avatar?: string;
}

interface OnboardingStatusResponseData {
  user_id: string;
  onboarding_status: 'not_started' | 'in_progress' | 'completed';
  current_step: string | null;
  is_completed: boolean;
  completion_percentage: number;
  steps: OnboardingProgress[];
}

interface TenantCodeGenerationResponse {
  tenant_code: string;
  is_available: boolean;
}

interface OnboardingInitializeResponse {
  user_id: string;
  tenant_id: string;
  website_id: string;
  onboarding_status: string;
  current_step: string;
}

interface NextStepResponse {
  step_name: string;
  step_title: string;
  step_description: string;
  is_required: boolean;
  is_completed: boolean;
}

interface WebsiteTemplate {
  id: number;
  name: string;
  description: string;
  preview_url?: string;
  thumbnail?: string;
  category: string;
}

interface TenantNameSuggestion {
  name: string;
  code: string;
  is_available: boolean;
}

interface TenantOnboardingResponse extends StatusResponse {
  data: TenantOnboardingResponseData;
}

interface WebsiteSetupResponse extends StatusResponse {
  data: WebsiteSetupResponseData;
}

interface ProfileSetupResponse extends StatusResponse {
  data: ProfileSetupResponseData;
}

interface OnboardingStatusResponse extends StatusResponse {
  data: OnboardingStatusResponseData;
}

export const tenantOnboardingApi = {
  // Health check
  healthCheck: () =>
    apiService.get<StatusResponse>('/api/admin/v1/onboarding/health'),

  // Generate tenant code
  generateTenantCode: (tenantName: string) =>
    apiService.get<{
      status: StatusResponse;
      data: TenantCodeGenerationResponse;
    }>(
      `/api/admin/v1/onboarding/generate-tenant-code?tenant_name=${encodeURIComponent(tenantName)}`,
    ),

  // Tenant setup
  setupTenant: (data: TenantOnboarding) =>
    apiService.post<TenantOnboardingResponse>(
      '/api/admin/v1/onboarding/tenant',
      data,
    ),

  // Website setup
  setupWebsite: (data: WebsiteSetup) =>
    apiService.post<WebsiteSetupResponse>(
      '/api/admin/v1/onboarding/website',
      data,
    ),

  // Initialize onboarding tracking
  initializeOnboarding: () =>
    apiService.post<{
      status: StatusResponse;
      data: OnboardingInitializeResponse;
    }>('/api/admin/v1/onboarding/initialize'),

  // Get onboarding status
  getOnboardingStatus: () =>
    apiService.get<OnboardingStatusResponse>('/api/admin/v1/onboarding/status'),

  // Complete onboarding step
  completeOnboardingStep: (data: { step_name: string; data?: any }) =>
    apiService.post<StatusResponse>(
      '/api/admin/v1/onboarding/steps/complete',
      data,
    ),

  // Skip onboarding step
  skipOnboardingStep: (data: { step_name: string; reason?: string }) =>
    apiService.post<StatusResponse>(
      '/api/admin/v1/onboarding/steps/skip',
      data,
    ),

  // Get next step
  getNextStep: () =>
    apiService.get<{ status: StatusResponse; data: NextStepResponse }>(
      '/api/admin/v1/onboarding/next-step',
    ),

  // Get website templates
  getWebsiteTemplates: () =>
    apiService.get<{ status: StatusResponse; data: WebsiteTemplate[] }>(
      '/api/admin/v1/onboarding/templates',
    ),

  // Get tenant name suggestions
  getTenantNameSuggestions: (input: string) =>
    apiService.get<{ status: StatusResponse; data: TenantNameSuggestion[] }>(
      `/api/admin/v1/onboarding/suggestions/tenant-name?input=${encodeURIComponent(input)}`,
    ),

  // Profile setup (legacy)
  setupProfile: (data: ProfileSetup) =>
    apiService.post<ProfileSetupResponse>(
      '/api/admin/v1/onboarding/profile',
      data,
    ),

  // List websites for current tenant
  listWebsites: () => apiService.get<any>('/api/admin/v1/website/websites'),

  // Legacy endpoints (for backward compatibility)
  getTenantStatus: (tenantId: string) =>
    apiService.get<TenantOnboardingResponse>(`/api/tenant/${tenantId}/status`),
};
