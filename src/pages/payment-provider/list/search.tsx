import { Button, Col, Form, Input, Row, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import { MODULE, PROVIDER_TYPES } from '../config';
import { useNavigateTenant } from '../../../hooks';

interface Props {
  query: any;
  loading?: boolean;
  onChange?: (values: any) => void;
}

export default function Search({ query, loading, onChange }: Props) {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const initialValues = {
    name: query.name || '',
    code: query.code || '',
    provider_type: query.provider_type || '',
    is_active:
      query.is_active === 'true'
        ? true
        : query.is_active === 'false'
          ? false
          : '',
  };

  const onFinish = (values: any) => {
    if (onChange) {
      onChange(values);
    }
  };

  const handleReset = () => {
    form.resetFields();
    if (onChange) {
      onChange({});
    }
  };

  return (
    <div className="bg-white p-6 mb-4">
      <Form
        name="search"
        form={form}
        initialValues={initialValues}
        onFinish={onFinish}
        layout="vertical"
      >
        <Row gutter={24}>
          <Col xs={24} sm={12} lg={6}>
            <Form.Item label={t('Name')} name="name">
              <Input placeholder={t('Enter provider name')} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Form.Item label={t('Code')} name="code">
              <Input placeholder={t('Enter provider code')} />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Form.Item label={t('Provider Type')} name="provider_type">
              <Select
                placeholder={t('Select provider type')}
                allowClear
                options={PROVIDER_TYPES}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Form.Item label={t('Status')} name="is_active">
              <Select
                placeholder={t('Select status')}
                allowClear
                options={[
                  { label: t('Active'), value: true },
                  { label: t('Inactive'), value: false },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row justify="end">
          <Col>
            <Button
              type="default"
              onClick={handleReset}
              className="mr-2"
              disabled={loading}
            >
              {t('Reset')}
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className="ml-2"
              loading={loading}
              disabled={loading}
            >
              {t('Search')}
            </Button>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
