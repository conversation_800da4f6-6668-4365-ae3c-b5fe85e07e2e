import {
  CheckCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { Button, Col, message, Popconfirm, Row, Space, Table, Tag } from 'antd';
import dayjs from 'dayjs';
import queryString from 'query-string';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { deleteItem, getItems, toggleStatus } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP, PROVIDER_TYPES } from '../config';
import { useNavigateTenant } from '../../../hooks';
import ModalForm from '../form/modal';
import usePaymentProviderStore from '../store';
import { Provider } from '../type';
import Search from './search';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading, setLoading } = usePaymentProviderStore();

  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });

  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<Provider[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  async function fetchData(payload?: any) {
    setLoading(true);
    try {
      const params = {
        ...query,
        ...filters,
        ...payload,
      };
      delete params.total;

      const response = await getItems(params);
      if (response.status.success) {
        setItems(response.data.items || []);
        setTotal(response.meta.total);
        setNextCursor(response.meta.next_cursor);
      } else {
        message.error(response.status.message);
      }
    } finally {
      setLoading(false);
    }
  }

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData(values);
  };

  useEffectOnce(() => {
    fetchData();
  });

  const handleDelete = async (id: string) => {
    try {
      const res = await deleteItem(id);
      if (res.status.success) {
        message.success(t('deleteSuccess'));
        fetchData();
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      message.error(t('deleteError'));
    }
  };

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const res = await toggleStatus(id, !isActive);
      if (res.status.success) {
        message.success(t('statusUpdateSuccess'));
        fetchData();
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      message.error(t('statusUpdateError'));
    }
  };

  const handleActions = (action: string, record?: Provider) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record?.provider_id.toString());
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record?.provider_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const getProviderTypeLabel = (type: string) => {
    const providerType = PROVIDER_TYPES.find((pt) => pt.value === type);
    return providerType ? providerType.label : type;
  };

  const columns = [
    {
      title: t('ID'),
      dataIndex: 'provider_id',
      key: 'provider_id',
      width: '80px',
    },
    {
      title: t('Name'),
      dataIndex: 'name',
      key: 'name',
      render: (dom: any, record: Provider) => (
        <div
          className="text-blue-600 cursor-pointer"
          onClick={() => navigate(`/${MODULE}/${record.provider_id}`)}
        >
          {dom}
        </div>
      ),
    },
    {
      title: t('Code'),
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: t('Provider Type'),
      dataIndex: 'provider_type',
      key: 'provider_type',
      render: (type: string) => <span>{getProviderTypeLabel(type)}</span>,
    },
    {
      title: t('Status'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? t('Active') : t('Inactive')}
        </Tag>
      ),
    },
    {
      title: t('Created At'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => (
        <span>{dayjs(date).format('DD/MM/YYYY HH:mm')}</span>
      ),
    },
    {
      title: t('Actions'),
      key: 'action',
      render: (text: any, record: Provider) => (
        <Space size="small">
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => handleActions('edit', record)}
          >
            {t('Edit')}
          </Button>
          <Button
            type={record.is_active ? 'default' : 'primary'}
            icon={record.is_active ? <StopOutlined /> : <CheckCircleOutlined />}
            onClick={() =>
              handleToggleStatus(
                record.provider_id.toString(),
                record.is_active,
              )
            }
          >
            {record.is_active ? t('Deactivate') : t('Activate')}
          </Button>
          <Popconfirm
            title={t('deleteConfirmation')}
            description={t('deleteConfirmationDesc')}
            onConfirm={() => handleDelete(record.provider_id.toString())}
            okText={t('Yes')}
            cancelText={t('No')}
          >
            <Button type="default" danger icon={<DeleteOutlined />}>
              {t('Delete')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{t('module')}</div>
        <div className="gap-4">
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={() => handleActions('add')}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <Search query={query} loading={loading} onChange={handleFilters} />
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="provider_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        />
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          />
        )}
      </Space>
    </div>
  );
}
