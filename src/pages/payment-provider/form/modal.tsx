import { Modal } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';
import { useNavigateTenant } from '../../../hooks';
import ProviderForm from './index';

interface ModalFormProps {
  showModal: boolean;
  onChange: () => void;
  id?: string;
}

export default function ModalForm({ showModal, onChange, id }: ModalFormProps) {
  const { t } = useTranslation(MODULE);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    setIsModalOpen(showModal);
  }, [showModal]);

  const handleCancel = () => {
    setIsModalOpen(false);
    onChange();
  };

  return (
    <Modal
      title={id ? t('editProvider') : t('createProvider')}
      open={isModalOpen}
      onCancel={handleCancel}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <ProviderForm />
    </Modal>
  );
}
