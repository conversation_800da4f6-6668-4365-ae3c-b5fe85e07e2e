import {
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
  Switch,
} from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import JSONInput from 'react-json-editor-ajrm';
import locale from 'react-json-editor-ajrm/locale/en';
import { useParams } from 'react-router-dom';

import { useNavigateTenant } from '../../../hooks';
import { createItem, getItem, updateItem } from '../api';
import { MODULE, PROVIDER_TYPES } from '../config';
import { useNavigateTenant } from '../../../hooks';
import usePaymentProviderStore from '../store';
import { Provider } from '../type';

export default function ProviderForm() {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();
  const navigate = useNavigateTenant();
  const { id } = useParams();
  const { loading, setLoading } = usePaymentProviderStore();

  const [apiConfig, setApiConfig] = useState<Record<string, any>>({});
  const [webhookConfig, setWebhookConfig] = useState<Record<string, any>>({});
  const [initialValues, setInitialValues] = useState<Provider | null>(null);

  useEffect(() => {
    if (id && id !== 'create') {
      fetchData(id);
    }
  }, [id]);

  const fetchData = async (providerId: string) => {
    setLoading(true);
    try {
      const res = await getItem(providerId);
      if (res.status.success) {
        const provider = res.data;
        setInitialValues(provider);
        form.setFieldsValue({
          name: provider.name,
          code: provider.code,
          description: provider.description || '',
          is_active: provider.is_active,
          provider_type: provider.provider_type,
        });
        setApiConfig(provider.api_config || {});
        setWebhookConfig(provider.webhook_config || {});
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      message.error(t('errorFetchingProvider'));
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      const payload = {
        ...values,
        api_config: apiConfig,
        webhook_config: webhookConfig,
      };

      let res;
      if (id && id !== 'create') {
        res = await updateItem(id, payload);
        if (res.status.success) {
          message.success(t('updateSuccess'));
          navigate(`/${MODULE}`);
        } else {
          message.error(res.status.message);
        }
      } else {
        res = await createItem(payload);
        if (res.status.success) {
          message.success(t('createSuccess'));
          navigate(`/${MODULE}`);
        } else {
          message.error(res.status.message);
        }
      }
    } catch (error) {
      message.error(t('errorSubmittingForm'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(`/${MODULE}`);
  };

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">
          {id && id !== 'create' ? t('editProvider') : t('createProvider')}
        </div>
      </div>
      <Card className="mt-4">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            is_active: true,
            ...initialValues,
          }}
        >
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                label={t('Name')}
                name="name"
                rules={[{ required: true, message: t('nameRequired') }]}
              >
                <Input placeholder={t('enterName')} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label={t('Code')}
                name="code"
                rules={[{ required: true, message: t('codeRequired') }]}
              >
                <Input placeholder={t('enterCode')} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label={t('Provider Type')}
                name="provider_type"
                rules={[{ required: true, message: t('providerTypeRequired') }]}
              >
                <Select
                  options={PROVIDER_TYPES}
                  placeholder={t('selectProviderType')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                label={t('Active Status')}
                name="is_active"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item label={t('Description')} name="description">
                <Input.TextArea rows={4} placeholder={t('enterDescription')} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item label={t('API Configuration')}>
                <JSONInput
                  id="api_config"
                  placeholder={apiConfig}
                  locale={locale}
                  height="300px"
                  width="100%"
                  onChange={(value: any) => {
                    try {
                      if (value.jsObject) {
                        setApiConfig(value.jsObject);
                      }
                    } catch (error) {
                      console.error('Invalid JSON for API config');
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item label={t('Webhook Configuration')}>
                <JSONInput
                  id="webhook_config"
                  placeholder={webhookConfig}
                  locale={locale}
                  height="300px"
                  width="100%"
                  onChange={(value: any) => {
                    try {
                      if (value.jsObject) {
                        setWebhookConfig(value.jsObject);
                      }
                    } catch (error) {
                      console.error('Invalid JSON for webhook config');
                    }
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row justify="end">
            <Col>
              <Button onClick={handleCancel} style={{ marginRight: 8 }}>
                {t('Cancel')}
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {id && id !== 'create' ? t('Update') : t('Create')}
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
}
