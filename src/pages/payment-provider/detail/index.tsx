import {
  ArrowLeftOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Popconfirm,
  Row,
  Space,
  Tag,
  message,
} from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import J<PERSON><PERSON>retty from 'react-json-pretty';
import { useParams } from 'react-router-dom';

import { useNavigateTenant } from '../../../hooks';
import { deleteItem, getItem } from '../api';
import '../assets/styles.scss';
import { MODULE, PROVIDER_TYPES } from '../config';
import { useNavigateTenant } from '../../../hooks';
import usePaymentProviderStore from '../store';
import { Provider } from '../type';

export default function ProviderDetail() {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { id } = useParams();
  const { loading, setLoading } = usePaymentProviderStore();
  const [provider, setProvider] = useState<Provider | null>(null);

  useEffect(() => {
    if (id) {
      fetchData(id);
    }
  }, [id]);

  const fetchData = async (providerId: string) => {
    setLoading(true);
    try {
      const res = await getItem(providerId);
      if (res.status.success) {
        setProvider(res.data);
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      message.error(t('errorFetchingProvider'));
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const res = await deleteItem(id);
      if (res.status.success) {
        message.success(t('deleteSuccess'));
        navigate(`/${MODULE}`);
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      message.error(t('deleteError'));
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/${MODULE}/${id}/edit`);
  };

  const handleBack = () => {
    navigate(`/${MODULE}`);
  };

  const getProviderTypeLabel = (type: string) => {
    const providerType = PROVIDER_TYPES.find((pt) => pt.value === type);
    return providerType ? providerType.label : type;
  };

  const jsonPrettyTheme = {
    key: 'color:#268bd2',
    string: 'color:#859900',
    value: 'color:#cb4b16',
    boolean: 'color:#d33682',
  };

  if (!provider) {
    return null;
  }

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="flex items-center">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            className="mr-2"
          />
          <span className="text-xl font-bold">{t('providerDetails')}</span>
        </div>
        <div>
          <Space>
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
              {t('Edit')}
            </Button>
            <Popconfirm
              title={t('deleteConfirmation')}
              description={t('deleteConfirmationDesc')}
              onConfirm={handleDelete}
              okText={t('Yes')}
              cancelText={t('No')}
            >
              <Button type="default" danger icon={<DeleteOutlined />}>
                {t('Delete')}
              </Button>
            </Popconfirm>
          </Space>
        </div>
      </div>

      <div className="p-4">
        <Card loading={loading} className="mb-4">
          <Descriptions
            title={t('Basic Information')}
            bordered
            column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}
          >
            <Descriptions.Item label={t('ID')}>
              {provider.provider_id}
            </Descriptions.Item>
            <Descriptions.Item label={t('Name')}>
              {provider.name}
            </Descriptions.Item>
            <Descriptions.Item label={t('Code')}>
              {provider.code}
            </Descriptions.Item>
            <Descriptions.Item label={t('Provider Type')}>
              {getProviderTypeLabel(provider.provider_type)}
            </Descriptions.Item>
            <Descriptions.Item label={t('Status')}>
              <Tag color={provider.is_active ? 'green' : 'red'}>
                {provider.is_active ? t('Active') : t('Inactive')}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label={t('Created At')}>
              {dayjs(provider.created_at).format('DD/MM/YYYY HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label={t('Updated At')}>
              {dayjs(provider.updated_at).format('DD/MM/YYYY HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label={t('Description')} span={2}>
              {provider.description || t('No description provided')}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Row gutter={16}>
          <Col xs={24} md={12}>
            <Card title={t('API Configuration')} className="mb-4">
              {provider.api_config &&
              Object.keys(provider.api_config).length > 0 ? (
                <JSONPretty
                  data={provider.api_config}
                  theme={jsonPrettyTheme}
                  style={{ maxHeight: '400px', overflow: 'auto' }}
                />
              ) : (
                <div>{t('No API configuration provided')}</div>
              )}
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card title={t('Webhook Configuration')} className="mb-4">
              {provider.webhook_config &&
              Object.keys(provider.webhook_config).length > 0 ? (
                <JSONPretty
                  data={provider.webhook_config}
                  theme={jsonPrettyTheme}
                  style={{ maxHeight: '400px', overflow: 'auto' }}
                />
              ) : (
                <div>{t('No webhook configuration provided')}</div>
              )}
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
}
