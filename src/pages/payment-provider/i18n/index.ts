import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import { MODULE } from '../config';
import { useNavigateTenant } from '../../../hooks';
import en from './en.json';
import vi from './vi.json';

i18n.use(initReactI18next).init({
  resources: {
    en: {
      [MODULE]: en,
    },
    vi: {
      [MODULE]: vi,
    },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
