import { message } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import { createItem, getItem, updateItem } from '../api';
import CustomerGroupForm from './form';

export default function FormPage() {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigateTenant();
  const [initialValues, setInitialValues] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const isEdit = !!id;

  const fetchItem = async () => {
    if (!isEdit) return;

    try {
      setLoading(true);
      const res = await getItem(id);
      setInitialValues(res.data);
    } catch (error) {
      console.log(error);
      message.error(t('common:error_occurred'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItem();
  }, [id]);

  const handleFinish = async (values: any) => {
    try {
      setLoading(true);
      if (isEdit) {
        await updateItem(id!, values);
        message.success(t('common:updated_successfully'));
      } else {
        await createItem(values);
        message.success(t('common:created_successfully'));
      }
      navigate('/');
    } catch (error) {
      console.log(error);
      message.error(t('common:error_occurred'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <CustomerGroupForm
      isEdit={isEdit}
      initialValues={initialValues}
      onFinish={handleFinish}
      loading={loading}
    />
  );
}
