import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Row,
  Space,
  Switch,
  Typography,
} from 'antd';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const { Title } = Typography;

interface Props {
  isEdit?: boolean;
  initialValues?: any;
  onFinish: (values: any) => void;
  loading?: boolean;
}

export default function CustomerGroupForm({
  isEdit = false,
  initialValues,
  onFinish,
  loading = false,
}: Props) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const navigate = useNavigateTenant();

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues]);

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Card>
      <div style={{ marginBottom: '16px' }}>
        <Row>
          <Col span={24}>
            <Space>
              <Button onClick={handleGoBack} type="text">
                ← {t('common:back')}
              </Button>
              <Title level={4} style={{ margin: 0 }}>
                {isEdit
                  ? t('crm-customer-group:form.edit_title')
                  : t('crm-customer-group:form.create_title')}
              </Title>
            </Space>
          </Col>
        </Row>
      </div>

      <Form
        layout="vertical"
        form={form}
        onFinish={onFinish}
        initialValues={{
          is_active: true,
          discount_percentage: 0,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={t('crm-customer-group:form.name')}
              rules={[
                {
                  required: true,
                  message: t('common:field_required'),
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="discount_percentage"
              label={t('crm-customer-group:form.discount_percentage')}
              rules={[
                {
                  required: true,
                  message: t('common:field_required'),
                },
              ]}
            >
              <InputNumber
                min={0}
                max={100}
                style={{ width: '100%' }}
                formatter={(value) => `${value}%`}
                parser={(value) => value!.replace('%', '')}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label={t('crm-customer-group:form.description')}
        >
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item
          name="is_active"
          label={t('crm-customer-group:form.status')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? t('common:update') : t('common:create')}
            </Button>
            <Button onClick={handleGoBack}>{t('common:cancel')}</Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}
