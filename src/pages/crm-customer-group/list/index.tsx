import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  <PERSON>ton,
  Card,
  Col,
  message,
  Row,
  Space,
  Table,
  Tag,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import { deleteItem, getItems } from '../api';
import { useStore } from '../store';
import Search from './search';

const { Title } = Typography;

export default function List() {
  const { t } = useTranslation();
  const navigate = useNavigateTenant();
  const { items, setItems } = useStore((state) => state);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<any>({});

  const fetchItems = async (params = {}) => {
    try {
      setLoading(true);
      const res = await getItems({
        page: pagination.current,
        limit: pagination.pageSize,
        ...searchParams,
        ...params,
      });
      setItems(res.data || []);
      setPagination({
        ...pagination,
        total: res.meta?.total || 0,
      });
    } catch (error) {
      console.log(error);
      message.error(t('common:error_occurred'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, [pagination.current, pagination.pageSize, searchParams]);

  const handleTableChange = (pagination: any) => {
    setPagination({
      ...pagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const handleSearch = (values: any) => {
    setPagination({ ...pagination, current: 1 });
    setSearchParams(values);
  };

  const handleDelete = async (id: number) => {
    try {
      if (window.confirm(t('common:delete_confirmation'))) {
        await deleteItem(String(id));
        message.success(t('common:deleted_successfully'));
        fetchItems();
      }
    } catch (error) {
      console.log(error);
      message.error(t('common:error_occurred'));
    }
  };

  const columns = [
    {
      title: t('crm-customer-group:list.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('crm-customer-group:list.description'),
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '-',
    },
    {
      title: t('crm-customer-group:list.discount_percentage'),
      dataIndex: 'discount_percentage',
      key: 'discount_percentage',
      render: (text: number) => (
        <span className="customer-group-discount">{text}%</span>
      ),
    },
    {
      title: t('crm-customer-group:list.status'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (is_active: boolean) =>
        is_active ? (
          <Tag color="green">{t('common:active')}</Tag>
        ) : (
          <Tag color="red">{t('common:inactive')}</Tag>
        ),
    },
    {
      title: t('common:action'),
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/${record.group_id}`)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => navigate(`/edit/${record.group_id}`)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.group_id)}
          />
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Row style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Title level={4}>{t('crm-customer-group:title')}</Title>
        </Col>
        <Col span={12} style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/create')}
          >
            {t('common:create')}
          </Button>
        </Col>
      </Row>

      <Search onSearch={handleSearch} />
      <Table
        rowKey="group_id"
        columns={columns}
        dataSource={items}
        pagination={pagination}
        loading={loading}
        onChange={handleTableChange}
        style={{ marginTop: '16px' }}
      />
    </Card>
  );
}
