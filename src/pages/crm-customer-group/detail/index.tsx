import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import {
  <PERSON>ton,
  Card,
  Col,
  Descriptions,
  Row,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { deleteItem, getItem } from '../api';
import { useStore } from '../store';

const { Title } = Typography;

export default function Detail() {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigateTenant();
  const { item, setItem } = useStore((state) => state);
  const [loading, setLoading] = useState(false);

  const fetchItem = async () => {
    if (!id) return;
    try {
      setLoading(true);
      const res = await getItem(id);
      setItem(res.data);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItem();
    return () => {
      setItem(null);
    };
  }, [id]);

  const handleDelete = async () => {
    if (!id) return;
    try {
      if (
        window.confirm(
          t('common:delete_confirmation_description', { name: item?.name }),
        )
      ) {
        await deleteItem(id);
        navigate('/');
      }
    } catch (error) {
      console.log(error);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!item) {
    return null;
  }

  return (
    <Card>
      <div style={{ marginBottom: '16px' }}>
        <Row>
          <Col span={16}>
            <Space>
              <Button onClick={() => navigate('/')} type="text">
                ← {t('common:back')}
              </Button>
              <Title level={4} style={{ margin: 0 }}>
                {item.name}
              </Title>
              <span>{t('crm-customer-group:detail.title')}</span>
            </Space>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => navigate(`/edit/${id}`)}
              >
                {t('common:edit')}
              </Button>
              <Button danger icon={<DeleteOutlined />} onClick={handleDelete}>
                {t('common:delete')}
              </Button>
            </Space>
          </Col>
        </Row>
      </div>
      <Descriptions bordered column={2}>
        <Descriptions.Item label={t('crm-customer-group:detail.name')}>
          {item.name}
        </Descriptions.Item>
        <Descriptions.Item label={t('crm-customer-group:detail.status')}>
          {item.is_active ? (
            <Tag color="green">{t('common:active')}</Tag>
          ) : (
            <Tag color="red">{t('common:inactive')}</Tag>
          )}
        </Descriptions.Item>
        <Descriptions.Item
          label={t('crm-customer-group:detail.discount_percentage')}
        >
          {item.discount_percentage}%
        </Descriptions.Item>
        <Descriptions.Item label={t('crm-customer-group:detail.created_at')}>
          {formatDate(item.created_at)}
        </Descriptions.Item>
        <Descriptions.Item
          label={t('crm-customer-group:detail.description')}
          span={2}
        >
          {item.description || '-'}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
}
