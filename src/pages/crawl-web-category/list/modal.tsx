import { ReloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Modal } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { CrawlWebCategoryList } from './list';

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  showModal: boolean;
  id?: string;
  crawlWebId?: string;
}

const CrawlWebCategoryListModal: React.FC<IndexFormProps> = (props) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { showModal, onChange, id, crawlWebId } = props;
  const navigate = useNavigateTenant();
  const formRef = useRef<any>(null);

  const handleModal = () => {
    onChange(false);
  };

  function handleActions(action: string): void {
    logger('[handleActions]', action);
    if (action === 'save') {
      formRef.current?.submitForm();
    } else if (action === 'cancel') {
      onChange(false);
    }
  }
  return (
    <Modal
      title={t('module')}
      open={showModal}
      onCancel={handleModal}
      footer={false}
      className="form_modal"
      width={'90%'}
    >
      <CrawlWebCategoryList
        showHeader={false}
        crawlWebId={crawlWebId}
      ></CrawlWebCategoryList>
      <div className="form_footer flex gap-4">
        <Button
          type="dashed"
          icon={<ReloadOutlined />}
          onClick={() => handleActions('cancel')}
        >
          {t('btnCancel')}
        </Button>

        {/* <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={() => handleActions('save')}
                >
                    {t('btnSave')}
                </Button> */}
      </div>
    </Modal>
  );
};

export { CrawlWebCategoryListModal };
