import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { CrawlWebCategoryModal } from '../form/modal';
import useCrawlWebCategoryStore from '../store';
import { CrawlWebCategory } from '../type';
import { CrawlWebCategorySearch } from './search';

interface CrawlWebCategoryListProps {
  showHeader?: boolean;
  crawlWebId?: string;
}

function CrawlWebCategoryList({
  showHeader = true,
  crawlWebId,
}: CrawlWebCategoryListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useCrawlWebCategoryStore();

  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<CrawlWebCategory[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...payload,
      };

      if (crawlWebId) {
        params.crawlWebId = crawlWebId;
      }
      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.meta.total);
        setNextCursor(response.meta.cursors.after);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, crawlWebId],
  );

  useEffect(() => {
    fetchData({ after: afterKey });
  }, [fetchData, afterKey]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });

    setFilters(values);
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('form.deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    {
      title: t('form.id'),
      dataIndex: 'id',
      key: 'id',
      render: (dom: any, record: any) => <span>{dom}</span>,
    },
    {
      title: t('form.domain'),
      dataIndex: 'crawlWebId',
      key: 'crawlWebId',
      render: (dom: any, record: any) => (
        <span>{record.crawlWebId?.domain}</span>
      ),
    },
    {
      title: t('form.url'),
      dataIndex: 'url',
      key: 'url',
      render: (dom: any, record: any) => (
        <a href={dom} target="_blank" rel="noreferrer">
          {dom}
        </a>
      ),
    },

    {
      title: t('form.status'),
      dataIndex: 'status',
      key: 'status',
      render: (dom: any) => <span>{dom}</span>,
    },
    {
      title: t('list.actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('list.deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('list.btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('list.btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      {showHeader && (
        <div className="bg-gray flex justify-between p-4">
          <div className="text-xl font-bold w-full md:w-auto">
            {t('module')}
          </div>
          <div className="flex gap-4">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleActions('add', null)}
            >
              {t('list.btnAdd')}
            </Button>

            {/* <Button
            type="dashed"
            icon={<ImportOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('form.btnImport')}
          </Button>

          <Dropdown menu={{
            items: [
              {
                key: '1',
                label: <a href="#">Chức năng 1</a>,
              },
              {
                key: '2',
                label: <a href="#">Chức năng 2</a>,
              },
              {
                key: '3',
                label: <a href="#">Chức năng 3</a>,
              },
            ]
          }} trigger={['click']}>
            <Button icon={<EllipsisOutlined />} />
          </Dropdown> */}
          </div>
        </div>
      )}

      <CrawlWebCategorySearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></CrawlWebCategorySearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <CrawlWebCategoryModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></CrawlWebCategoryModal>
        )}
      </Space>
    </div>
  );
}
export { CrawlWebCategoryList };
