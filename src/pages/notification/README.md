# Notification Module

A comprehensive React module for managing user notifications with support for multiple channels, real-time updates, and advanced filtering capabilities.

## Features

- ✅ **Multi-channel notifications**: Email, SMS, Push, In-App, WebSocket, Telegram
- ✅ **Real-time updates**: Auto-refresh with configurable intervals
- ✅ **Advanced filtering**: Filter by status, channel, priority, and search
- ✅ **Bulk operations**: Mark multiple notifications as read or delete
- ✅ **Notification settings**: Customizable user preferences
- ✅ **Detailed view**: Full notification details with metadata
- ✅ **Unread counter**: Real-time unread count with badge
- ✅ **Statistics**: Comprehensive notification analytics
- ✅ **Responsive design**: Works on desktop and mobile
- ✅ **Internationalization**: English and Vietnamese support

## API Integration

Based on `docs-api/Notification Module` with the following endpoints:

- `GET /api/admin/v1/notifications` - Get all notifications with pagination
- `GET /api/admin/v1/notifications/:id` - Get notification by ID
- `PATCH /api/admin/v1/notifications/:id/read` - Mark notification as read
- `PATCH /api/admin/v1/notifications/mark-all-read` - Mark all notifications as read
- `DELETE /api/admin/v1/notifications/:id` - Delete notification
- `GET /api/admin/v1/notifications/unread-count` - Get unread count
- `GET /api/admin/v1/notifications/settings` - Get notification settings
- `PUT /api/admin/v1/notifications/settings` - Update notification settings

## File Structure

```
src/pages/notification/
├── api.ts                     # API service functions
├── config.ts                  # Configuration constants
├── type.ts                    # TypeScript interfaces
├── store.ts                   # Zustand state management
├── index.tsx                  # Main component with routing
├── README.md                  # This file
├── components/                # Reusable components
│   ├── index.tsx
│   ├── notification-item.tsx  # Individual notification card
│   ├── notification-list.tsx  # Main list component
│   ├── notification-filters.tsx # Search and filter controls
│   ├── notification-settings.tsx # Settings modal
│   ├── notification-detail.tsx # Detail modal
│   ├── unread-counter.tsx     # Unread count badge
│   ├── bulk-actions.tsx       # Bulk operation controls
│   └── notification-stats.tsx # Statistics dashboard
└── i18n/                      # Internationalization
    ├── en.json               # English translations
    └── vi.json               # Vietnamese translations
```

## Usage

### Basic Import

```tsx
import Notification from './pages/notification';

// In your router
<Route path="/notifications/*" element={<Notification />} />;
```

### Using Components

```tsx
import {
  UnreadCounter,
  NotificationItem,
  NotificationSettings
} from './pages/notification';

// Unread counter in header
<UnreadCounter onClick={() => navigate('/notifications')} />

// Individual notification
<NotificationItem
  notification={notification}
  onMarkAsRead={handleMarkAsRead}
  onDelete={handleDelete}
/>

// Settings modal
<NotificationSettings
  visible={settingsVisible}
  onClose={() => setSettingsVisible(false)}
/>
```

### Using Store

```tsx
import useNotificationStore from './pages/notification/store';

const MyComponent = () => {
  const {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    deleteNotification,
  } = useNotificationStore();

  // Use store state and actions
};
```

### API Functions

```tsx
import {
  getNotifications,
  markAsRead,
  deleteNotification,
  getUnreadCount,
  updateNotificationSettings,
} from './pages/notification/api';

// Get notifications with filters
const notifications = await getNotifications({
  page: 1,
  limit: 20,
  status: 'unread',
  channel: 'email',
});

// Mark as read
await markAsRead(notificationId);

// Get unread count
const { unread_count } = await getUnreadCount();
```

## Configuration

### Environment Variables

```env
# Notification API endpoints
REACT_APP_API_NOTIFICATIONS=/api/admin/v1/notifications
REACT_APP_API_UNREAD_COUNT=/api/admin/v1/notifications/unread-count
REACT_APP_API_MARK_ALL_READ=/api/admin/v1/notifications/mark-all-read
REACT_APP_API_SETTINGS=/api/admin/v1/notifications/settings
```

### Auto-refresh Settings

```typescript
// In config.ts
export const REFRESH_INTERVALS = {
  NOTIFICATIONS: 30000, // 30 seconds
  UNREAD_COUNT: 10000, // 10 seconds
} as const;
```

## Notification Types

### Channels

- **Email** - Email notifications
- **Push** - Mobile push notifications
- **SMS** - Text message notifications
- **In-App** - In-application notifications
- **WebSocket** - Real-time notifications
- **Telegram** - Telegram bot notifications

### Priorities

- **Low** - Non-urgent notifications
- **Normal** - Standard notifications
- **High** - Important notifications
- **Urgent** - Critical notifications requiring immediate attention

### Status

- **Unread** - New notifications
- **Read** - Viewed notifications
- **All** - Both read and unread

## Features in Detail

### Real-time Updates

- Auto-refresh notifications list every 30 seconds
- Auto-refresh unread count every 10 seconds
- Toggle auto-refresh on/off
- Manual refresh button

### Filtering & Search

- Filter by read/unread status
- Filter by notification channel
- Filter by priority level
- Text search across title and content
- Reset all filters

### Bulk Operations

- Select individual notifications
- Select all notifications
- Bulk mark as read
- Bulk delete
- Clear selection

### Notification Settings

- Enable/disable email notifications
- Enable/disable push notifications
- Enable/disable in-app notifications
- Configure notification types (system, message, activity, marketing)
- Save settings to user preferences

### Statistics Dashboard

- Total notification count
- Read/unread breakdown
- Read rate percentage
- Notifications by channel
- Notifications by priority

## Styling & Theming

The module uses Ant Design components with custom styling:

```css
/* Unread notifications have blue border */
.notification-item.unread {
  border-color: #1890ff;
  background-color: #f6ffed;
}

/* Read notifications have default styling */
.notification-item.read {
  border-color: #d9d9d9;
  background-color: #ffffff;
}

/* Expired notifications are dimmed */
.notification-item.expired {
  opacity: 0.6;
}
```

## Error Handling

The module includes comprehensive error handling:

- API request failures
- Network connectivity issues
- Invalid data responses
- User-friendly error messages
- Retry mechanisms

## Performance Optimizations

- Pagination for large notification lists
- Lazy loading with "Load More" button
- Efficient state management with Zustand
- Debounced search input
- Optimized re-renders

## Accessibility

- ARIA labels for screen readers
- Keyboard navigation support
- High contrast color schemes
- Semantic HTML structure
- Focus management

## Dependencies

- React 18+
- Ant Design 5+
- React Router 6+
- React i18next
- Zustand
- Dayjs
- Lodash

## Integration with Backend

The module expects the backend to implement the Notification Module API as documented in `docs-api/Notification Module/`. Key requirements:

1. **Authentication**: Bearer token in Authorization header
2. **Pagination**: Cursor-based or page-based pagination
3. **Filtering**: Support for status, channel, priority filters
4. **Real-time**: WebSocket support for live updates (optional)
5. **Settings**: User-specific notification preferences

## Testing

The module includes comprehensive error handling and loading states for robust user experience:

- Loading spinners during API calls
- Error messages with retry options
- Empty states for no notifications
- Skeleton loading for better UX
