export const MODULE = 'notification';
export const MODULE_POPUP = true;

export const API_ENDPOINTS = {
  NOTIFICATIONS: '/api/admin/v1/notifications',
  UNREAD_COUNT: '/api/admin/v1/notifications/unread-count',
  MARK_ALL_READ: '/api/admin/v1/notifications/mark-all-read',
  SETTINGS: '/api/admin/v1/notifications/settings',
} as const;

export const NOTIFICATION_CHANNELS = {
  EMAIL: 'email',
  PUSH: 'push',
  SMS: 'sms',
  IN_APP: 'in_app',
  WEBSOCKET: 'websocket',
  TELEGRAM: 'telegram',
} as const;

export const NOTIFICATION_CHANNEL_OPTIONS = [
  { value: 'email', label: 'Email', icon: '📧', color: 'blue' },
  { value: 'push', label: 'Push Notification', icon: '🔔', color: 'green' },
  { value: 'sms', label: 'SMS', icon: '📱', color: 'orange' },
  { value: 'in_app', label: 'In-App', icon: '📱', color: 'purple' },
  { value: 'websocket', label: 'WebSocket', icon: '🔌', color: 'cyan' },
  { value: 'telegram', label: 'Telegram', icon: '✈️', color: 'blue' },
] as const;

export const NOTIFICATION_PRIORITIES = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent',
} as const;

export const NOTIFICATION_PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', color: 'default' },
  { value: 'normal', label: 'Normal', color: 'blue' },
  { value: 'high', label: 'High', color: 'orange' },
  { value: 'urgent', label: 'Urgent', color: 'red' },
] as const;

export const NOTIFICATION_STATUS_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'unread', label: 'Unread' },
  { value: 'read', label: 'Read' },
] as const;

export const NOTIFICATION_TYPES = {
  SYSTEM: 'system',
  MESSAGE: 'message',
  ACTIVITY: 'activity',
  MARKETING: 'marketing',
} as const;

export const NOTIFICATION_TYPE_OPTIONS = [
  { value: 'system', label: 'System', icon: '⚙️', color: 'blue' },
  { value: 'message', label: 'Message', icon: '💬', color: 'green' },
  { value: 'activity', label: 'Activity', icon: '🎯', color: 'orange' },
  { value: 'marketing', label: 'Marketing', icon: '📢', color: 'purple' },
] as const;

export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

export const REFRESH_INTERVALS = {
  NOTIFICATIONS: 30000, // 30 seconds
  UNREAD_COUNT: 10000, // 10 seconds
} as const;

export const DEFAULT_NOTIFICATION_SETTINGS = {
  emailNotifications: true,
  pushNotifications: true,
  inAppNotifications: true,
  notificationTypes: {
    system: true,
    message: true,
    activity: true,
    marketing: false,
  },
} as const;
