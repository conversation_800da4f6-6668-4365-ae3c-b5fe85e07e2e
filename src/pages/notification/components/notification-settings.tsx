import { SettingOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Divider,
  Form,
  message,
  Modal,
  Space,
  Switch,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getNotificationSettings, updateNotificationSettings } from '../api';
import { DEFAULT_NOTIFICATION_SETTINGS, MODULE } from '../config';
import useNotificationStore from '../store';

const { Title, Text } = Typography;

interface NotificationSettingsModalProps {
  visible: boolean;
  onClose: () => void;
}

const NotificationSettingsModal: React.FC<NotificationSettingsModalProps> = ({
  visible,
  onClose,
}) => {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const { settings, setSettings } = useNotificationStore();

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await getNotificationSettings();
      if (response.status.success) {
        const settingsData = response.data;
        setSettings(settingsData);
        form.setFieldsValue(settingsData);
      } else {
        // Use default settings if API fails
        const defaultSettings = DEFAULT_NOTIFICATION_SETTINGS;
        setSettings(defaultSettings);
        form.setFieldsValue(defaultSettings);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      message.error(t('errorLoadingSettings'));
      // Use default settings on error
      const defaultSettings = DEFAULT_NOTIFICATION_SETTINGS;
      setSettings(defaultSettings);
      form.setFieldsValue(defaultSettings);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchSettings();
    }
  }, [visible]);

  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();

      const response = await updateNotificationSettings(values);
      if (response.status.success) {
        setSettings(response.data);
        message.success(t('settingsUpdated'));
        onClose();
      } else {
        message.error(response.status.message || t('errorUpdatingSettings'));
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      message.error(t('errorUpdatingSettings'));
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(DEFAULT_NOTIFICATION_SETTINGS);
  };

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          {t('notificationSettings')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="reset" onClick={handleReset} disabled={loading || saving}>
          Reset to Default
        </Button>,
        <Button key="cancel" onClick={onClose} disabled={saving}>
          Cancel
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          loading={saving}
          disabled={loading}
        >
          Save Settings
        </Button>,
      ]}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={DEFAULT_NOTIFICATION_SETTINGS}
      >
        <Card
          title="General Settings"
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Form.Item
            label={t('emailNotifications')}
            name="emailNotifications"
            valuePropName="checked"
            extra="Receive notifications via email"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('pushNotifications')}
            name="pushNotifications"
            valuePropName="checked"
            extra="Receive push notifications on your devices"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('inAppNotifications')}
            name="inAppNotifications"
            valuePropName="checked"
            extra="Show notifications within the application"
          >
            <Switch />
          </Form.Item>
        </Card>

        <Card title={t('notificationTypes')} size="small">
          <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
            Choose which types of notifications you want to receive
          </Text>

          <Form.Item
            label={t('typeSystem')}
            name={['notificationTypes', 'system']}
            valuePropName="checked"
            extra="System updates, maintenance notices, security alerts"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('typeMessage')}
            name={['notificationTypes', 'message']}
            valuePropName="checked"
            extra="New messages, replies, mentions"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('typeActivity')}
            name={['notificationTypes', 'activity']}
            valuePropName="checked"
            extra="Activity updates, status changes, progress notifications"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={t('typeMarketing')}
            name={['notificationTypes', 'marketing']}
            valuePropName="checked"
            extra="Promotional content, newsletters, product updates"
          >
            <Switch />
          </Form.Item>
        </Card>

        <Divider />

        <Text type="secondary" style={{ fontSize: 12 }}>
          Note: Some critical system notifications cannot be disabled for
          security reasons.
        </Text>
      </Form>
    </Modal>
  );
};

export default NotificationSettingsModal;
