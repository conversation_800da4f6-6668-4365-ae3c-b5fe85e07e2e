import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { Button, Popconfirm, Space, Tooltip } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';

interface BulkActionsProps {
  selectedCount: number;
  onMarkAsRead: () => void;
  onDelete: () => void;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  loading?: boolean;
  totalCount: number;
  allSelected?: boolean;
}

const BulkActions: React.FC<BulkActionsProps> = ({
  selectedCount,
  onMarkAsRead,
  onDelete,
  onSelectAll,
  onDeselectAll,
  loading = false,
  totalCount,
  allSelected = false,
}) => {
  const { t } = useTranslation(MODULE);

  if (selectedCount === 0) {
    return (
      <Space>
        <Button
          size="small"
          icon={<CheckOutlined />}
          onClick={onSelectAll}
          disabled={loading || totalCount === 0}
        >
          {t('selectAll')}
        </Button>
      </Space>
    );
  }

  return (
    <Space>
      <span style={{ fontSize: 12, color: '#666' }}>
        {t('selectedCount', { count: selectedCount })}
      </span>

      <Button
        size="small"
        icon={<CloseOutlined />}
        onClick={onDeselectAll}
        disabled={loading}
      >
        {t('deselectAll')}
      </Button>

      <Tooltip title={t('bulkActionsTooltip')}>
        <Button
          size="small"
          type="primary"
          icon={<EyeOutlined />}
          onClick={onMarkAsRead}
          disabled={loading}
        >
          {t('btnMarkAsRead')}
        </Button>
      </Tooltip>

      <Popconfirm
        title={t('bulkDeleteConfirm', { count: selectedCount })}
        onConfirm={onDelete}
        okText="Yes"
        cancelText="No"
        disabled={loading}
      >
        <Tooltip title={t('bulkActionsTooltip')}>
          <Button
            size="small"
            danger
            icon={<DeleteOutlined />}
            disabled={loading}
          >
            {t('btnDelete')}
          </Button>
        </Tooltip>
      </Popconfirm>

      {!allSelected && selectedCount < totalCount && (
        <Button
          size="small"
          icon={<CheckOutlined />}
          onClick={onSelectAll}
          disabled={loading}
        >
          {t('selectAll')} ({totalCount})
        </Button>
      )}
    </Space>
  );
};

export default BulkActions;
