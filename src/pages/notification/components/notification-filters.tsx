import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, Col, Input, Row, Select, Space } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  MODULE,
  NOTIFICATION_CHANNEL_OPTIONS,
  NOTIFICATION_PRIORITY_OPTIONS,
  NOTIFICATION_STATUS_OPTIONS,
} from '../config';

const { Search } = Input;

interface NotificationFiltersProps {
  statusFilter: 'all' | 'read' | 'unread';
  channelFilter: string | null;
  priorityFilter: string | null;
  searchQuery: string;
  onStatusChange: (status: 'all' | 'read' | 'unread') => void;
  onChannelChange: (channel: string | null) => void;
  onPriorityChange: (priority: string | null) => void;
  onSearchChange: (query: string) => void;
  onReset: () => void;
  loading?: boolean;
}

const NotificationFilters: React.FC<NotificationFiltersProps> = ({
  statusFilter,
  channelFilter,
  priorityFilter,
  searchQuery,
  onStatusChange,
  onChannelChange,
  onPriorityChange,
  onSearchChange,
  onReset,
  loading = false,
}) => {
  const { t } = useTranslation(MODULE);

  const handleReset = () => {
    onStatusChange('all');
    onChannelChange(null);
    onPriorityChange(null);
    onSearchChange('');
    onReset();
  };

  return (
    <Card size="small" style={{ marginBottom: 16 }}>
      <Row gutter={[16, 16]} align="middle">
        <Col xs={24} sm={12} md={6}>
          <Search
            placeholder={t('searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            onSearch={onSearchChange}
            allowClear
            enterButton={<SearchOutlined />}
          />
        </Col>

        <Col xs={24} sm={12} md={4}>
          <Select
            placeholder={t('filterByStatus')}
            value={statusFilter}
            onChange={onStatusChange}
            style={{ width: '100%' }}
          >
            {NOTIFICATION_STATUS_OPTIONS.map((option) => (
              <Select.Option key={option.value} value={option.value}>
                {t(`status${option.label}`)}
              </Select.Option>
            ))}
          </Select>
        </Col>

        <Col xs={24} sm={12} md={4}>
          <Select
            placeholder={t('filterByChannel')}
            value={channelFilter}
            onChange={onChannelChange}
            style={{ width: '100%' }}
            allowClear
          >
            {NOTIFICATION_CHANNEL_OPTIONS.map((option) => (
              <Select.Option key={option.value} value={option.value}>
                <Space>
                  <span>{option.icon}</span>
                  <span>{t(`channel${option.label.replace(/\s+/g, '')}`)}</span>
                </Space>
              </Select.Option>
            ))}
          </Select>
        </Col>

        <Col xs={24} sm={12} md={4}>
          <Select
            placeholder={t('filterByPriority')}
            value={priorityFilter}
            onChange={onPriorityChange}
            style={{ width: '100%' }}
            allowClear
          >
            {NOTIFICATION_PRIORITY_OPTIONS.map((option) => (
              <Select.Option key={option.value} value={option.value}>
                {t(`priority${option.label}`)}
              </Select.Option>
            ))}
          </Select>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              disabled={loading}
            >
              Reset
            </Button>
          </Space>
        </Col>
      </Row>
    </Card>
  );
};

export default NotificationFilters;
