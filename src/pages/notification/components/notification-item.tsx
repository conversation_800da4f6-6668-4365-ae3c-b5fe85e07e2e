import {
  ClockCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Checkbox,
  Space,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  MODULE,
  NOTIFICATION_CHANNEL_OPTIONS,
  NOTIFICATION_PRIORITY_OPTIONS,
} from '../config';
import { Notification } from '../type';

dayjs.extend(relativeTime);

const { Text, Paragraph } = Typography;

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead?: (id: number) => void;
  onDelete?: (id: number) => void;
  onViewDetail?: (notification: Notification) => void;
  onSelect?: (id: number, selected: boolean) => void;
  selected?: boolean;
  showSelection?: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onDelete,
  onViewDetail,
  onSelect,
  selected = false,
  showSelection = false,
}) => {
  const { t } = useTranslation(MODULE);

  const getChannelInfo = (channel: string) => {
    return (
      NOTIFICATION_CHANNEL_OPTIONS.find((c) => c.value === channel) || {
        icon: '📄',
        label: channel,
        color: 'default',
      }
    );
  };

  const getPriorityInfo = (priority: string) => {
    return (
      NOTIFICATION_PRIORITY_OPTIONS.find((p) => p.value === priority) || {
        label: priority,
        color: 'default',
      }
    );
  };

  const getRelativeTime = (dateString: string) => {
    const date = dayjs(dateString);
    const now = dayjs();
    const diffMinutes = now.diff(date, 'minute');

    if (diffMinutes < 1) return t('justNow');
    if (diffMinutes < 60) return t('minutesAgo', { count: diffMinutes });

    const diffHours = now.diff(date, 'hour');
    if (diffHours < 24) return t('hoursAgo', { count: diffHours });

    const diffDays = now.diff(date, 'day');
    if (diffDays < 7) return t('daysAgo', { count: diffDays });

    const diffWeeks = now.diff(date, 'week');
    if (diffWeeks < 4) return t('weeksAgo', { count: diffWeeks });

    const diffMonths = now.diff(date, 'month');
    if (diffMonths < 12) return t('monthsAgo', { count: diffMonths });

    const diffYears = now.diff(date, 'year');
    return t('yearsAgo', { count: diffYears });
  };

  const isExpired =
    notification.expires_at && dayjs().isAfter(dayjs(notification.expires_at));
  const willExpireSoon =
    notification.expires_at &&
    dayjs().add(1, 'day').isAfter(dayjs(notification.expires_at)) &&
    !isExpired;

  const channelInfo = getChannelInfo(notification.channel);
  const priorityInfo = getPriorityInfo(notification.priority || 'normal');

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead?.(notification.notification_id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(notification.notification_id);
  };

  const handleViewDetail = () => {
    onViewDetail?.(notification);
  };

  const handleSelect = (checked: boolean) => {
    onSelect?.(notification.notification_id, checked);
  };

  return (
    <Card
      size="small"
      className={`notification-item ${!notification.is_read ? 'unread' : 'read'} ${isExpired ? 'expired' : ''}`}
      style={{
        marginBottom: 8,
        cursor: 'pointer',
        border: !notification.is_read
          ? '1px solid #1890ff'
          : '1px solid #d9d9d9',
        backgroundColor: !notification.is_read ? '#f6ffed' : '#ffffff',
        opacity: isExpired ? 0.6 : 1,
      }}
      onClick={handleViewDetail}
      hoverable
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
        {showSelection && (
          <Checkbox
            checked={selected}
            onChange={(e) => {
              e.stopPropagation();
              handleSelect(e.target.checked);
            }}
          />
        )}

        <Avatar
          size="small"
          style={{
            backgroundColor:
              channelInfo.color === 'default' ? '#1890ff' : channelInfo.color,
            flexShrink: 0,
          }}
        >
          {channelInfo.icon}
        </Avatar>

        <div style={{ flex: 1, minWidth: 0 }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: 4,
            }}
          >
            <Text strong style={{ fontSize: 14 }} ellipsis>
              {notification.title}
            </Text>
            <Space size="small">
              {notification.priority && notification.priority !== 'normal' && (
                <Tag color={priorityInfo.color} size="small">
                  {t(`priority${priorityInfo.label}`)}
                </Tag>
              )}
              <Tag color={channelInfo.color} size="small">
                {channelInfo.icon}{' '}
                {t(`channel${channelInfo.label.replace(/\s+/g, '')}`)}
              </Tag>
            </Space>
          </div>

          <Paragraph
            style={{
              margin: 0,
              fontSize: 13,
              color: '#666',
              marginBottom: 8,
            }}
            ellipsis={{ rows: 2 }}
          >
            {notification.content}
          </Paragraph>

          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Space size="small">
              <Text type="secondary" style={{ fontSize: 12 }}>
                <ClockCircleOutlined style={{ marginRight: 4 }} />
                {getRelativeTime(notification.created_at)}
              </Text>

              {!notification.is_read && (
                <Tag color="blue" size="small">
                  {t('statusUnread')}
                </Tag>
              )}

              {isExpired && (
                <Tag color="red" size="small">
                  <ExclamationCircleOutlined style={{ marginRight: 4 }} />
                  {t('notificationExpired')}
                </Tag>
              )}

              {willExpireSoon && (
                <Tag color="orange" size="small">
                  <ExclamationCircleOutlined style={{ marginRight: 4 }} />
                  {t('notificationWillExpire', {
                    date: dayjs(notification.expires_at).format(
                      'DD/MM/YYYY HH:mm',
                    ),
                  })}
                </Tag>
              )}
            </Space>

            <Space size="small">
              {!notification.is_read && (
                <Tooltip title={t('markAsReadTooltip')}>
                  <Button
                    type="text"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={handleMarkAsRead}
                  />
                </Tooltip>
              )}

              <Tooltip title={t('deleteTooltip')}>
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleDelete}
                />
              </Tooltip>
            </Space>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default NotificationItem;
