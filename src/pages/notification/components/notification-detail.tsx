import {
  ClockCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Descriptions,
  Divider,
  Modal,
  Popconfirm,
  Space,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  MODULE,
  NOTIFICATION_CHANNEL_OPTIONS,
  NOTIFICATION_PRIORITY_OPTIONS,
} from '../config';
import { Notification } from '../type';

const { Text, Paragraph } = Typography;

interface NotificationDetailProps {
  notification: Notification;
  visible: boolean;
  onClose: () => void;
  onMarkAsRead?: (id: number) => void;
  onDelete?: (id: number) => void;
}

const NotificationDetail: React.FC<NotificationDetailProps> = ({
  notification,
  visible,
  onClose,
  onMarkAsRead,
  onDelete,
}) => {
  const { t } = useTranslation(MODULE);

  const getChannelInfo = (channel: string) => {
    return (
      NOTIFICATION_CHANNEL_OPTIONS.find((c) => c.value === channel) || {
        icon: '📄',
        label: channel,
        color: 'default',
      }
    );
  };

  const getPriorityInfo = (priority: string) => {
    return (
      NOTIFICATION_PRIORITY_OPTIONS.find((p) => p.value === priority) || {
        label: priority,
        color: 'default',
      }
    );
  };

  const isExpired =
    notification.expires_at && dayjs().isAfter(dayjs(notification.expires_at));
  const willExpireSoon =
    notification.expires_at &&
    dayjs().add(1, 'day').isAfter(dayjs(notification.expires_at)) &&
    !isExpired;

  const channelInfo = getChannelInfo(notification.channel);
  const priorityInfo = getPriorityInfo(notification.priority || 'normal');

  const handleMarkAsRead = () => {
    onMarkAsRead?.(notification.notification_id);
  };

  const handleDelete = () => {
    onDelete?.(notification.notification_id);
    onClose();
  };

  const formatDate = (dateString: string) => {
    return dayjs(dateString).format('DD/MM/YYYY HH:mm:ss');
  };

  return (
    <Modal
      title={
        <Space>
          <InfoCircleOutlined />
          {t('notificationDetail')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={700}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('closeDetail')}
        </Button>,
        !notification.is_read && (
          <Button
            key="markRead"
            type="primary"
            icon={<EyeOutlined />}
            onClick={handleMarkAsRead}
          >
            {t('btnMarkAsRead')}
          </Button>
        ),
        <Popconfirm
          key="delete"
          title={t('deleteConfirm')}
          onConfirm={handleDelete}
          okText="Yes"
          cancelText="No"
        >
          <Button danger icon={<DeleteOutlined />}>
            {t('btnDelete')}
          </Button>
        </Popconfirm>,
      ].filter(Boolean)}
      destroyOnClose
    >
      <div>
        {/* Status Alerts */}
        {!notification.is_read && (
          <Alert
            message={t('statusUnread')}
            description="This notification has not been read yet"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {isExpired && (
          <Alert
            message={t('notificationExpired')}
            description={`This notification expired on ${formatDate(notification.expires_at!)}`}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {willExpireSoon && (
          <Alert
            message="Notification Expiring Soon"
            description={t('notificationWillExpire', {
              date: formatDate(notification.expires_at!),
            })}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* Basic Information */}
        <Descriptions
          title="Basic Information"
          bordered
          column={2}
          size="small"
          style={{ marginBottom: 24 }}
        >
          <Descriptions.Item label={t('notification_id')} span={2}>
            <Text code>{notification.notification_id}</Text>
          </Descriptions.Item>

          <Descriptions.Item label={t('title')} span={2}>
            <Text strong style={{ fontSize: 16 }}>
              {notification.title}
            </Text>
          </Descriptions.Item>

          <Descriptions.Item label={t('channel')}>
            <Tag color={channelInfo.color}>
              {channelInfo.icon}{' '}
              {t(`channel${channelInfo.label.replace(/\s+/g, '')}`)}
            </Tag>
          </Descriptions.Item>

          <Descriptions.Item label={t('priority')}>
            <Tag color={priorityInfo.color}>
              {t(`priority${priorityInfo.label}`)}
            </Tag>
          </Descriptions.Item>

          <Descriptions.Item label={t('is_read')}>
            <Tag color={notification.is_read ? 'green' : 'blue'}>
              {notification.is_read ? t('statusRead') : t('statusUnread')}
            </Tag>
          </Descriptions.Item>

          <Descriptions.Item label={t('template_code')}>
            <Text code>{notification.template_code}</Text>
          </Descriptions.Item>

          <Descriptions.Item label={t('created_at')}>
            <Space>
              <ClockCircleOutlined />
              {formatDate(notification.created_at)}
            </Space>
          </Descriptions.Item>

          {notification.read_at && (
            <Descriptions.Item label={t('read_at')}>
              <Space>
                <ClockCircleOutlined />
                {formatDate(notification.read_at)}
              </Space>
            </Descriptions.Item>
          )}

          {notification.expires_at && (
            <Descriptions.Item label={t('expires_at')} span={2}>
              <Space>
                <ExclamationCircleOutlined
                  style={{ color: isExpired ? 'red' : 'orange' }}
                />
                {formatDate(notification.expires_at)}
                {isExpired && <Text type="danger">(Expired)</Text>}
              </Space>
            </Descriptions.Item>
          )}
        </Descriptions>

        {/* Content */}
        <div style={{ marginBottom: 24 }}>
          <h4>{t('content')}</h4>
          <div
            style={{
              background: '#f5f5f5',
              padding: '16px',
              borderRadius: '6px',
              border: '1px solid #d9d9d9',
              minHeight: '100px',
              maxHeight: '300px',
              overflow: 'auto',
            }}
          >
            <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {notification.content}
            </Paragraph>
          </div>
        </div>

        {/* Additional Data */}
        {notification.data && Object.keys(notification.data).length > 0 && (
          <div>
            <h4>Additional Data</h4>
            <div
              style={{
                background: '#f5f5f5',
                padding: '16px',
                borderRadius: '6px',
                border: '1px solid #d9d9d9',
                fontFamily: 'monospace',
                fontSize: '12px',
                maxHeight: '200px',
                overflow: 'auto',
              }}
            >
              <pre style={{ margin: 0 }}>
                {JSON.stringify(notification.data, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <Divider />

        {/* Metadata */}
        <div style={{ fontSize: 12, color: '#666' }}>
          <Space split={<Divider type="vertical" />}>
            <span>ID: {notification.notification_id}</span>
            <span>Created: {formatDate(notification.created_at)}</span>
            <span>Updated: {formatDate(notification.updated_at)}</span>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default NotificationDetail;
