import { Bar<PERSON><PERSON>Outlined, BellOutlined, EyeOutlined } from '@ant-design/icons';
import { Card, Col, Row, Spin, Statistic, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getNotificationStats } from '../api';
import {
  MODULE,
  NOTIFICATION_CHANNEL_OPTIONS,
  NOTIFICATION_PRIORITY_OPTIONS,
} from '../config';
import { NotificationStats } from '../type';

interface NotificationStatsProps {
  refreshTrigger?: number;
}

const NotificationStatsComponent: React.FC<NotificationStatsProps> = ({
  refreshTrigger = 0,
}) => {
  const { t } = useTranslation(MODULE);
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchStats = async () => {
    setLoading(true);
    try {
      const response = await getNotificationStats();
      if (response.status.success) {
        setStats(response.data);
      } else {
        message.error('Failed to load notification statistics');
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      message.error('Failed to load notification statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [refreshTrigger]);

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  const getChannelLabel = (channel: string) => {
    const channelInfo = NOTIFICATION_CHANNEL_OPTIONS.find(
      (c) => c.value === channel,
    );
    return channelInfo
      ? t(`channel${channelInfo.label.replace(/\s+/g, '')}`)
      : channel;
  };

  const getPriorityLabel = (priority: string) => {
    const priorityInfo = NOTIFICATION_PRIORITY_OPTIONS.find(
      (p) => p.value === priority,
    );
    return priorityInfo ? t(`priority${priorityInfo.label}`) : priority;
  };

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* Overview Stats */}
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Notifications"
              value={stats.total}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('statusUnread')}
              value={stats.unread}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title={t('statusRead')}
              value={stats.read}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Read Rate"
              value={
                stats.total > 0
                  ? Math.round((stats.read / stats.total) * 100)
                  : 0
              }
              suffix="%"
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>

        {/* Channel Breakdown */}
        <Col xs={24} lg={12}>
          <Card title="By Channel" size="small">
            <Row gutter={[8, 8]}>
              {Object.entries(stats.by_channel).map(([channel, count]) => (
                <Col xs={12} sm={8} key={channel}>
                  <Statistic
                    title={getChannelLabel(channel)}
                    value={count}
                    valueStyle={{ fontSize: 16 }}
                  />
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* Priority Breakdown */}
        <Col xs={24} lg={12}>
          <Card title="By Priority" size="small">
            <Row gutter={[8, 8]}>
              {Object.entries(stats.by_priority).map(([priority, count]) => (
                <Col xs={12} sm={8} key={priority}>
                  <Statistic
                    title={getPriorityLabel(priority)}
                    value={count}
                    valueStyle={{
                      fontSize: 16,
                      color:
                        priority === 'urgent'
                          ? '#f5222d'
                          : priority === 'high'
                            ? '#fa8c16'
                            : priority === 'normal'
                              ? '#1890ff'
                              : '#52c41a',
                    }}
                  />
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NotificationStatsComponent;
