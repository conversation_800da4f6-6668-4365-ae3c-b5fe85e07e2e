import {
  CheckOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Empty,
  Row,
  Space,
  Spin,
  Switch,
  Tooltip,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BackButton } from '../../../components/button';
import {
  bulkDelete,
  bulkMarkAsRead,
  deleteNotification,
  getNotifications,
  markAllAsRead,
  markAsRead,
} from '../api';
import { MODULE, PAGINATION_CONFIG, REFRESH_INTERVALS } from '../config';
import useNotificationStore from '../store';
import { Notification } from '../type';
import BulkActions from './bulk-actions';
import NotificationDetail from './notification-detail';
import NotificationFilters from './notification-filters';
import NotificationItem from './notification-item';
import NotificationSettings from './notification-settings';
import UnreadCounter from './unread-counter';

const NotificationList: React.FC = () => {
  const { t } = useTranslation(MODULE);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(
    null,
  );

  const {
    loading,
    setLoading,
    notifications,
    setNotifications,
    unreadCount,
    currentPage,
    setCurrentPage,
    setTotalPages,
    setHasMore,
    statusFilter,
    setStatusFilter,
    channelFilter,
    setChannelFilter,
    priorityFilter,
    setPriorityFilter,
    selectedNotifications,
    setSelectedNotifications,
    clearSelection,
    detailModalVisible,
    setDetailModalVisible,
    selectedNotificationId,
    setSelectedNotificationId,
    autoRefresh,
    setAutoRefresh,
    settingsModalVisible,
    setSettingsModalVisible,
    hasMore,
    markAsRead: storeMarkAsRead,
    markAllAsRead: storeMarkAllAsRead,
    deleteNotification: storeDeleteNotification,
    bulkMarkAsRead: storeBulkMarkAsRead,
    bulkDelete: storeBulkDelete,
  } = useNotificationStore();

  const fetchNotifications = async (page = 1, append = false) => {
    if (!append) setLoading(true);

    try {
      const params = {
        page,
        limit: PAGINATION_CONFIG.DEFAULT_LIMIT,
        status: statusFilter,
        channel: channelFilter || undefined,
        priority: priorityFilter || undefined,
      };

      const response = await getNotifications(params);
      if (response.status.success && response.data) {
        const { notifications: newNotifications = [], meta } = response.data;
        console.log('[notifications]', { newNotifications, meta });

        // Update notifications state
        if (append) {
          // Use the store's setNotifications function directly
          setNotifications([...notifications, ...newNotifications]);
        } else {
          setNotifications(newNotifications);
        }

        // Update pagination state if meta exists
        if (meta) {
          setCurrentPage(meta.current_page);
          setTotalPages(meta.total_pages || 1);
          setHasMore(meta.has_more || false);
        }
      } else {
        message.error(
          response.status.message || t('errorLoadingNotifications'),
        );
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      message.error(t('errorLoadingNotifications'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [statusFilter, channelFilter, priorityFilter]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchNotifications(1, false);
      }, REFRESH_INTERVALS.NOTIFICATIONS);
      setRefreshInterval(interval);
    } else if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh]);

  const handleMarkAsRead = async (id: number) => {
    try {
      const response = await markAsRead(id);
      if (response.status.success) {
        storeMarkAsRead(id);
        message.success(t('markAsReadSuccess'));
      }
    } catch (error) {
      message.error(t('errorMarkingAsRead'));
    }
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteNotification(id);
      if (response.status.success) {
        storeDeleteNotification(id);
        message.success(t('deleteSuccess'));
      }
    } catch (error) {
      message.error(t('errorDeleting'));
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllAsRead();
      if (response.status.success) {
        storeMarkAllAsRead();
        message.success(t('markAllAsReadSuccess'));
      }
    } catch (error) {
      message.error(t('errorMarkingAsRead'));
    }
  };

  const handleBulkMarkAsRead = async () => {
    try {
      const response = await bulkMarkAsRead(selectedNotifications);
      if (response.status.success) {
        storeBulkMarkAsRead(selectedNotifications);
        message.success(
          t('bulkMarkAsReadSuccess', { count: selectedNotifications.length }),
        );
        clearSelection();
      }
    } catch (error) {
      message.error(t('errorMarkingAsRead'));
    }
  };

  const handleBulkDelete = async () => {
    try {
      const response = await bulkDelete(selectedNotifications);
      if (response.status.success) {
        storeBulkDelete(selectedNotifications);
        message.success(
          t('bulkDeleteSuccess', { count: selectedNotifications.length }),
        );
        clearSelection();
      }
    } catch (error) {
      message.error(t('errorDeleting'));
    }
  };

  const handleViewDetail = (notification: Notification) => {
    setSelectedNotificationId(notification.notification_id);
    setDetailModalVisible(true);

    // Mark as read when viewing detail
    if (!notification.is_read) {
      handleMarkAsRead(notification.notification_id);
    }
  };

  const handleSelectNotification = (id: number, selected: boolean) => {
    if (selected) {
      setSelectedNotifications([...selectedNotifications, id]);
    } else {
      setSelectedNotifications(
        selectedNotifications.filter((nId) => nId !== id),
      );
    }
  };

  const handleSelectAll = () => {
    setSelectedNotifications(notifications.map((n) => n.notification_id));
  };

  const handleDeselectAll = () => {
    clearSelection();
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      fetchNotifications(currentPage + 1, true);
    }
  };

  const handleRefresh = () => {
    fetchNotifications(1, false);
  };

  const handleFiltersReset = () => {
    setSearchQuery('');
    fetchNotifications(1, false);
  };

  const selectedNotification =
    notifications?.find((n) => n.notification_id === selectedNotificationId) ||
    null;

  return (
    <div>
      <div className="mb-4">
        <BackButton destination="dashboard" />
      </div>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Space>
              <UnreadCounter showIcon size="default" />
              <span style={{ fontSize: 16, fontWeight: 500 }}>
                {t('module')}
              </span>
            </Space>

            <Space>
              <Tooltip title={t('autoRefreshTooltip')}>
                <Space>
                  <span style={{ fontSize: 12 }}>{t('autoRefresh')}</span>
                  <Switch
                    size="small"
                    checked={autoRefresh}
                    onChange={setAutoRefresh}
                  />
                </Space>
              </Tooltip>

              <Tooltip title={t('refreshTooltip')}>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                />
              </Tooltip>

              <Tooltip title={t('settingsTooltip')}>
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setSettingsModalVisible(true)}
                />
              </Tooltip>

              {unreadCount > 0 && (
                <Button
                  type="primary"
                  icon={<CheckOutlined />}
                  onClick={handleMarkAllAsRead}
                >
                  {t('btnMarkAllAsRead')}
                </Button>
              )}
            </Space>
          </Space>
        </Col>
      </Row>

      <NotificationFilters
        statusFilter={statusFilter}
        channelFilter={channelFilter}
        priorityFilter={priorityFilter}
        searchQuery={searchQuery}
        onStatusChange={setStatusFilter}
        onChannelChange={setChannelFilter}
        onPriorityChange={setPriorityFilter}
        onSearchChange={setSearchQuery}
        onReset={handleFiltersReset}
        loading={loading}
      />

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <BulkActions
            selectedCount={selectedNotifications.length}
            totalCount={notifications.length}
            allSelected={
              selectedNotifications.length === notifications.length &&
              notifications.length > 0
            }
            onMarkAsRead={handleBulkMarkAsRead}
            onDelete={handleBulkDelete}
            onSelectAll={handleSelectAll}
            onDeselectAll={handleDeselectAll}
            loading={loading}
          />
        </Col>

        <Col span={24}>
          {loading && notifications.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Spin size="large" />
              <div style={{ marginTop: 16 }}>{t('loadingNotifications')}</div>
            </div>
          ) : notifications.length === 0 ? (
            <Empty
              description={t('emptyState')}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <div style={{ color: '#999', marginTop: 8 }}>
                {t('emptyStateDescription')}
              </div>
            </Empty>
          ) : (
            <div>
              {notifications.map((notification) => (
                <NotificationItem
                  key={notification.notification_id}
                  notification={notification}
                  onMarkAsRead={handleMarkAsRead}
                  onDelete={handleDelete}
                  onViewDetail={handleViewDetail}
                  onSelect={handleSelectNotification}
                  selected={selectedNotifications.includes(
                    notification.notification_id,
                  )}
                  showSelection={true}
                />
              ))}

              {hasMore && (
                <div style={{ textAlign: 'center', marginTop: 16 }}>
                  <Button
                    onClick={handleLoadMore}
                    loading={loading}
                    size="large"
                  >
                    {t('loadMore')}
                  </Button>
                </div>
              )}
            </div>
          )}
        </Col>
      </Row>

      {/* Modals */}
      <NotificationSettings
        visible={settingsModalVisible}
        onClose={() => setSettingsModalVisible(false)}
      />

      {selectedNotification && (
        <NotificationDetail
          notification={selectedNotification}
          visible={detailModalVisible}
          onClose={() => setDetailModalVisible(false)}
          onMarkAsRead={handleMarkAsRead}
          onDelete={handleDelete}
        />
      )}
    </div>
  );
};

export default NotificationList;
