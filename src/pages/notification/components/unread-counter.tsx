import { BellOutlined } from '@ant-design/icons';
import { Bad<PERSON>, Tooltip } from 'antd';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { getUnreadCount } from '../api';
import { MODULE, REFRESH_INTERVALS } from '../config';
import useNotificationStore from '../store';

interface UnreadCounterProps {
  onClick?: () => void;
  showIcon?: boolean;
  size?: 'small' | 'default';
}

const UnreadCounter: React.FC<UnreadCounterProps> = ({
  onClick,
  showIcon = true,
  size = 'default',
}) => {
  const { t } = useTranslation(MODULE);
  const { unreadCount, setUnreadCount, autoRefresh } = useNotificationStore();

  const fetchUnreadCount = async () => {
    try {
      const response = await getUnreadCount();
      if (response.status.success) {
        setUnreadCount(response.data.unread_count);
      }
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };

  useEffect(() => {
    fetchUnreadCount();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(
      fetchUnreadCount,
      REFRESH_INTERVALS.UNREAD_COUNT,
    );
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const content = (
    <div
      style={{
        cursor: onClick ? 'pointer' : 'default',
        display: 'flex',
        alignItems: 'center',
        gap: 4,
      }}
      onClick={onClick}
    >
      {showIcon && (
        <Badge count={unreadCount} size={size}>
          <BellOutlined style={{ fontSize: size === 'small' ? 16 : 20 }} />
        </Badge>
      )}
      {!showIcon && <Badge count={unreadCount} size={size} />}
    </div>
  );

  if (onClick) {
    return <Tooltip title={t('unreadCount')}>{content}</Tooltip>;
  }

  return content;
};

export default UnreadCounter;
