import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import moment from 'moment';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { CrawlWebCategoryListModal } from '../../crawl-web-category';
import { deleteItem, getAnalytics, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_GROUP, MODULE_POPUP } from '../config';
import { CrawlUserModal } from '../form/modal';
import useCrawlUserStore from '../store';
import { CrawlUser, CrawlUserAnalytics } from '../type';
import { CrawlUserSearch } from './search';
function CrawlUserList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useCrawlUserStore();

  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showModalCategory, setShowModalCategory] = useState(false);
  const [items, setItems] = useState<CrawlUser[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [total, setTotal] = useState<number>(0);
  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.meta.total);
        setNextCursor(response.meta.cursors.after);

        const analyticsData = await getAnalytics();
        if (analyticsData.status.success) {
          const itemsWithAnalytics = response.data.map((item) => ({
            ...item,
            analytics: analyticsData.data.find(
              (analytics: CrawlUserAnalytics) => analytics.userId === item.id,
            ),
          }));
          setItems(itemsWithAnalytics);
        }
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname],
  );

  useEffect(() => {
    fetchData({ after: afterKey });
  }, [fetchData, afterKey]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    {
      title: t('form.id'),
      dataIndex: 'id',
      key: 'id',
      render: (dom: any, record: any) => <span>{dom}</span>,
    },

    {
      title: t('form.domain'),
      dataIndex: 'domain',
      key: 'domain',
    },
    {
      title: t('form.ip'),
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: t('form.expireDate'),
      dataIndex: 'expireDate',
      key: 'expireDate',
      render: (dom: string) => <span>{moment(dom).format('DD/MM/YYYY')}</span>,
    },
    {
      title: t('form.status'),
      dataIndex: 'status',
      key: 'status',
      render: (dom: string) => <span>{t(`status.${dom}`)}</span>,
    },
    {
      title: t('form.categories'),
      dataIndex: 'categories',
      key: 'categories',
      render: (dom: []) => (
        <a onClick={() => setShowModalCategory(true)}>{dom ? dom.length : 0}</a>
      ),
    },
    {
      title: t('form.analytics'),
      dataIndex: 'analytics',
      key: 'analytics',
      render: (dom: CrawlUserAnalytics) => (
        <>Bài chờ: {JSON.stringify(dom.blogTotal)}</>
      ),
    },
    {
      title: t('list.actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('list.deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('list.btnDelete')}
                  </Popconfirm>
                ),
              },
              {
                key: 'logs',
                label: (
                  <Link to={`/crawl/crawl-log?topic=${record.id}`}>
                    <UnorderedListOutlined /> {t('list.btnLogs')}
                  </Link>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('list.btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('list.btnAdd')}
          </Button>

          {/* <Button
            type="dashed"
            icon={<ImportOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnImport')}
          </Button>

          <Dropdown menu={{
            items: [
              {
                key: '1',
                label: <a href="#">Chức năng 1</a>,
              },
              {
                key: '2',
                label: <a href="#">Chức năng 2</a>,
              },
              {
                key: '3',
                label: <a href="#">Chức năng 3</a>,
              },
            ]
          }} trigger={['click']}>
            <Button icon={<EllipsisOutlined />} />
          </Dropdown> */}
        </div>
      </div>
      <CrawlUserSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></CrawlUserSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <CrawlUserModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></CrawlUserModal>
        )}
        {showModalCategory && (
          <CrawlWebCategoryListModal
            showModal={showModalCategory}
            onChange={() => setShowModalCategory(false)}
          ></CrawlWebCategoryListModal>
        )}
      </Space>
    </div>
  );
}
export { CrawlUserList };
