import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { Col, DatePicker, Form, Input, message, Row, Select } from 'antd';
import _ from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import { CrawlUser, CrawlUserStatus, transformValues } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const CrawlUserForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<CrawlUser>();
    const [formValues, setFormValues] = useState<CrawlUser>();

    const initForm = {
      active: true,
    };

    const getItemData = async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        res.data = transformValues(res.data);
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    };

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id]);

    const onFinish = async (values: CrawlUser) => {
      try {
        let res;
        if (isNew) {
          res = await createItem(values);
          if (res.status.success) {
            message.success(t('addSuccess'));
          }
        } else {
          res = await updateItem(id!, values);
          if (res.status.success) {
            message.success(t('updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('submitError'),
        );
      }
    };

    const handleValuesChange = (newValue: any, allValues: any) => {
      logger(newValue);
      logger(allValues);
      setFormValues(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('form.domain')}
                name="domain"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('form.ip')}
                name="ip"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('form.apiKey')}
                name="apiKey"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Input.Password
                  iconRender={(visible) =>
                    visible ? <EyeOutlined /> : <EyeInvisibleOutlined />
                  }
                  visibilityToggle={true}
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('form.expireDate')}
                name="expireDate"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <DatePicker format="YYYY-MM-DD" className="w-full" />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.webhookUrl')}
                name="webhookUrl"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.webhookMethod')}
                name="webhookMethod"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Select
                  options={[
                    { value: 'GET', label: 'GET' },
                    { value: 'POST', label: 'POST' },
                    { value: 'PUT', label: 'PUT' },
                    { value: 'DELETE', label: 'DELETE' },
                  ]}
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.webhookHeaderKey')}
                name="webhookHeaderKey"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.webhookHeaderField')}
                name="webhookHeaderField"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.webhookApiKey')}
                name="webhookApiKey"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Input.Password
                  iconRender={(visible) =>
                    visible ? <EyeOutlined /> : <EyeInvisibleOutlined />
                  }
                  visibilityToggle={true}
                />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.status')}
                name="status"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Select
                  options={Object.entries(CrawlUserStatus).map(
                    ([value, label]) => ({
                      value,
                      label: t(`status.${label}`),
                    }),
                  )}
                />
              </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
    );
  },
);
CrawlUserForm.displayName = 'CrawlUserForm';

export { CrawlUserForm };
