import { Input, Form, Button } from 'antd';
import { useEffect } from 'react';

export function BlogAuthorSearch({ query, loading, onChange }: any) {
  const [form] = Form.useForm();

  useEffect(() => {
    // Initialize form with query values
    form.setFieldsValue({
      display_name: query.display_name || '',
      email: query.email || '',
    });
  }, [query, form]);

  const handleFinish = (values: any) => {
    // Only include non-empty values
    const cleanedValues = Object.entries(values).reduce(
      (acc: any, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      },
      {},
    );

    onChange(cleanedValues);
  };

  const handleReset = () => {
    form.resetFields();
    onChange({});
  };

  return (
    <Form
      form={form}
      layout="inline"
      onFinish={handleFinish}
      className="p-4 bg-white border-b"
      validateTrigger={['onChange', 'onBlur']}
    >
      <Form.Item name="display_name" label="Tên tác giả">
        <Input placeholder="Tìm theo tên" allowClear />
      </Form.Item>

      <Form.Item name="email" label="Email">
        <Input placeholder="Tìm theo email" allowClear />
      </Form.Item>

      <Form.Item>
        <Button htmlType="submit" type="primary" loading={loading}>
          Tìm kiếm
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>Xóa bộ lọc</Button>
      </Form.Item>
    </Form>
  );
}
