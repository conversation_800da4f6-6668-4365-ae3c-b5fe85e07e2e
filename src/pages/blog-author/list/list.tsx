import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Avatar,
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
  Tag,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { BackButton } from '../../../components/button';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  cleanParams,
} from '../../../services/utils.service';
import { deleteAuthor, getAuthors } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { BlogAuthorModal } from '../form/modal';
import useBlogAuthorStore from '../store';
import { BlogAuthor } from '../type';
import { BlogAuthorSearch } from './search';

function BlogAuthorList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading, setLoading } = useBlogAuthorStore();

  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<BlogAuthor[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const filtersRef = useRef(filters);
  const queryRef = useRef(query);

  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  useEffect(() => {
    queryRef.current = query;
  }, [query]);

  const fetchDataRef = useRef(async (payload?: any) => {});

  fetchDataRef.current = async (payload?: any) => {
    try {
      setLoading(true);
      const params = {
        ...queryRef.current,
        ...filtersRef.current,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getAuthors(cleanedParams);

      if (response.data?.status?.success) {
        if (
          response.data.data?.authors &&
          Array.isArray(response.data.data.authors)
        ) {
          setItems(response.data.data.authors);
          setNextCursor(response.data.data.meta?.next_cursor ?? '');
        } else if (Array.isArray(response.data.data)) {
          setItems(response.data.data);
          setNextCursor(response.data.meta?.next_cursor ?? '');
        } else {
          setItems([]);
          setNextCursor('');
        }
      } else {
        message.error(response.data?.status?.message ?? 'Error fetching data');
        setItems([]);
      }
    } catch (error) {
      console.error('Failed to fetch authors:', error);
      message.error('Failed to load author data');
      setItems([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchData = useCallback((payload?: any) => {
    fetchDataRef.current(payload);
  }, []);

  useEffect(() => {
    fetchData({ after: afterKey });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [afterKey]);

  const handleFilters = useCallback((values: any) => {
    logger('[filters]', { filters: filtersRef.current, values });
    setFilters(values);
    fetchData({ ...values, after: null });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    try {
      setLoading(true);
      const res = await deleteAuthor(id);
      const response = res.data;

      if (response?.status?.success) {
        message.success(t('deleteSuccess'));
        fetchData();
      } else {
        message.error(response?.status?.message ?? 'Error deleting author');
      }
    } catch (error) {
      console.error('Failed to delete author:', error);
      message.error('Failed to delete author');
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleActions = useCallback((action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleModal = useCallback(() => {
    setShowModal(false);
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: 'Tác giả',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text: string, record: BlogAuthor) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {record.avatar_url ? (
            <Avatar
              src={record.avatar_url}
              size="small"
              style={{ marginRight: 8 }}
            />
          ) : (
            <Avatar size="small" style={{ marginRight: 8 }}>
              {text?.charAt(0).toUpperCase() || 'A'}
            </Avatar>
          )}
          <span>{text}</span>
        </div>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Đang hoạt động' : 'Không hoạt động'}
        </Tag>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      width: 120,
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="flex items-center space-x-2">
          <BackButton destination="dashboard" />
          <div className="text-xl font-bold w-full md:w-auto">
            {t('module')}
          </div>
        </div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <BlogAuthorSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></BlogAuthorSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <BlogAuthorModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></BlogAuthorModal>
        )}
      </Space>
    </div>
  );
}
export { BlogAuthorList };
