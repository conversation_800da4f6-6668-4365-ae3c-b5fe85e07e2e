import { Button, Form, Input, message, Switch } from 'antd';
import { useEffect, useState } from 'react';
import { FormHeader } from '../../../components/form-header';
import { createAuthor, updateAuthor, getAuthors } from '../api';
import { BlogAuthor } from '../type';

export function BlogAuthorForm({
  id,
  onSuccess,
}: {
  id?: string;
  onSuccess: () => void;
}) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      setLoading(true);
      // Fetch author detail for editing
      getAuthors({ id })
        .then((res) => {
          const response = res.data;
          if (
            response.status &&
            response.status.success &&
            Array.isArray(response.data) &&
            response.data.length > 0
          ) {
            const author = response.data[0];
            // Set form fields with the correct field names from backend
            form.setFieldsValue({
              display_name: author.display_name,
              email: author.email,
              bio: author.bio,
              is_active: author.is_active !== false, // Default to true if undefined
            });
          }
        })
        .finally(() => setLoading(false));
    } else {
      form.resetFields();
      // Set default values for new authors
      form.setFieldsValue({
        is_active: true,
      });
    }
  }, [id, form]);

  const onFinish = async (values: Partial<BlogAuthor>) => {
    try {
      setLoading(true);
      let res;
      if (id) {
        res = await updateAuthor(id, values);
      } else {
        res = await createAuthor(values);
      }
      const response = res.data;
      if (response.status && response.status.success) {
        message.success(
          response.status.message || 'Tác giả đã được lưu thành công',
        );
        onSuccess();
      } else {
        message.error(
          response.status?.message || 'Có lỗi xảy ra khi lưu tác giả',
        );
      }
    } catch (error) {
      console.error('Failed to save author:', error);
      message.error('Có lỗi xảy ra khi lưu tác giả');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <FormHeader
        title={id ? 'Chỉnh sửa tác giả' : 'Thêm tác giả mới'}
        backDestination="list"
      />
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        validateTrigger={['onBlur', 'onChange']}
      >
        <Form.Item
          name="display_name"
          label="Tên tác giả"
          rules={[{ required: true, message: 'Vui lòng nhập tên tác giả' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="email"
          label="Email"
          rules={[
            { required: true, message: 'Vui lòng nhập email' },
            { type: 'email', message: 'Email không đúng định dạng' },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item name="bio" label="Tiểu sử">
          <Input.TextArea rows={4} />
        </Form.Item>

        <Form.Item name="is_active" label="Kích hoạt" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            {id ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
