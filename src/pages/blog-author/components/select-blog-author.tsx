import { Avatar, Select, SelectProps, Spin } from 'antd';
import debounce from 'lodash/debounce';
import React, { useEffect, useMemo, useState } from 'react';
import ConsoleService from '../../../services/console.service';
import { getAuthorOptions } from '../api';
import { MODULE } from '../config';
import { BlogAuthor } from '../type';

interface Props extends SelectProps {
  isOptionAll?: boolean;
}

export const SelectBlogAuthor: React.FC<Props> = (props) => {
  const logger = ConsoleService.register(MODULE);
  const { isOptionAll = false } = props;
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<
    { label: React.ReactNode; value: string }[]
  >([]);
  const [searchValue, setSearchValue] = useState('');

  logger('[SelectBlogAuthor]', props);

  const fetchAuthors = async (search?: string) => {
    setLoading(true);
    try {
      const response = await getAuthorOptions(search);
      logger('[Authors response]', response);

      const authors = response.data || [];

      const optionItems = authors.map((author: BlogAuthor) => ({
        value: String(author.id),
        label: (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {author.avatar ? (
              <Avatar
                src={author.avatar}
                size="small"
                style={{ marginRight: 8 }}
              />
            ) : (
              <Avatar size="small" style={{ marginRight: 8 }}>
                {author.display_name?.charAt(0).toUpperCase() || 'A'}
              </Avatar>
            )}
            <span>{author.display_name}</span>
          </div>
        ),
      }));

      if (isOptionAll) {
        optionItems.unshift({
          label: 'Tất cả',
          value: '',
        });
      }

      logger('[Option items]', optionItems);
      setOptions(optionItems);
    } catch (error) {
      logger('[Error fetching authors]', error);
    } finally {
      setLoading(false);
    }
  };

  // Sử dụng debounce để tối ưu việc search
  const debouncedFetchAuthors = useMemo(
    () =>
      debounce((value: string) => {
        fetchAuthors(value);
      }, 300),
    [],
  );

  // Xử lý khi search thay đổi
  const handleSearch = (value: string) => {
    setSearchValue(value);
    debouncedFetchAuthors(value);
  };

  // Fetch authors lần đầu khi component mount
  useEffect(() => {
    fetchAuthors();

    // Clean up debounce khi unmount
    return () => {
      debouncedFetchAuthors.cancel();
    };
  }, []);

  return (
    <Select
      showSearch
      filterOption={false}
      options={options}
      loading={loading}
      onSearch={handleSearch}
      notFoundContent={loading ? <Spin size="small" /> : null}
      placeholder="Chọn tác giả"
      style={{ width: '100%' }}
      {...props}
    />
  );
};
