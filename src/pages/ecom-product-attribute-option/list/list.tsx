import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Table } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useGetPath } from '../../../hooks';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  cleanParams,
} from '../../../services/utils.service';
import {
  deleteItem,
  getAttribute,
  getAttributeOptionsByAttributeId,
  getItems,
} from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { ProductAttributeOptionModal } from '../form/modal';
import useProductAttributeOptionStore from '../store';
import { ProductAttributeOption, SwatchType } from '../type';
import { ProductAttributeOptionSearch } from './search';

interface ProductAttributeOptionListProps {
  attributeId?: string;
}

function ProductAttributeOptionList({
  attributeId,
}: ProductAttributeOptionListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const params = useParams();
  const routeAttributeId = params.attributeId;
  const effectiveAttributeId = attributeId || routeAttributeId;
  const query = queryString.parse(search);
  const { loading } = useProductAttributeOptionStore();
  const getPath = useGetPath();

  const [pagination, setPagination] = useState<any>({
    page: 1,
    page_size: 10,
  });
  const [meta, setMeta] = useState<any>({
    next_cursor: '',
    has_more: false,
  });
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<ProductAttributeOption[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [attributeName, setAttributeName] = useState<string>('');

  // Wrap fetchAttributeData in useCallback to avoid dependency issues
  const fetchAttributeData = useCallback(async () => {
    if (effectiveAttributeId) {
      try {
        const response = await getAttribute(effectiveAttributeId);
        if (response.status.success) {
          setAttributeName(response.data.name);
        }
      } catch (error) {
        logger('Error fetching attribute data', error);
      }
    }
  }, [effectiveAttributeId, logger]);

  // Use a ref to track if initial fetch has been done
  const initialFetchDone = useRef(false);
  // Use a ref to store the current attribute ID to avoid dependency issues
  const attributeIdRef = useRef<string | undefined>(effectiveAttributeId);

  // Update the ref when effectiveAttributeId changes
  useEffect(() => {
    attributeIdRef.current = effectiveAttributeId;
  }, [effectiveAttributeId]);

  const fetchData = useCallback(
    async (payload?: any) => {
      const currentAttributeId = attributeIdRef.current;

      if (!currentAttributeId) {
        // If no attribute ID is provided, fetch all options
        const params = {
          ...query,
          ...filters,
          ...pagination,
          ...payload,
        };

        const cleanedParams = cleanParams(params);
        //changeBrowserLocation(navigate, pathname, cleanedParams);

        const response = await getItems(cleanedParams);
        if (response.status.success) {
          setItems(response.data);
          setMeta(response.meta);
        } else {
          message.error(response.status.message);
        }
      } else {
        // If attribute ID is provided, fetch options for that attribute
        try {
          const response =
            await getAttributeOptionsByAttributeId(currentAttributeId);
          if (response.status.success) {
            setItems(response.data);
            // No pagination for attribute-specific options
            setMeta({
              next_cursor: '',
              has_more: false,
            });
          } else {
            message.error(response.status.message);
          }
        } catch (error) {
          logger('Error fetching attribute options', error);
          message.error('Failed to fetch attribute options');
        }
      }
    },
    [filters, query, navigate, pathname, pagination, logger],
  );

  // Memoize fetchAttributeData to avoid dependency issues
  const fetchAttributeDataMemo = useCallback(async () => {
    if (attributeIdRef.current) {
      return fetchAttributeData();
    }
  }, [fetchAttributeData]);

  // Use a separate effect for handling attribute ID changes
  useEffect(() => {
    // Only fetch data when effectiveAttributeId changes and initial fetch is done
    if (initialFetchDone.current && effectiveAttributeId) {
      fetchData();
      fetchAttributeDataMemo();
    }
  }, [effectiveAttributeId, fetchData, fetchAttributeDataMemo]);

  // Initial data fetch on component mount
  useEffectOnce(() => {
    const initialLoad = async () => {
      await fetchData();
      if (effectiveAttributeId) {
        await fetchAttributeDataMemo();
      }
      initialFetchDone.current = true;
    };

    initialLoad();
  });

  // Handle pagination changes
  useEffect(() => {
    // Only fetch when pagination changes and cursor is not empty
    // Also make sure we're not in the initial loading phase
    if (initialFetchDone.current && pagination.cursor !== '') {
      fetchData();
    }
  }, [pagination, fetchData]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData({ ...values });
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.option_id.toString());
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.option_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        if (effectiveAttributeId) {
          navigate(
            getPath(`/${MODULE}/create?attribute_id=${effectiveAttributeId}`),
          );
        } else {
          navigate(`/${MODULE}/create`);
        }
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  // Handle pagination changes - page and pageSize params are required by Ant Design Table
  const handlePagination = (_page: number, _pageSize: number) => {
    // Only use the next_cursor from meta for cursor-based pagination
    setPagination({ cursor: meta.next_cursor });
  };

  const handleBackToAttributes = () => {
    navigate('/ecom-product-attribute');
  };

  const columns = [
    {
      title: t('value'),
      dataIndex: 'value',
      key: 'value',
    },
    {
      title: t('label'),
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: t('swatchType'),
      dataIndex: 'swatch_type',
      key: 'swatch_type',
      render: (swatchType: string) =>
        swatchType ? <span>{t(`swatchType.${swatchType}`)}</span> : '-',
    },
    {
      title: t('swatchValue'),
      dataIndex: 'swatch_value',
      key: 'swatch_value',
      render: (swatchValue: string, record: ProductAttributeOption) => {
        if (!swatchValue) return '-';

        if (record.swatch_type === SwatchType.COLOR) {
          return (
            <div
              style={{
                backgroundColor: swatchValue,
                width: '24px',
                height: '24px',
                borderRadius: '4px',
                border: '1px solid #ddd',
              }}
            />
          );
        } else if (record.swatch_type === SwatchType.IMAGE) {
          return (
            <img
              src={swatchValue}
              alt={record.value}
              style={{ width: '24px', height: '24px' }}
            />
          );
        }

        return swatchValue;
      },
    },
    {
      title: t('displayOrder'),
      dataIndex: 'display_order',
      key: 'display_order',
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.option_id.toString())}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">
          {effectiveAttributeId
            ? `${t('module')} - ${attributeName}`
            : t('module')}
        </div>
        <div className="flex gap-4">
          {effectiveAttributeId && (
            <Button onClick={handleBackToAttributes}>
              {t('btnBackToAttributes')}
            </Button>
          )}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>

      {!effectiveAttributeId && (
        <div className="p-4 bg-white shadow">
          <ProductAttributeOptionSearch onSearch={handleFilters} />
        </div>
      )}

      <div className="mt-4">
        <Table
          columns={columns}
          dataSource={items}
          rowKey="option_id"
          loading={loading}
          pagination={
            !effectiveAttributeId
              ? {
                  current: pagination.page,
                  pageSize: pagination.page_size,
                  total: meta.total,
                  onChange: handlePagination,
                  showSizeChanger: true,
                }
              : false
          }
        />
      </div>

      {showModal && (
        <ProductAttributeOptionModal
          open={showModal}
          id={idCurrent}
          attributeId={effectiveAttributeId}
          onClose={handleModal}
        />
      )}
    </div>
  );
}

export default ProductAttributeOptionList;
