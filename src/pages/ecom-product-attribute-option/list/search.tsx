import { Button, Col, Form, Input, Row, Select, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getAllAttributes } from '../api';
import { MODULE } from '../config';
import { useNavigateTenant } from '../../../hooks';
import { SwatchType } from '../type';

const FormItem = Form.Item;

interface ProductAttributeOptionSearchProps {
  onSearch: (values: any) => void;
}

export function ProductAttributeOptionSearch({
  onSearch,
}: ProductAttributeOptionSearchProps) {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();
  const [attributes, setAttributes] = useState<any[]>([]);

  useEffect(() => {
    const fetchAttributes = async () => {
      try {
        const response = await getAllAttributes();
        if (response.status.success) {
          setAttributes(response.data);
        }
      } catch (error) {
        console.error('Error fetching attributes:', error);
      }
    };

    fetchAttributes();
  }, []);

  const onFinish = (values: any) => {
    onSearch(values);
  };

  const resetForm = () => {
    form.resetFields();
    onSearch({});
  };

  return (
    <Form
      form={form}
      name="search"
      onFinish={onFinish}
      layout="vertical"
      autoComplete="off"
    >
      <Row gutter={16}>
        <Col xs={24} md={8} lg={6}>
          <FormItem label={t('value')} name="value">
            <Input placeholder={t('search.placeholder')} />
          </FormItem>
        </Col>

        <Col xs={24} md={8} lg={6}>
          <FormItem label={t('label')} name="label">
            <Input placeholder={t('search.placeholder')} />
          </FormItem>
        </Col>

        <Col xs={24} md={8} lg={6}>
          <FormItem label={t('search.attribute')} name="attribute_id">
            <Select
              allowClear
              placeholder={t('search.attribute')}
              options={attributes.map((attr) => ({
                value: attr.attribute_id,
                label: attr.name,
              }))}
            />
          </FormItem>
        </Col>

        <Col xs={24} md={8} lg={6}>
          <FormItem label={t('search.swatchType')} name="swatch_type">
            <Select
              allowClear
              placeholder={t('search.swatchType')}
              options={Object.values(SwatchType).map((type) => ({
                value: type,
                label: t(`swatchType.${type}`),
              }))}
            />
          </FormItem>
        </Col>
      </Row>

      <Row>
        <Col>
          <Space>
            <Button type="primary" htmlType="submit">
              {t('search.searchBtn')}
            </Button>
            <Button onClick={resetForm}>{t('search.resetBtn')}</Button>
          </Space>
        </Col>
      </Row>
    </Form>
  );
}
