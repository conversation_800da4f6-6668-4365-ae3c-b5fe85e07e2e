import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, message, Space } from 'antd';
import queryString from 'query-string';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { useGetPath, useNavigateTenant } from '../../../hooks';
import { MODULE } from '../config';
import ProductAttributeOptionForm from './form';

export default function ProductAttributeOptionFormPage() {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const getPath = useGetPath();
  const { optionId, attributeId } = useParams();
  const location = useLocation();
  const query = queryString.parse(location.search);
  const queryAttributeId = query.attribute_id as string;
  const formRef = useRef<any>();

  const handleBack = () => {
    // If we have an attribute ID, go back to the options list for that attribute
    if (attributeId || queryAttributeId) {
      navigate(
        getPath(
          `/ecom-product-attribute/${attributeId || queryAttributeId}/options`,
        ),
      );
    } else {
      navigate(`/${MODULE}`);
    }
  };

  const handleSubmit = () => {
    if (formRef.current) {
      formRef.current.submitForm();
    }
  };

  const handleChange = (reload: boolean) => {
    if (reload) {
      message.success(
        optionId ? t('form.updateSuccess') : t('form.addSuccess'),
      );

      // If we have an attribute ID, go back to the options list for that attribute
      if (attributeId || queryAttributeId) {
        navigate(
          getPath(
            `/ecom-product-attribute/${attributeId || queryAttributeId}/options`,
          ),
        );
      } else {
        navigate(`/${MODULE}`);
      }
    }
  };

  const isEdit = optionId && optionId !== 'create';
  const title = isEdit ? t('form.updateTitle') : t('form.addTitle');

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{title}</div>
        <div className="flex gap-4">
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              {t('btnBack')}
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
            >
              {t('btnSave')}
            </Button>
          </Space>
        </div>
      </div>

      <div className="p-6 bg-white">
        <ProductAttributeOptionForm
          id={isEdit ? optionId : undefined}
          attributeId={attributeId || queryAttributeId}
          onChange={handleChange}
          ref={formRef}
        />
      </div>
    </div>
  );
}
