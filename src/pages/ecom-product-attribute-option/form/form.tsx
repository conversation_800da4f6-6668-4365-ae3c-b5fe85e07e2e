import { Col, Form, Input, InputNumber, message, Row, Select } from 'antd';
import _ from 'lodash';
import queryString from 'query-string';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import ConsoleService from '../../../services/console.service';
import {
  createItem,
  getAllAttributes,
  getAttribute,
  getItem,
  updateItem,
} from '../api';
import { MODULE } from '../config';
import { ProductAttributeOption, SwatchType } from '../type';

const FormItem = Form.Item;
const { Option } = Select;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
  attributeId?: string;
}

const ProductAttributeOptionForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id, attributeId: propAttributeId }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<ProductAttributeOption>();
    const [formValues, setFormValues] = useState<ProductAttributeOption>();
    const [attributes, setAttributes] = useState<any[]>([]);
    const [selectedSwatchType, setSelectedSwatchType] = useState<string | null>(
      null,
    );
    const location = useLocation();
    const query = queryString.parse(location.search);
    const queryAttributeId = query.attribute_id as string;
    const effectiveAttributeId = propAttributeId || queryAttributeId;

    const initForm = {
      display_order: 0,
      swatch_type: null,
    };

    const fetchAttributes = useCallback(async () => {
      try {
        const res = await getAllAttributes();
        if (res.status.success) {
          setAttributes(res.data);

          // If we have an attribute ID from props or query, set it in the form
          if (effectiveAttributeId && isNew) {
            form.setFieldsValue({
              attribute_id: parseInt(effectiveAttributeId),
            });

            // Also fetch the attribute details to show in UI
            try {
              const attrRes = await getAttribute(effectiveAttributeId);
              if (attrRes.status.success) {
                logger('Attribute details:', attrRes.data);
              }
            } catch (error) {
              logger('Error fetching attribute details', error);
            }
          }
        }
      } catch (error) {
        logger('Error fetching attributes', error);
      }
    }, [effectiveAttributeId, form, isNew, logger]);

    const getItemData = useCallback(
      async (_id: string) => {
        const res = await getItem(_id);
        if (res.status.success) {
          setItem(res.data);
          form.setFieldsValue(res.data);
          if (res.data.swatch_type) {
            setSelectedSwatchType(res.data.swatch_type);
          }
        } else {
          message.error(res.status.message);
        }
      },
      [form],
    );

    useEffect(() => {
      fetchAttributes();
    }, [fetchAttributes]);

    useEffect(() => {
      logger('ID:', id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
        form.setFieldsValue(initForm);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id, form, getItemData, initForm, logger]);

    const onFinish = async (values: ProductAttributeOption) => {
      try {
        let res;
        if (isNew) {
          res = await createItem(values);
          if (res.status.success) {
            message.success(t('form.addSuccess'));
          }
        } else {
          res = await updateItem(id!, values);
          if (res.status.success) {
            message.success(t('form.updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('form.submitError'),
        );
      }
    };

    const handleValuesChange = (changedValues: any, allValues: any) => {
      logger(changedValues);
      logger(allValues);
      setFormValues(allValues);

      // Update selected swatch type state when it changes
      if (changedValues.swatch_type) {
        setSelectedSwatchType(changedValues.swatch_type);
      }
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.attribute')}
                name="attribute_id"
                rules={[
                  { required: true, message: t('form.pleaseSelectAttribute') },
                ]}
              >
                <Select
                  placeholder={t('form.selectAttribute')}
                  disabled={!isNew || !!effectiveAttributeId}
                  options={attributes.map((attr) => ({
                    value: attr.attribute_id,
                    label: attr.name,
                  }))}
                />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.value')}
                name="value"
                rules={[{ required: true, message: t('form.pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem label={t('form.label')} name="label">
                <Input />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem label={t('form.displayOrder')} name="display_order">
                <InputNumber style={{ width: '100%' }} min={0} />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem label={t('form.swatchType')} name="swatch_type">
                <Select
                  allowClear
                  placeholder={t('form.selectSwatchType')}
                  options={Object.values(SwatchType).map((type) => ({
                    value: type,
                    label: t(`swatchType.${type}`),
                  }))}
                />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem label={t('form.swatchValue')} name="swatch_value">
                {selectedSwatchType === SwatchType.COLOR ? (
                  <Input
                    type="color"
                    style={{ width: '100%', height: '32px' }}
                  />
                ) : selectedSwatchType === SwatchType.IMAGE ? (
                  <Input placeholder="Enter image URL" />
                ) : (
                  <Input />
                )}
              </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
    );
  },
);

ProductAttributeOptionForm.displayName = 'ProductAttributeOptionForm';

export default ProductAttributeOptionForm;
