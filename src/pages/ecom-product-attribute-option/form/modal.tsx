import { Button, Modal } from 'antd';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';
import { useNavigateTenant } from '../../../hooks';
import ProductAttributeOptionForm from './form';

interface ProductAttributeOptionModalProps {
  open: boolean;
  id?: string;
  attributeId?: string;
  onClose: () => void;
}

export function ProductAttributeOptionModal({
  open,
  id,
  attributeId,
  onClose,
}: ProductAttributeOptionModalProps) {
  const { t } = useTranslation(MODULE);
  const formRef = useRef<any>();

  const handleChange = (reload: boolean) => {
    if (reload) {
      onClose();
    }
  };

  const handleSubmit = () => {
    if (formRef.current) {
      formRef.current.submitForm();
    }
  };

  const title = id ? t('form.updateTitle') : t('form.addTitle');

  return (
    <Modal
      title={title}
      open={open}
      width="800px"
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          {t('btnCancel')}
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {t('btnSave')}
        </Button>,
      ]}
    >
      <ProductAttributeOptionForm
        id={id}
        attributeId={attributeId}
        onChange={handleChange}
        ref={formRef}
      />
    </Modal>
  );
}
