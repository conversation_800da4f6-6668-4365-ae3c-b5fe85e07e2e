import { ArrowLeftOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Card, Descriptions, message, Space, Spin, Tag } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import { getItem } from '../api';
import { MODULE } from '../config';
import { ProductAttribute } from '../type';

export default function ProductAttributeDetail() {
  const { t } = useTranslation(MODULE);
  const { attributeId } = useParams();
  const navigate = useNavigateTenant();
  const [loading, setLoading] = useState<boolean>(false);
  const [item, setItem] = useState<ProductAttribute | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!attributeId) return;

      setLoading(true);
      try {
        const response = await getItem(attributeId);
        if (response.status.success) {
          setItem(response.data);
        } else {
          message.error(response.status.message);
        }
      } catch (error) {
        console.error('Error fetching attribute:', error);
        message.error('Không thể tải dữ liệu thuộc tính');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [attributeId]);

  const handleBack = () => {
    navigate(`/${MODULE}`);
  };

  const handleEdit = () => {
    navigate(`/${MODULE}/${attributeId}/edit`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!item) {
    return (
      <div className="p-4">
        <Card title={t('detailNotFound')}>
          <p>{t('detailNotFoundDesc')}</p>
          <Button type="primary" onClick={handleBack}>
            {t('btnBack')}
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{item.name}</div>
        <div className="flex gap-4">
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              {t('btnBack')}
            </Button>
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
              {t('btnEdit')}
            </Button>
          </Space>
        </div>
      </div>

      <div className="p-6 bg-white">
        <Descriptions bordered column={2}>
          <Descriptions.Item label={t('name')}>{item.name}</Descriptions.Item>
          <Descriptions.Item label={t('code')}>{item.code}</Descriptions.Item>
          <Descriptions.Item label={t('type')}>
            {t(`attributeType.${item.type}`)}
          </Descriptions.Item>
          <Descriptions.Item label={t('group')}>
            {item.group_id || '-'}
          </Descriptions.Item>
          <Descriptions.Item label={t('unit')}>
            {item.unit || '-'}
          </Descriptions.Item>
          <Descriptions.Item label={t('validationRules')}>
            {item.validation_rules || '-'}
          </Descriptions.Item>
          <Descriptions.Item label={t('isConfigurable')}>
            <Tag color={item.is_configurable ? 'green' : 'red'}>
              {item.is_configurable ? 'Có' : 'Không'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('isFilterable')}>
            <Tag color={item.is_filterable ? 'green' : 'red'}>
              {item.is_filterable ? 'Có' : 'Không'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('isSearchable')}>
            <Tag color={item.is_searchable ? 'green' : 'red'}>
              {item.is_searchable ? 'Có' : 'Không'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('isComparable')}>
            <Tag color={item.is_comparable ? 'green' : 'red'}>
              {item.is_comparable ? 'Có' : 'Không'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('isRequired')}>
            <Tag color={item.is_required ? 'green' : 'red'}>
              {item.is_required ? 'Có' : 'Không'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label={t('frontendInput')}>
            {item.frontend_input || '-'}
          </Descriptions.Item>
          <Descriptions.Item label={t('displayOrder')}>
            {item.display_order}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </div>
  );
}
