import { Button, Col, Form, Input, message, Row, Select, Spin } from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormHeader } from '../../../components';
import ConsoleService from '../../../services/console.service';
import { SelectRbacPermissionGroup } from '../../rbac-permission-group/components/select-rbac-permission-group';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import useRbacPermissionStore from '../store';
import {
  RbacPermission,
  RbacPermissionCreate,
  RbacPermissionUpdate,
} from '../type';

const FormItem = Form.Item;
const { Option } = Select;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading: submitLoading } = useRbacPermissionStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<RbacPermission>();
  const [loading, setLoading] = useState<boolean>(false);

  const getItemData = useCallback(
    async (_id: string) => {
      try {
        setLoading(true);
        const res = await getItem(_id);
        if (res.status.success) {
          setItem(res.data);
          form.setFieldsValue(res.data);
        } else {
          message.error(res.status.message);
        }
      } catch (error) {
        logger('Error fetching permission data:', error);
        message.error(t('loadError'));
      } finally {
        setLoading(false);
      }
    },
    [form, t, logger],
  );

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
      form.resetFields();
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id, getItemData, form]);

  const onFinish = async (
    values: RbacPermissionCreate | RbacPermissionUpdate,
  ) => {
    try {
      // Convert group_id from string to number if it exists
      if (values.group_id) {
        values.group_id = Number(values.group_id);
      }

      logger('Submitting form with values:', values);

      let res;
      if (isNew) {
        const createPayload = values as RbacPermissionCreate;
        res = await createItem(createPayload);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        const updatePayload = values as RbacPermissionUpdate;
        res = await updateItem(id!, updatePayload);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') ?? t('submitError'),
      );
    }
  };

  return (
    <div>
      <FormHeader
        title={isNew ? t('addNewPermission') : t('editPermission')}
        backDestination="/rbac-permission"
      />
      <Spin spinning={loading} tip={t('pleaseWait') || t('inputData')}>
        <Form
          style={{ marginTop: 8 }}
          form={form}
          name="form"
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
        >
          <div className="form_content">
            <Row gutter={16}>
              <Col xs={24} lg={24}>
                <FormItem
                  label={t('group')}
                  name="group_id"
                  rules={[{ required: false, message: t('pleaseEnterData') }]}
                >
                  <SelectRbacPermissionGroup />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('name')}
                  name="permission_name"
                  rules={[{ required: true, message: t('pleaseEnterData') }]}
                >
                  <Input />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('code')}
                  name="permission_code"
                  rules={[
                    {
                      required: isNew,
                      message: t('pleaseEnterData'),
                    },
                  ]}
                >
                  <Input disabled={!isNew} />
                </FormItem>
              </Col>

              <Col xs={24} lg={24}>
                <FormItem
                  label={t('description')}
                  name="permission_description"
                  rules={[{ required: false, message: t('pleaseEnterData') }]}
                >
                  <Input.TextArea rows={4} />
                </FormItem>
              </Col>
            </Row>
          </div>
          <div className="form_footer">
            <FormItem style={{ marginTop: 32, textAlign: 'center' }}>
              <Button type="primary" htmlType="submit" loading={submitLoading}>
                {isNew ? t('btnAdd') : t('btnUpdate')}
              </Button>
            </FormItem>
          </div>
        </Form>
      </Spin>
    </div>
  );
};

export default IndexForm;
