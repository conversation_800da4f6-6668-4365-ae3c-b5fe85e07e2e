import { Card } from 'antd';
import React from 'react';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import IndexForm from './form';

const Detail: React.FC = (props) => {
  const { id } = useParams();
  // const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();

  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    navigate(`/${MODULE}/`);
  };

  return (
    <div>
      <Card bordered={false}>
        <IndexForm onChange={handleChangeForm} id={id}></IndexForm>
      </Card>
    </div>
  );
};

export default Detail;
