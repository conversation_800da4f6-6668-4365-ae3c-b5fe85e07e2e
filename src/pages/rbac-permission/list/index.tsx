import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import CursorPaginationV2 from '../../../components/pagination/cursor-pagination-v2';
import { ListHeader } from '../../../components';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useRbacPermissionStore from '../store';
import { RbacPermission } from '../type';
import Search from './search';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useRbacPermissionStore();

  const [cursorAfter, setCursorAfter] = useState<string | null>(null);
  const [cursorHistory, setCursorHistory] = useState<string[]>([]);

  const isFirstPage = cursorHistory.length === 0;

  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<RbacPermission[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [limit] = useState<number>(10);

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        limit,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      console.log('🔍 API Request params:', cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        console.log('✅ API Response meta:', response.meta);
        setItems(response.data);
        // Update cursor based on the new API response format
        const nextCursor = response.meta.next_cursor ?? null;
        console.log('🔄 Setting next cursor:', nextCursor);
        setCursorAfter(nextCursor);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, limit],
  );

  const handleNext = () => {
    if (cursorAfter) {
      const currentCursor = cursorAfter;
      console.log('🔄 Next clicked - Current cursor:', currentCursor);
      console.log('📚 Current history:', cursorHistory);
      setCursorHistory((prev) => {
        const newHistory = [...prev, currentCursor];
        console.log('📚 New history:', newHistory);
        return newHistory;
      });
      fetchData({ cursor: cursorAfter });
    }
  };

  const handleBack = () => {
    console.log('⬅️ Back clicked - Current history:', cursorHistory);
    if (cursorHistory.length > 0) {
      // Remove the last cursor from history
      setCursorHistory((prev) => {
        const newHistory = prev.slice(0, -1);
        console.log('📚 New history after back:', newHistory);
        return newHistory;
      });

      if (cursorHistory.length <= 1) {
        // Go back to first page
        console.log('🏠 Going to first page');
        fetchData();
      } else {
        // Go to previous page using the previous cursor
        const previousCursor = cursorHistory[cursorHistory.length - 2];
        console.log('⬅️ Going to previous page with cursor:', previousCursor);
        fetchData({ cursor: previousCursor });
      }
    }
  };

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData(values);
  };

  useEffectOnce(() => {
    setCursorHistory([]);
    fetchData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.permission_id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.permission_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    {
      title: t('id'),
      dataIndex: 'permission_id',
      key: 'permission_id',
      width: 80,
      render: (dom: any, record: any) => (
        <a
          href={`/${MODULE}/${record.permission_id}`}
          className="text-blue-600"
          onClick={(e) => {
            e.preventDefault();
            navigate(`/${MODULE}/${record.permission_id}`);
          }}
        >
          {dom}
        </a>
      ),
    },
    {
      title: t('group'),
      dataIndex: ['group', 'permission_group_name'],
      key: 'group.permission_group_name',
      //width: 100,
      render: (_: any, record: any) =>
        record.group?.permission_group_name || '-',
    },
    {
      title: t('code'),
      dataIndex: 'permission_code',
      key: 'permission_code',
      //width: 200,
    },
    {
      title: t('name'),
      dataIndex: 'permission_name',
      key: 'permission_name',
      //width: 200,
    },
    {
      title: t('description'),
      dataIndex: 'permission_description',
      key: 'permission_description',
      ellipsis: true,
    },

    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.permission_id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <ListHeader
        title={t('title')}
        module={MODULE}
        onAddClick={() => handleActions('add', null)}
      />
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPaginationV2
              onBack={handleBack}
              onNext={handleNext}
              disabledBack={isFirstPage}
              disabledNext={!cursorAfter}
              loading={loading}
              isFirstPage={isFirstPage}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
      </Space>
    </div>
  );
}
