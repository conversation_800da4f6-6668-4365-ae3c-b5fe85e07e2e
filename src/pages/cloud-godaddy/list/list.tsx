import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import moment from 'moment';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { CloudGodaddyModal } from '../form/modal';
import useCloudGodaddyStore from '../store';
import { CloudGodaddy } from '../type';
import { CloudGodaddySearch } from './search';

// Thêm hàm changeBrowserLocationtạm thời
const changeBrowserLocation = (
  navigate: any,
  pathname: string,
  params: any,
) => {
  const queryString = new URLSearchParams(params).toString();
  navigate(`${pathname}?${queryString}`);
};

function CloudGodaddyList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useCloudGodaddyStore();

  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<CloudGodaddy[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.meta.total);
        setNextCursor(response.meta.cursors.after);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname],
  );

  useEffect(() => {
    fetchData({ after: afterKey });
  }, [fetchData, afterKey]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });

    setFilters(values);
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    // {
    //   title: t('form.createdAt'),
    //   dataIndex: 'createdAt',
    //   key: 'createdAt',
    //   render: (value: string) => moment(value).format('DD/MM/YYYY'),
    // },
    {
      title: t('form.domain'),
      dataIndex: 'domain',
      key: 'domain',
    },
    // {
    //   title: t('form.domainId'),
    //   dataIndex: 'domainId',
    //   key: 'domainId',
    // },
    // {
    //   title: t('form.expirationProtected'),
    //   dataIndex: 'expirationProtected',
    //   key: 'expirationProtected',
    //   render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    // },

    // {
    //   title: t('form.exposeWhois'),
    //   dataIndex: 'exposeWhois',
    //   key: 'exposeWhois',
    //   render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    // },
    // {
    //   title: t('form.holdRegistrar'),
    //   dataIndex: 'holdRegistrar',
    //   key: 'holdRegistrar',
    //   render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    // },
    // {
    //   title: t('form.locked'),
    //   dataIndex: 'locked',
    //   key: 'locked',
    //   render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    // },
    // {
    //   title: t('form.nameServers'),
    //   dataIndex: 'nameServers',
    //   key: 'nameServers',
    // },
    // {
    //   title: t('form.privacy'),
    //   dataIndex: 'privacy',
    //   key: 'privacy',
    //   render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    // },
    {
      title: t('form.registrarCreatedAt'),
      dataIndex: 'registrarCreatedAt',
      key: 'registrarCreatedAt',
      render: (value: string) => moment(value).format('DD/MM/YYYY'),
    },

    {
      title: t('form.expires'),
      dataIndex: 'expires',
      key: 'expires',
      render: (value: string) => moment(value).format('DD/MM/YYYY'),
    },
    {
      title: t('form.renewAuto'),
      dataIndex: 'renewAuto',
      key: 'renewAuto',
      render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    },
    {
      title: t('form.renewDeadline'),
      dataIndex: 'renewDeadline',
      key: 'renewDeadline',
      render: (value: string) => moment(value).format('DD/MM/YYYY'),
    },
    // {
    //   title: t('form.renewable'),
    //   dataIndex: 'renewable',
    //   key: 'renewable',
    //   render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    // },
    {
      title: t('form.status'),
      dataIndex: 'status',
      key: 'status',
      render: (value: string) => t(`status.${value}`),
    },
    // {
    //   title: t('form.transferAwayEligibleAt'),
    //   dataIndex: 'transferAwayEligibleAt',
    //   key: 'transferAwayEligibleAt',
    // },
    // {
    //   title: t('form.transferProtected'),
    //   dataIndex: 'transferProtected',
    //   key: 'transferProtected',
    //   render: (value: boolean) => (value ? t('form.yes') : t('form.no')),
    // },
    {
      title: t('list.actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('list.btnBuy')}
          </Button>

          {/* <Button
            type="dashed"
            icon={<ImportOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnImport')}
          </Button>

          <Dropdown menu={{
            items: [
              {
                key: '1',
                label: <a href="#">Chức năng 1</a>,
              },
              {
                key: '2',
                label: <a href="#">Chức năng 2</a>,
              },
              {
                key: '3',
                label: <a href="#">Chức năng 3</a>,
              },
            ]
          }} trigger={['click']}>
            <Button icon={<EllipsisOutlined />} />
          </Dropdown> */}
        </div>
      </div>
      <CloudGodaddySearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></CloudGodaddySearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
              showTotal={false}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <CloudGodaddyModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></CloudGodaddyModal>
        )}
      </Space>
    </div>
  );
}
export { CloudGodaddyList };
