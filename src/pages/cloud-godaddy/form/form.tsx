import { Button, Col, Form, Input, message, Row } from 'antd';
import _ from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import {
  checkDomainsAvailability,
  createItem,
  getItem,
  updateItem,
} from '../api';
import { MODULE } from '../config';
import { CloudGodaddy } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const CloudGodaddyForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<CloudGodaddy>();
    const [formValues, setFormValues] = useState<CloudGodaddy>();
    const [domain, setDomain] = useState('');

    const initForm = {};

    const getItemData = async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    };

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id]);

    const onFinish = async (values: CloudGodaddy) => {
      try {
        let res;
        if (isNew) {
          res = await createItem(values);
          if (res.status.success) {
            message.success(t('addSuccess'));
          }
        } else {
          res = await updateItem(id!, values);
          if (res.status.success) {
            message.success(t('updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('submitError'),
        );
      }
    };

    const handleValuesChange = (newValue: any, allValues: any) => {
      logger(newValue);
      logger(allValues);
      setFormValues(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    const handleCheckDomain = async () => {
      await checkDomainsAvailability({
        domains: [domain],
      });
    };

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('form.domain')}
                name="domain"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input.TextArea
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  rows={8}
                />
              </FormItem>
            </Col>

            <Button onClick={handleCheckDomain}>{t('Check Domain')}</Button>
          </Row>
        </div>
      </Form>
    );
  },
);
CloudGodaddyForm.displayName = 'CloudGodaddyForm';

export { CloudGodaddyForm };
