import {
  ArrowLeftOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Descriptions,
  message,
  Popconfirm,
  Row,
  Spin,
  Tag,
} from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import { deleteItem, getItem } from '../api';
import { MODULE } from '../config';
import useMenuStore from '../store';
import { MenuItem, STATUS } from '../type';

export default function MenuDetail() {
  const { id } = useParams();
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { loading, setLoading } = useMenuStore();
  const [menuItem, setMenuItem] = useState<MenuItem | null>(null);

  useEffect(() => {
    async function fetchData() {
      if (!id) return;

      setLoading(true);
      try {
        const response = await getItem(id);
        if (response.status.success) {
          setMenuItem(response.data);
        } else {
          message.error(response.status.message);
        }
      } catch (error) {
        console.error('Error fetching menu item details:', error);
        message.error('Có lỗi xảy ra khi tải thông tin mục thực đơn');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [id, setLoading]);

  const handleDelete = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await deleteItem(id);
      if (response.status.success) {
        message.success('Xóa mục thực đơn thành công');
        navigate(`/${MODULE}`);
      } else {
        message.error(response.status.message);
      }
    } catch (error) {
      console.error('Error deleting menu item:', error);
      message.error('Có lỗi xảy ra khi xóa mục thực đơn');
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status?: string) => {
    const statusObj = STATUS.find((s) => s.value === status);
    if (!statusObj) return null;

    return <Tag color={statusObj.color}>{statusObj.label}</Tag>;
  };

  const formatPrice = (price?: number) => {
    if (!price && price !== 0) return '-';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '-';
    return new Date(dateStr).toLocaleString('vi-VN');
  };

  if (loading && !menuItem) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-4">
      <Row className="mb-4">
        <Col span={24} className="flex justify-between items-center">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(`/${MODULE}`)}
          >
            Quay lại
          </Button>
          <div>
            <Button
              type="primary"
              icon={<EditOutlined />}
              className="mr-2"
              onClick={() => navigate(`/${MODULE}/${id}/edit`)}
            >
              Chỉnh sửa
            </Button>
            <Popconfirm
              title="Bạn có chắc chắn muốn xóa mục thực đơn này?"
              onConfirm={handleDelete}
              okText="Xóa"
              cancelText="Hủy"
            >
              <Button danger icon={<DeleteOutlined />}>
                Xóa
              </Button>
            </Popconfirm>
          </div>
        </Col>
      </Row>

      <Card>
        <Row gutter={16}>
          <Col span={16}>
            <Descriptions title="Thông tin mục thực đơn" bordered column={1}>
              <Descriptions.Item label="ID">{menuItem?.id}</Descriptions.Item>
              <Descriptions.Item label="Tên">
                {menuItem?.name}
              </Descriptions.Item>
              <Descriptions.Item label="Đường dẫn">
                {menuItem?.slug || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Giá">
                {formatPrice(menuItem?.price)}
              </Descriptions.Item>
              <Descriptions.Item label="Giá khuyến mãi">
                {menuItem?.sale_price
                  ? formatPrice(menuItem?.sale_price)
                  : 'Không có'}
              </Descriptions.Item>
              <Descriptions.Item label="Trạng thái">
                {getStatusTag(menuItem?.status)}
              </Descriptions.Item>
              <Descriptions.Item label="Kích hoạt">
                {menuItem?.is_active ? 'Có' : 'Không'}
              </Descriptions.Item>
              <Descriptions.Item label="Nổi bật">
                {menuItem?.is_featured ? 'Có' : 'Không'}
              </Descriptions.Item>
              <Descriptions.Item label="Có sẵn để phục vụ">
                {menuItem?.is_available ? 'Có' : 'Không'}
              </Descriptions.Item>
              <Descriptions.Item label="Thời gian chế biến">
                {menuItem?.estimated_cooking_time
                  ? `${menuItem.estimated_cooking_time} phút`
                  : 'Không xác định'}
              </Descriptions.Item>
              <Descriptions.Item label="Mô tả">
                {menuItem?.description || 'Không có mô tả'}
              </Descriptions.Item>
              <Descriptions.Item label="Ngày tạo">
                {formatDate(menuItem?.created_at)}
              </Descriptions.Item>
              <Descriptions.Item label="Cập nhật lần cuối">
                {formatDate(menuItem?.updated_at)}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            {menuItem?.image && (
              <Card title="Hình ảnh">
                <img
                  src={menuItem.image}
                  alt={menuItem.name}
                  style={{ width: '100%', objectFit: 'cover' }}
                />
              </Card>
            )}
          </Col>
        </Row>
      </Card>
    </div>
  );
}
