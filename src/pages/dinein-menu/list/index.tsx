import { PlusCircleOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, message, Spin } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import { getTree } from '../api';
import { MODULE_NAME } from '../config';
import useMenuStore from '../store';
import { MenuItem } from '../type';
import MenuTree from './tree';

export default function MenuList() {
  const { t } = useTranslation();
  const navigate = useNavigateTenant();
  const { loading, setLoading } = useMenuStore();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);

  const fetchMenuTree = async () => {
    setLoading(true);
    try {
      const res = await getTree();
      if (res.status.success) {
        setMenuItems(res.data);
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      console.error('Error fetching menu tree:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu thực đơn');
    } finally {
      setLoading(false);
    }
  };

  useEffectOnce(() => {
    fetchMenuTree();
  });

  const handleCreate = () => {
    navigate(`/${MODULE_NAME}/create`);
  };

  return (
    <div className="p-4">
      <Card
        title="Quản lý thực đơn"
        extra={
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={handleCreate}
          >
            Thêm mục thực đơn
          </Button>
        }
      >
        {loading ? (
          <div className="flex justify-center py-8">
            <Spin size="large" />
          </div>
        ) : (
          <MenuTree data={menuItems} onRefresh={fetchMenuTree} />
        )}
      </Card>
    </div>
  );
}
