import {
  DeleteOutlined,
  DragOutlined,
  EditOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Button, message, Popconfirm, Space, Tag, Typography } from 'antd';
import classNames from 'classnames';
import { useState } from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import {
  deleteItem,
  moveMenuItem,
  moveMenuItemRoot,
  updateMenuPosition,
} from '../api';
import { MODULE } from '../config';
import useMenuStore from '../store';
import { MenuItem, STATUS } from '../type';

const { Text } = Typography;

interface Props {
  data: MenuItem[];
  onRefresh?: () => void;
}

export default function MenuTree({ data, onRefresh }: Props) {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const { loading, setLoading } = useMenuStore();

  const handleEdit = (id: string) => {
    navigate(`/${MODULE}/${id}`);
  };

  const handleAdd = (parentId?: number) => {
    if (parentId) {
      navigate(`/${MODULE}/create?parentId=${parentId}`);
    } else {
      navigate(`/${MODULE}/create`);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      setLoading(true);
      const res = await deleteItem(id);
      if (res.status.success) {
        message.success(t('deleteSuccess'));
        if (onRefresh) onRefresh();
      } else {
        message.error(res.status.message || 'Xóa thất bại');
      }
    } catch (error) {
      console.error(error);
      message.error('Có lỗi xảy ra');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price?: number) => {
    if (!price && price !== 0) return '';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  };

  const getStatusLabel = (status?: string) => {
    const statusObj = STATUS.find((s) => s.value === status);
    return statusObj ? (
      <Tag color={statusObj.color} className="ml-2">
        {statusObj.label}
      </Tag>
    ) : null;
  };

  const handleDragEnd = async (result: any) => {
    const { source, destination, draggableId } = result;

    if (!destination) {
      return;
    }

    const sourceDroppableId = source.droppableId;
    const destinationDroppableId = destination.droppableId;
    const draggedItemId = parseInt(draggableId.split('-')[1], 10);

    try {
      setLoading(true);

      // Moving within the same parent
      if (sourceDroppableId === destinationDroppableId) {
        // Find next item at the destination to use as target
        const targetArray =
          sourceDroppableId === 'root'
            ? data.filter((item) => !item.parent_id)
            : data.find(
                (item) =>
                  item.id === parseInt(sourceDroppableId.split('-')[1], 10),
              )?.children || [];

        // If moving down, use the item at destination index as target
        // If moving up, use the item at destination index - 1 as target
        let targetIndex = destination.index;
        if (source.index < destination.index) {
          targetIndex = Math.min(targetIndex + 1, targetArray.length - 1);
        }

        // If there's a target item, update position relative to it
        if (targetArray[targetIndex]) {
          await updateMenuPosition({
            menu_id: draggedItemId,
            target_id: targetArray[targetIndex].id,
          });
        }
      }
      // Moving to a different parent
      else {
        const newParentId =
          destinationDroppableId === 'root'
            ? 0 // Root level
            : parseInt(destinationDroppableId.split('-')[1], 10);

        if (newParentId === 0) {
          // Moving to root
          await moveMenuItemRoot({
            menu_id: draggedItemId,
            position: destination.index,
          });
        } else {
          // Moving to another parent
          await moveMenuItem({
            menu_id: draggedItemId,
            new_parent_id: newParentId,
            position: destination.index,
          });
        }
      }

      message.success('Cập nhật vị trí thành công');
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error(error);
      message.error('Có lỗi xảy ra khi cập nhật vị trí');
    } finally {
      setLoading(false);
    }
  };

  const renderMenuItem = (item: MenuItem) => (
    <div className="flex items-center justify-between w-full py-2 px-3 border-b border-gray-100">
      <div className="flex items-center">
        {item.image && (
          <img
            src={item.image}
            alt={item.name}
            className="w-12 h-12 object-cover rounded mr-3"
          />
        )}
        <div>
          <div className="font-bold">{item.name}</div>
          <div className="text-sm text-gray-500">
            {item.description}
            {getStatusLabel(item.status)}
          </div>
          {(item.price || item.price === 0) && (
            <div className="text-sm">
              <Text type="danger" strong>
                {formatPrice(item.price)}
              </Text>
              {item.sale_price && item.sale_price < item.price && (
                <Text delete className="ml-2">
                  {formatPrice(item.sale_price)}
                </Text>
              )}
            </div>
          )}
        </div>
      </div>

      <Space size="small">
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => handleEdit(item.id.toString())}
        />
        <Button
          type="text"
          icon={<PlusOutlined />}
          onClick={() => handleAdd(item.id)}
        />
        <Popconfirm
          title="Bạn có chắc chắn muốn xóa mục này?"
          onConfirm={() => handleDelete(item.id.toString())}
          okText="Xóa"
          cancelText="Hủy"
        >
          <Button type="text" danger icon={<DeleteOutlined />} />
        </Popconfirm>
      </Space>
    </div>
  );

  const renderMenuTree = (data: MenuItem[], level = 0) => {
    return (
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="root" type="menu-item">
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className="menu-tree-container"
            >
              {data
                .filter((item) => !item.parent_id)
                .map((item, index) => (
                  <Draggable
                    key={`item-${item.id}`}
                    draggableId={`item-${item.id}`}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={classNames('menu-item', {
                          'menu-item-dragging': snapshot.isDragging,
                        })}
                      >
                        <div className="menu-item-content">
                          <div
                            className="drag-handle"
                            {...provided.dragHandleProps}
                          >
                            <DragOutlined />
                          </div>
                          {renderMenuItem(item)}
                        </div>

                        {item.children && item.children.length > 0 && (
                          <Droppable
                            droppableId={`parent-${item.id}`}
                            type="menu-item"
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                                className="menu-children pl-8"
                              >
                                {item.children.map((child, childIndex) => (
                                  <Draggable
                                    key={`item-${child.id}`}
                                    draggableId={`item-${child.id}`}
                                    index={childIndex}
                                  >
                                    {(provided, snapshot) => (
                                      <div
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        className={classNames('menu-item', {
                                          'menu-item-dragging':
                                            snapshot.isDragging,
                                        })}
                                      >
                                        <div className="menu-item-content">
                                          <div
                                            className="drag-handle"
                                            {...provided.dragHandleProps}
                                          >
                                            <DragOutlined />
                                          </div>
                                          {renderMenuItem(child)}
                                        </div>
                                      </div>
                                    )}
                                  </Draggable>
                                ))}
                                {provided.placeholder}
                              </div>
                            )}
                          </Droppable>
                        )}
                      </div>
                    )}
                  </Draggable>
                ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    );
  };

  return (
    <div className="menu-tree">
      {data && data.length > 0 ? (
        renderMenuTree(data)
      ) : (
        <div className="p-5 text-center text-gray-500">
          Không có dữ liệu thực đơn. Hãy tạo mục thực đơn mới!
        </div>
      )}
    </div>
  );
}
