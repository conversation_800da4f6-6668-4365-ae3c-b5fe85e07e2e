import { UploadOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Space,
  Switch,
  Upload,
} from 'antd';
import QueryString from 'query-string';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { ButtonLink } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import { apiService } from '../../../services/api.service';
import { createItem, getOptions, updateItem } from '../api';
import { MODULE } from '../config';
import useMenuStore from '../store';
import { MenuItem, MenuItemCreate, MenuItemUpdate, STATUS } from '../type';

interface MenuFormProps {
  id?: string;
  initialValues?: MenuItem;
  onSuccess?: () => void;
}

export function MenuForm({ id, initialValues, onSuccess }: MenuFormProps) {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();
  const navigate = useNavigateTenant();
  const location = useLocation();
  const { loading, setLoading } = useMenuStore();
  const [parentOptions, setParentOptions] = useState<
    { value: number; label: string }[]
  >([]);
  const [uploadImage, setUploadImage] = useState<string>('');
  const query = QueryString.parse(location.search);
  const isUpdate = !!id;

  useEffect(() => {
    fetchParentOptions();
  }, []);

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
        parent_id: initialValues.parent_id || null,
      });

      if (initialValues.image) {
        setUploadImage(initialValues.image);
      }
    } else if (query.parentId) {
      form.setFieldsValue({
        parent_id: Number(query.parentId),
      });
    }
  }, [form, initialValues, query.parentId]);

  const fetchParentOptions = async () => {
    try {
      const res = await getOptions();
      if (res.status.success) {
        setParentOptions(
          res.data.map((item) => ({
            value: item.value,
            label: item.label,
          })),
        );
      }
    } catch (error) {
      console.error('Error fetching parent options:', error);
    }
  };

  const handleFinish = async (values: any) => {
    const payload = { ...values };

    // Add featured image from upload if available
    if (uploadImage) {
      payload.image = uploadImage;
    }

    setLoading(true);
    try {
      let response;
      if (isUpdate) {
        response = await updateItem(id!, payload as MenuItemUpdate);
      } else {
        response = await createItem(payload as MenuItemCreate);
      }

      if (response.status.success) {
        message.success(
          isUpdate ? 'Cập nhật thành công' : 'Tạo mới thành công',
        );
        if (onSuccess) {
          onSuccess();
        } else {
          navigate(`/${MODULE}`);
        }
      } else {
        message.error(response.status.message || 'Có lỗi xảy ra');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      message.error('Có lỗi xảy ra khi lưu thông tin');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadChange = async (info: any) => {
    if (info.file.status === 'done') {
      if (info.file.response.status.success) {
        const imageUrl = info.file.response.data.url;
        setUploadImage(imageUrl);
        message.success('Tải ảnh thành công');
      } else {
        message.error(info.file.response.status.message || 'Tải ảnh thất bại');
      }
    }
  };

  const uploadProps = {
    name: 'file',
    action: `${apiService.defaults.baseURL}/api/admin/v1/media/upload`,
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    onChange: handleUploadChange,
    showUploadList: false,
  };

  return (
    <div className="p-4">
      <Card
        title={isUpdate ? 'Chỉnh sửa mục thực đơn' : 'Thêm mục thực đơn mới'}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          initialValues={{
            is_active: true,
            is_featured: false,
            is_available: true,
            status: 'active',
            price: 0,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Tên mục thực đơn"
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập tên mục thực đơn',
                  },
                ]}
              >
                <Input placeholder="Nhập tên mục thực đơn" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="parent_id" label="Danh mục cha">
                <Select
                  placeholder="Chọn danh mục cha (nếu có)"
                  options={parentOptions}
                  allowClear
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="slug" label="Đường dẫn">
                <Input placeholder="Nhập đường dẫn (không bắt buộc)" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="Trạng thái">
                <Select
                  options={STATUS.map((status) => ({
                    value: status.value,
                    label: status.label,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="price" label="Giá">
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                  placeholder="Nhập giá"
                  addonAfter="VND"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="sale_price" label="Giá khuyến mãi">
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                  placeholder="Nhập giá khuyến mãi (nếu có)"
                  addonAfter="VND"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="estimated_cooking_time"
                label="Thời gian chế biến (phút)"
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Mô tả">
            <Input.TextArea rows={4} placeholder="Nhập mô tả chi tiết món ăn" />
          </Form.Item>

          <Form.Item label="Hình ảnh món ăn">
            <div className="flex items-center">
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />}>Tải ảnh lên</Button>
              </Upload>
              {uploadImage && (
                <div className="ml-4">
                  <img
                    src={uploadImage}
                    alt="Hình ảnh món ăn"
                    style={{
                      width: '100px',
                      height: '100px',
                      objectFit: 'cover',
                    }}
                  />
                </div>
              )}
            </div>
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="is_active"
                valuePropName="checked"
                label="Kích hoạt"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="is_featured"
                valuePropName="checked"
                label="Nổi bật"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="is_available"
                valuePropName="checked"
                label="Có sẵn để phục vụ"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {isUpdate ? 'Cập nhật' : 'Tạo mới'}
              </Button>
              <ButtonLink to={`${MODULE}`}>Huỷ</ButtonLink>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

export default MenuForm;
