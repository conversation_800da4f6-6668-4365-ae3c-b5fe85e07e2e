import { lazy } from 'react';
import { RouterItem } from '../../router/type';
import { MODULE } from './config';

const ListPage = lazy(() => import('./list'));
const FormPage = lazy(() => import('./form'));
const DetailPage = lazy(() => import('./detail'));

const routes: RouterItem[] = [
  {
    path: `/${MODULE}`,
    name: MODULE,
    permissions: [`${MODULE}.view`],
    icon: 'MenuOutlined',
    component: ListPage,
    hideChildrenInMenu: true,
  },
  {
    path: `/${MODULE}/create`,
    name: `${MODULE}.create`,
    permissions: [`${MODULE}.create`],
    icon: 'PlusOutlined',
    component: FormPage,
    hideMenu: true,
  },
  {
    path: `/${MODULE}/:id`,
    name: `${MODULE}.detail`,
    permissions: [`${MODULE}.view`],
    component: DetailPage,
    hideMenu: true,
  },
];

export default routes;
