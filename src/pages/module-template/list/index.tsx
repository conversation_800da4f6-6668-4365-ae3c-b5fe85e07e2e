import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  getPageNumber,
} from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useModuleTemplateStore from '../store';
import { ModuleTemplate } from '../type';
import Search from './search';

export function ModuleTemplateList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useModuleTemplateStore();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<ModuleTemplate[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  async function fetchData(payload?: any) {
    const params = {
      ...pagination,
      ...query,
      ...filters,
      ...payload,
    };
    //changeBrowserLocation(navigate, pathname, params);
    const response = await getItems(params);
    if (response.status.success) {
      setItems(response.data);
      setTotal(response.meta.total);
    } else {
      message.error(response.status.message);
    }
  }

  const onPagingChange = (page: number, limit: number) => {
    logger('[page]', { page, limit });
    setPagination({ page, limit });
    fetchData({ page, limit });
  };

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    // //changeBrowserLocation(navigate, pathname, values);
    setFilters(values);
    fetchData(values);
  };

  useEffectOnce(() => {
    fetchData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleTableChange = (page: number, pageSize: number) => {
    onPagingChange(page, pageSize);
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    {
      title: t('id'),
      dataIndex: 'id',
      key: 'id',
      render: (dom: any, record: any) => (
        <span
          className="text-blue-600 cursor-pointer"
          onClick={() => navigate(`/${MODULE}/${record.id}`)}
        >
          {dom}
        </span>
      ),
    },

    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('code'),
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: t('active'),
      dataIndex: 'active',
      key: 'active',
      render: (dom: any, record: any) => (
        <span>{dom ? t('statusActive') : t('statusDeActive')}</span>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{t('module')}</div>
        <div className="gap-4">
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <Pagination
              defaultCurrent={getPageNumber(query, 'page', 1)}
              total={total}
              defaultPageSize={pagination.limit}
              showSizeChanger
              showTitle={false}
              onChange={handleTableChange}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
      </Space>
    </div>
  );
}
