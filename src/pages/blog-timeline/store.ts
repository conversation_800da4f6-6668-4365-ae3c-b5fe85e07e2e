import { create } from 'zustand';
import { BlogTimeline } from './type';

interface BlogTimelineStore {
  loading: boolean;
  items: BlogTimeline[];
  item: BlogTimeline | null;
  total: number;
  page: number;
  limit: number;
  setLoading: (loading: boolean) => void;
  setItems: (items: BlogTimeline[]) => void;
  setItem: (item: BlogTimeline | null) => void;
  setTotal: (total: number) => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  addItem: (item: BlogTimeline) => void;
  updateItem: (item: BlogTimeline) => void;
  removeItem: (id: string) => void;
  reset: () => void;
}

const useBlogTimelineStore = create<BlogTimelineStore>((set) => ({
  loading: false,
  items: [],
  item: null,
  total: 0,
  page: 1,
  limit: 10,
  setLoading: (loading) => set({ loading }),
  setItems: (items) => set({ items }),
  setItem: (item) => set({ item }),
  setTotal: (total) => set({ total }),
  setPage: (page) => set({ page }),
  setLimit: (limit) => set({ limit }),
  addItem: (item) => set((state) => ({ items: [...state.items, item] })),
  updateItem: (item) =>
    set((state) => ({
      items: state.items.map((i) => (i.id === item.id ? item : i)),
      item: state.item?.id === item.id ? item : state.item,
    })),
  removeItem: (id) =>
    set((state) => ({
      items: state.items.filter((i) => i.id !== id),
      item: state.item?.id === id ? null : state.item,
    })),
  reset: () =>
    set({
      loading: false,
      items: [],
      item: null,
      total: 0,
      page: 1,
      limit: 10,
    }),
}));

export default useBlogTimelineStore;
