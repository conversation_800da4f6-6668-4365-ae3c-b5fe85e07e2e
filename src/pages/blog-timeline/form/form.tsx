import { DeleteOutlined, DragOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
  Switch,
  Table,
} from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from 'react-beautiful-dnd';
import { useTranslation } from 'react-i18next';
import { BackButton } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { BlogSelectionModal } from '../../blog/components';
import { Blog } from '../../blog/type';
import {
  addPostToTimeline,
  createItem,
  getItem,
  getTimelinePosts,
  removePostFromTimeline,
  updateItem,
} from '../api';
import { MODULE } from '../config';
import useBlogTimelineStore from '../store';
import { BlogTimeline, BlogTimelinePost } from '../type';

const FormItem = Form.Item;
const { Option } = Select;

interface IndexFormProps {
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();
  const { loading } = useBlogTimelineStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<BlogTimeline>();
  const [timelinePosts, setTimelinePosts] = useState<BlogTimelinePost[]>([]);
  const [loadingPosts, setLoadingPosts] = useState<boolean>(false);
  const [blogSelectionModalVisible, setBlogSelectionModalVisible] =
    useState<boolean>(false);

  const initForm = {
    active: true,
  };

  const getItemData = useCallback(
    async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
        // Load timeline posts
        loadTimelinePosts(_id);
      } else {
        message.error(res.status.message);
      }
    },
    [form],
  );

  const loadTimelinePosts = async (timelineId: string) => {
    try {
      setLoadingPosts(true);
      const res = await getTimelinePosts(timelineId, {
        page: 1,
        limit: 100,
        sort_by: 'priority',
        sort_order: 'asc',
      });
      if (res.status.success) {
        setTimelinePosts(res.data || []);
      }
    } catch (error) {
      console.error('Error loading timeline posts:', error);
    } finally {
      setLoadingPosts(false);
    }
  };

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id, getItemData]);

  const onFinish = async (values: BlogTimeline) => {
    try {
      let res;
      if (isNew) {
        res = await createItem(values);
        if (res.status.success) {
          message.success(t('addSuccess'));
          setItem(res.data);
          setIsNew(false);
        }
      } else {
        res = await updateItem(id!, values);
        if (res.status.success) {
          message.success(t('updateSuccess'));
          setItem(res.data);
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        navigate('/blog-timeline');
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') ?? t('submitError'),
      );
    }
  };

  const handleAddPosts = async (selectedBlogs: Blog[]) => {
    if (!item || selectedBlogs.length === 0) return;

    try {
      // Get current max priority
      const maxPriority =
        timelinePosts.length > 0
          ? Math.max(...timelinePosts.map((tp) => tp.priority || 0))
          : 0;

      // Add each selected blog to timeline
      const promises = selectedBlogs.map((blog, index) =>
        addPostToTimeline(item.id, {
          post_id: blog.post_id,
          priority: (maxPriority || 0) + index + 1,
        }),
      );

      const results = await Promise.all(promises);
      const successCount = results.filter((res) => res.status.success).length;
      const failCount = results.length - successCount;

      if (successCount > 0) {
        message.success(`Đã thêm ${successCount} bài viết vào timeline`);
        loadTimelinePosts(item.id);
      }

      if (failCount > 0) {
        message.warning(`${failCount} bài viết không thể thêm vào timeline`);
      }
    } catch (error) {
      console.error('Error adding posts:', error);
      message.error('Lỗi khi thêm bài viết vào timeline');
    }
  };

  const handleRemovePost = async (postId: string) => {
    if (!item) return;

    try {
      const res = await removePostFromTimeline(item.id, postId);
      if (res.status.success) {
        message.success(t('postRemovedSuccess'));
        loadTimelinePosts(item.id);
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      console.error('Error removing post:', error);
      message.error(t('postRemoveError'));
    }
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(timelinePosts);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update priorities
    const updatedItems = items.map((item, index) => ({
      ...item,
      priority: index + 1,
    }));

    setTimelinePosts(updatedItems);
    // TODO: Call API to update order
  };

  const postsColumns = [
    {
      title: '',
      dataIndex: 'drag',
      width: 30,
      render: () => <DragOutlined style={{ cursor: 'move', color: '#999' }} />,
    },
    {
      title: t('priority'),
      dataIndex: 'priority',
      width: 80,
      render: (priority: number) => (
        <span className="font-semibold text-blue-600">{priority}</span>
      ),
    },
    {
      title: t('postTitle'),
      dataIndex: 'post',
      render: (post: any) => (
        <div>
          <div className="font-medium">{post?.title || 'N/A'}</div>
          <div className="text-sm text-gray-500">{post?.slug}</div>
        </div>
      ),
    },
    {
      title: t('author'),
      dataIndex: 'post',
      width: 150,
      render: (post: any) => post?.author?.name || 'N/A',
    },
    {
      title: t('publishedAt'),
      dataIndex: 'post',
      width: 120,
      render: (post: any) => {
        if (!post?.published_at) return 'N/A';
        return new Date(post.published_at).toLocaleDateString('vi-VN');
      },
    },
    {
      title: t('actions'),
      width: 80,
      render: (_: any, record: BlogTimelinePost) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemovePost(record.id)}
          size="small"
        />
      ),
    },
  ];

  const handleValuesChange = (newValue: any, allValues: any) => {
    logger(newValue);
    logger(allValues);
  };

  const isEdit = !!id;

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div className="flex items-center gap-2 mb-6">
            <BackButton destination="back" />
            <h1 className="text-2xl font-bold m-0">
              {isEdit ? t('form.editTitle') : t('form.createTitle')}
            </h1>
          </div>
        </Col>
      </Row>
      <Form
        style={{ marginTop: 8 }}
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem
            label={t('form.name.label')}
            name="name"
            rules={[
              {
                required: true,
                message: t('form.name.required'),
              },
            ]}
          >
            <Input placeholder={t('form.name.placeholder')} />
          </FormItem>

          <FormItem
            label={t('form.code.label')}
            name="code"
            rules={[
              {
                required: true,
                message: t('form.code.required'),
              },
              {
                pattern: /^[a-zA-Z0-9-]+$/,
                message: t('form.code.pattern'),
              },
            ]}
          >
            <Input placeholder={t('form.code.placeholder')} />
          </FormItem>
        </div>

        <FormItem
          label={t('form.active.label')}
          name="active"
          valuePropName="checked"
          help={t('form.active.help')}
        >
          <Switch />
        </FormItem>

        <FormItem>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {t('save')}
            </Button>
            <Button onClick={() => navigate('/blog-timeline')}>
              {t('cancel')}
            </Button>
          </Space>
        </FormItem>
      </Form>

      {/* Posts Management Section */}
      {!isNew && item && (
        <Card
          title={t('managePosts')}
          extra={
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setBlogSelectionModalVisible(true)}
            >
              {t('addPost')}
            </Button>
          }
        >
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="timeline-posts">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  <Table
                    columns={postsColumns}
                    dataSource={timelinePosts}
                    rowKey="id"
                    loading={loadingPosts}
                    pagination={false}
                    size="small"
                    locale={{ emptyText: t('noPosts') }}
                    components={{
                      body: {
                        row: ({ children, ...props }: any) => {
                          const index = timelinePosts.findIndex(
                            (item) => item.id === props['data-row-key'],
                          );
                          return (
                            <Draggable
                              key={props['data-row-key']}
                              draggableId={props['data-row-key']}
                              index={index}
                            >
                              {(provided, snapshot) => (
                                <tr
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  {...props}
                                  style={{
                                    ...provided.draggableProps.style,
                                    ...(snapshot.isDragging
                                      ? {
                                          background: '#f0f8ff',
                                          boxShadow:
                                            '0 2px 8px rgba(0,0,0,0.15)',
                                        }
                                      : {}),
                                  }}
                                >
                                  {children}
                                </tr>
                              )}
                            </Draggable>
                          );
                        },
                      },
                    }}
                  />
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </Card>
      )}

      {/* Blog Selection Modal */}
      <BlogSelectionModal
        visible={blogSelectionModalVisible}
        onCancel={() => setBlogSelectionModalVisible(false)}
        onConfirm={handleAddPosts}
        excludeIds={timelinePosts.map((tp) => tp.post_id?.toString() || '')}
        title="Chọn bài viết cho timeline"
        confirmText="Thêm vào timeline"
        showContinueButton={true}
      />
    </div>
  );
};

export default IndexForm;
