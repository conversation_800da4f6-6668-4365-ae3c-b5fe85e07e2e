import { Modal } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';
import BlogTimelineForm from './form';

interface ModalFormProps {
  id?: string;
  onClose: () => void;
}

const ModalForm: React.FC<ModalFormProps> = ({ id, onClose }) => {
  const { t } = useTranslation(MODULE);
  const isEdit = !!id;

  return (
    <Modal
      title={isEdit ? t('form.editTitle') : t('form.createTitle')}
      open={true}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <BlogTimelineForm id={id} onChange={() => onClose()} />
    </Modal>
  );
};

export default ModalForm;
