import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Switch,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { BackButton } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { getPageNumber } from '../../../services/utils.service';
import { deleteItem, getItems, updateItem } from '../api';
import { MODULE } from '../config';

import useBlogTimelineStore from '../store';
import { BlogTimeline } from '../type';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useBlogTimelineStore();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [items, setItems] = useState<BlogTimeline[]>([]);

  async function fetchData(payload?: any) {
    const params = {
      ...pagination,
      ...query,
      ...filters,
      ...payload,
    };
    const response = await getItems(params);
    if (response.status.success) {
      setItems(response.data);
      setTotal(response.meta.total);
    } else {
      message.error(response.status.message);
    }
  }

  const onPagingChange = (_page: number, pageSize?: number) => {
    logger('[page]', { _page, pageSize });
    setPagination({ page: _page, limit: pageSize });
    fetchData({ page: _page, limit: pageSize });
  };

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData({ ...values, page: 1 });
  };

  const handleEdit = (id: string) => {
    navigate(`/blog-timeline/${id}`);
  };

  const handleCreate = () => {
    navigate('/blog-timeline/create');
  };

  const handleDelete = async (id: string) => {
    const response = await deleteItem(id);
    if (response.status.success) {
      message.success(t('list.deleteSuccess'));
      fetchData();
    } else {
      message.error(response.status.message);
    }
  };

  const handleToggleActive = async (id: string, active: boolean) => {
    const response = await updateItem(id, { active });
    if (response.status.success) {
      message.success(t('list.updateSuccess'));
      fetchData();
    } else {
      message.error(response.status.message);
    }
  };

  const columns = [
    {
      title: t('list.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('list.code'),
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: t('list.postsCount'),
      dataIndex: 'posts_count',
      key: 'posts_count',
      render: (count: number) => count || 0,
    },
    {
      title: t('list.active'),
      dataIndex: 'active',
      key: 'active',
      render: (active: boolean, record: BlogTimeline) => (
        <Switch
          checked={active}
          onChange={(checked) => handleToggleActive(record.id, checked)}
        />
      ),
    },
    {
      title: t('list.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: t('list.updatedAt'),
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: t('list.actions'),
      key: 'actions',
      render: (_: any, record: BlogTimeline) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
          >
            {t('list.edit')}
          </Button>
          <Popconfirm
            title={t('list.deleteConfirm')}
            onConfirm={() => handleDelete(record.id)}
            okText={t('list.yes')}
            cancelText={t('list.no')}
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              {t('list.delete')}
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffectOnce(() => {
    fetchData();
  });

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <BackButton destination="back" />
              <h1 className="text-2xl font-bold m-0">{t('list.title')}</h1>
            </div>
            <Button
              type="primary"
              icon={<PlusCircleOutlined />}
              onClick={handleCreate}
            >
              {t('list.create')}
            </Button>
          </div>
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={items}
        rowKey="id"
        loading={loading}
        pagination={false}
      />

      <div className="mt-4 flex justify-end">
        <Pagination
          current={getPageNumber(pagination.page)}
          total={total}
          pageSize={pagination.limit}
          onChange={onPagingChange}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) =>
            `${range[0]}-${range[1]} of ${total} items`
          }
        />
      </div>
    </div>
  );
}
