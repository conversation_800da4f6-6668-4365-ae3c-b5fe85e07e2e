{"title": "Blog Timeline", "list": {"title": "Blog Timeline", "name": "Timeline Name", "code": "Timeline Code", "postsCount": "Posts Count", "active": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "edit": "Edit", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete this timeline?", "deleteSuccess": "Timeline deleted successfully", "updateSuccess": "Timeline updated successfully", "create": "Create Timeline", "yes": "Yes", "no": "No"}, "create": "Create Timeline", "edit": "Edit Timeline", "detail": "Timeline Detail", "delete": "Delete Timeline", "search": "Search timelines...", "name": "Timeline Name", "code": "Timeline Code", "active": "Active", "inactive": "Inactive", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "addSuccess": "Timeline added successfully", "updateSuccess": "Timeline updated successfully", "deleteSuccess": "Timeline deleted successfully", "submitError": "An error occurred while submitting the form", "deleteConfirm": "Are you sure you want to delete this timeline?", "noData": "No data available", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "back": "Back", "refresh": "Refresh", "posts": "Posts", "managePosts": "Manage Posts", "addPost": "Add Post", "removePost": "Remove Post", "priority": "Priority", "postTitle": "Post Title", "postStatus": "Post Status", "publishedAt": "Published At", "author": "Author", "postAssignedSuccess": "Post added to timeline successfully", "postAssignedError": "Error adding post to timeline", "postRemovedSuccess": "Post removed from timeline successfully", "postRemoveError": "Error removing post from timeline", "postsReorderedSuccess": "Posts reordered successfully", "postsReorderError": "Error reordering posts", "searchPosts": "Search posts...", "availablePosts": "Available Posts", "timelinePosts": "Timeline Posts", "noTimelines": "No timelines available", "noPosts": "No posts available", "dragToAdd": "Drag and drop to add posts to timeline", "reorderPosts": "Drag and drop to reorder posts", "selectTimeline": "Select Timeline", "postsCount": "Posts Count", "yes": "Yes", "no": "No", "form": {"editTitle": "Edit Timeline", "createTitle": "Create Timeline", "name": {"label": "Timeline Name", "placeholder": "Enter timeline name", "required": "Please enter timeline name"}, "code": {"label": "Timeline Code", "placeholder": "Enter timeline code (used for URL)", "required": "Please enter timeline code", "pattern": "Code can only contain letters, numbers and hyphens"}, "active": {"label": "Activate Timeline", "help": "Timeline will be displayed on website when activated"}, "posts": {"label": "Posts", "addPost": "Add Post", "selectPost": "Select Post", "priority": "Priority", "remove": "Remove"}}}