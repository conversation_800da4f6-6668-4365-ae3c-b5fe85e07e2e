export interface BlogTimeline {
  _id: string;
  id: string;
  name: string;
  code: string;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogTimelinePost {
  id: string;
  blog_timeline_id: string;
  post_id: number;
  priority: number;
  created_at: string;
  updated_at: string;
  post?: {
    id: number;
    title: string;
    slug: string;
    excerpt?: string;
    content?: string;
    status: string;
    published_at?: string;
    author?: {
      id: number;
      name: string;
      email: string;
    };
  };
}

export interface PostOrder {
  post_id: number;
  priority: number;
}

export interface BatchPostResult {
  created_count: number;
  failed_count: number;
  created_posts: BlogTimelinePost[];
  failed_posts: Array<{
    post_id: number;
    error: string;
  }>;
}

export interface ReorderResult {
  updated_count: number;
  failed_count: number;
  updated_posts: BlogTimelinePost[];
  failed_posts: Array<{
    post_id: number;
    error: string;
  }>;
}
