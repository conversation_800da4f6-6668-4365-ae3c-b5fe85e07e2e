import React from 'react';
import { Select, SelectProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { useBlogTimelineStore } from '../store';
import { getOptions } from '../api';

interface SelectBlogTimelineProps extends Omit<SelectProps, 'options'> {
  placeholder?: string;
}

export const SelectBlogTimeline: React.FC<SelectBlogTimelineProps> = ({
  placeholder,
  ...props
}) => {
  const { t } = useTranslation('blog-timeline');
  const [options, setOptions] = React.useState<
    { label: string; value: string }[]
  >([]);
  const [loading, setLoading] = React.useState(false);

  const loadOptions = async () => {
    try {
      setLoading(true);
      const response = await getOptions();
      const formattedOptions = response.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
      setOptions(formattedOptions);
    } catch (error) {
      console.error('Error loading blog timeline options:', error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    loadOptions();
  }, []);

  return (
    <Select
      {...props}
      placeholder={placeholder || t('selectTimeline')}
      options={options}
      loading={loading}
      showSearch
      filterOption={(input, option) =>
        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
      }
    />
  );
};
