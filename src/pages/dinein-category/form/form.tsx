import { But<PERSON>, Col, Collapse, Form, Input, message, Row, Switch } from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { InputSlug } from '../../../components/input';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { SelectDineinCategoryTree } from '../components';
import { MODULE } from '../config';
import useDineinCategoryStore from '../store';
import {
  DineinCategory,
  DineinCategoryCreate,
  DineinCategoryUpdate,
} from '../type';

const FormItem = Form.Item;
const { TextArea } = Input;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  logger('id', id);
  const { loading } = useDineinCategoryStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<DineinCategory>();
  const [formValues, setFormValues] = useState<any>();
  const initForm = {
    is_active: true,
    is_featured: false,
  };

  const getItemData = useCallback(
    async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    },
    [form],
  );

  useEffect(() => {
    logger('id', id);
    if (id === 'create' || id === undefined) {
      setIsNew(true);
      form.resetFields();
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id, form, getItemData, logger]);

  const onFinish = async (
    values: DineinCategoryCreate | DineinCategoryUpdate,
  ) => {
    try {
      let res;
      const parentId = values.parent_id;
      if (
        parentId === undefined ||
        parentId === 0 ||
        parentId === null ||
        (typeof parentId === 'string' && parentId === '')
      ) {
        values.parent_id = null;
      } else {
        values.parent_id = Number(parentId);
      }
      if (isNew) {
        res = await createItem(values as DineinCategoryCreate);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        res = await updateItem(id!, values as DineinCategoryUpdate);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    }
  };

  const handleValuesChange = (newValue: any, allValues: any) => {
    logger(newValue);
    logger(allValues);
    setFormValues(allValues);
  };

  return (
    <Form
      style={{ marginTop: 8 }}
      form={form}
      name="form"
      layout="vertical"
      onFinish={onFinish}
      autoComplete="off"
      initialValues={initForm}
      onValuesChange={handleValuesChange}
    >
      <Row gutter={16}>
        <Col xs={24} lg={18}>
          <div className="form_content">
            <Row gutter={16}>
              <Col xs={24} lg={24}>
                <FormItem
                  label={t('parent')}
                  name="parent_id"
                  rules={[{ required: false, message: t('pleaseEnterData') }]}
                >
                  <SelectDineinCategoryTree isParentAll={true} />
                </FormItem>
              </Col>

              <Col xs={24} lg={24}>
                <FormItem
                  label={t('name')}
                  name="name"
                  rules={[{ required: true, message: t('pleaseEnterData') }]}
                >
                  <Input />
                </FormItem>
              </Col>

              <Col xs={24} lg={24}>
                <FormItem
                  label={t('slug')}
                  name="slug"
                  rules={[{ required: true, message: t('pleaseEnterData') }]}
                >
                  <InputSlug
                    form={form}
                    name="slug"
                    initialValue={formValues?.name}
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={24}>
                <FormItem label={t('description')} name="description">
                  <TextArea rows={4} />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('featuredImage')} name="image">
                  <Input placeholder="URL" />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('position')} name="position">
                  <Input type="number" />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('isActive')}
                  name="is_active"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('isFeatured')}
                  name="is_featured"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>
            </Row>
          </div>
        </Col>
        <Col xs={24} lg={6}>
          <Collapse defaultActiveKey={['source']} style={{ width: '100%' }}>
            <Collapse.Panel key="source" header="SEO">
              <FormItem label={t('metaTitle')} name="meta_title">
                <Input />
              </FormItem>
              <FormItem label={t('metaDescription')} name="meta_description">
                <TextArea rows={4} />
              </FormItem>
            </Collapse.Panel>
          </Collapse>
        </Col>
      </Row>
      <div className="form_footer">
        <FormItem>
          <Button type="primary" htmlType="submit" loading={loading}>
            {isNew ? t('btnAdd') : t('btnUpdate')}
          </Button>
        </FormItem>
      </div>
    </Form>
  );
};

export default IndexForm;
