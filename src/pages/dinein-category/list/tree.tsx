import { PlusCircleOutlined } from '@ant-design/icons';
import { UniqueIdentifier } from '@dnd-kit/core';
import { Button, message } from 'antd';
import queryString from 'query-string';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { SortableTree } from '../../../components/tree';
import { TreeItems } from '../../../components/tree/types';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  deleteItem,
  getTree,
  moveCategoryNode,
  moveCategoryNodeRoot,
  MoveNodeRequest,
  updateCategoryPosition,
  UpdatePositionRequest,
} from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import useDineinCategoryStore from '../store';
import { DineinCategory } from '../type';
import Search from './search';

// Hàm chuyển đổi DineinCategory[] sang TreeItems
const convertToTreeItems = (categories: DineinCategory[]): TreeItems => {
  const convertCategory = (category: DineinCategory): any => {
    return {
      id: category.id,
      name: category.name,
      parentId: category.parent_id,
      children: category.children ? category.children.map(convertCategory) : [],
      position: category.position,
      depth: category.depth,
    };
  };

  return categories.map(convertCategory);
};

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useDineinCategoryStore();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showSortModal, setShowSortModal] = useState(false);
  const [categories, setCategories] = useState<DineinCategory[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  // Chuyển đổi categories sang định dạng TreeItems
  const treeItems = useMemo(() => {
    return convertToTreeItems(categories);
  }, [categories]);

  async function fetchData(payload?: any) {
    const params = {
      ...pagination,
      ...query,
      ...filters,
      ...payload,
    };
    //changeBrowserLocation(navigate, pathname, params);
    const response = await getTree(params);
    if (response.status.success) {
      setCategories(response.data || []);
      // Update total for pagination if needed
      // setTotal(response.meta.total);
    } else {
      message.error(response.status.message);
    }
  }

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData(values);
  };

  const getTreeData = async () => {
    const response = await getTree();
    if (response.status.success) {
      setCategories(response.data || []);
    }
  };

  useEffectOnce(() => {
    getTreeData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    } else if (action === 'sort') {
      setShowSortModal(true);
    }
  };

  const handleMoveNodeRoot = async (
    category_id: UniqueIdentifier,
    position: number,
  ) => {
    const payload = {
      category_id: Number(category_id),
      position: position,
    };
    const response = await moveCategoryNodeRoot(payload);
    if (response.status.success) {
      message.success(t('moveSuccess') || 'Di chuyển danh mục thành công');
    } else {
      message.error(response.status.message);
    }
    getTreeData();
  };

  const handleMoveNode = async (
    activeId: UniqueIdentifier,
    overId: UniqueIdentifier,
    position: string,
  ) => {
    logger('[handleMoveNode]', { activeId, overId, position });
    // Trường hợp 1: Di chuyển vào làm con của một danh mục khác
    const payload: MoveNodeRequest = {
      category_id: Number(activeId),
      new_parent_id: Number(overId),
      position: position, // Vị trí trong danh mục cha
    };

    logger('[moveCategoryNode]', payload);
    const response = await moveCategoryNode(payload);

    if (response.status.success) {
      message.success(t('moveSuccess') || 'Di chuyển danh mục thành công');
    } else {
      message.error(response.status.message);
    }
    getTreeData();
  };

  const handleUpdatePosition = async (categoryId: number, targetId: number) => {
    logger('[handleUpdatePosition]', { categoryId, targetId });
    // API mới chỉ cần chỉ định node nguồn và node đích
    const payload: UpdatePositionRequest = {
      category_id: categoryId,
      target_id: targetId,
    };

    logger('[updateCategoryPosition]', payload);
    const response = await updateCategoryPosition(payload);

    if (response.status.success) {
      message.success(t('moveSuccess') || 'Di chuyển danh mục thành công');
    } else {
      message.error(response.status.message);
    }
    getTreeData();
  };
  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{t('module')}</div>
        <div className="gap-4 flex">
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      {treeItems.length > 0 && (
        <SortableTree
          collapsible
          indicator
          removable
          items={treeItems}
          onMoveNode={handleMoveNode}
          onMoveNodeRoot={handleMoveNodeRoot}
          onUpdatePosition={handleUpdatePosition}
        />
      )}
    </div>
  );
}
