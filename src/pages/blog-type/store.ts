import { create } from 'zustand';
import { deleteItem, getOptions } from './api';
import { BlogType } from './type';

export interface StoreState {
  loading: boolean;
  items: BlogType[];
  options: any[];
  setLoading: (loading: boolean) => void;
  delete: (id: string) => void;
  setOptions: (options: any) => void;
  deleteItem: (id: string) => Promise<void>;
  getOptions: () => Promise<void>;
}

const useBlogTypeStore = create<StoreState>((set) => ({
  items: [],
  item: undefined,
  options: [],
  loading: false,
  setLoading: (loading: boolean) => {
    set({ loading });
  },
  delete: (id: string) => {
    set((state) => {
      const itemsNew = state.items.filter((b) => b.id !== id);
      return { items: itemsNew };
    });
  },
  setOptions: (options: any) => {
    set({ options });
  },
  deleteItem: async (id: string) => {
    set({ loading: true });
    await deleteItem(id);
    set((state) => {
      const itemsNew = state.items.filter((b) => b.id !== id);
      return { items: itemsNew, loading: false };
    });
  },
  getOptions: async () => {
    const response = await getOptions();
    set({ options: response.data });
  },
}));

export default useBlogTypeStore;
