import { CheckOutlined, ReloadOutlined, SaveOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { CloudDashboardForm } from './form';

const CloudDashboardPage: React.FC = () => {
  const { id } = useParams();
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const formRef = useRef<any>(null);
  const navigate = useNavigateTenant();

  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    navigate(`/${MODULE}/`);
  };

  function handleActions(action: string): void {
    logger('[handleActions]', action);
    if (action === 'save') {
      formRef.current?.submitForm();
    } else if (action === 'cancel') {
      navigate(`/${MODULE}/`);
    }
  }

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="dashed"
            icon={<ReloadOutlined />}
            onClick={() => handleActions('cancel')}
          >
            {t('btnCancel')}
          </Button>

          <Button
            type="dashed"
            icon={<CheckOutlined />}
            onClick={() => handleActions('saveAndContinue')}
          >
            {t('btnSaveAndContinue')}
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => handleActions('save')}
          >
            {t('btnSave')}
          </Button>
        </div>
      </div>
      <div className="p-4">
        <CloudDashboardForm
          ref={formRef}
          onChange={handleChangeForm}
          id={id}
        ></CloudDashboardForm>
      </div>
    </div>
  );
};

export { CloudDashboardPage };
