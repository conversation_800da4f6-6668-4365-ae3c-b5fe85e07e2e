// Blog AI Form Modal Component
import {
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
  EyeOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Form,
  Input,
  Select,
  Radio,
  Switch,
  Button,
  Space,
  Divider,
  Card,
  Tag,
  Rate,
  message,
  Tabs,
} from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BlogAI,
  BLOG_AI_STATUS,
  AI_MODEL_TYPE,
  AI_MODEL_LABELS,
  BlogAIDrawerData,
} from '../type';
import {
  TONE_OPTIONS,
  LANGUAGE_OPTIONS,
  WORD_COUNT_OPTIONS,
  QUALITY_THRESHOLD,
} from '../config';
import { createItem, updateItem, getItem, analyzeQuality } from '../api';
import { useBlogAIStore } from '../store';
import ConsoleService from '../../../services/console.service';

const { TextArea } = Input;
const { TabPane } = Tabs;

interface FormModalProps {
  visible: boolean;
  mode: 'create' | 'edit' | 'view';
  itemId?: string;
  onCancel: () => void;
  onSuccess: (item: BlogAI) => void;
}

export default function FormModal({
  visible,
  mode,
  itemId,
  onCancel,
  onSuccess,
}: FormModalProps) {
  const { t } = useTranslation('blog-ai');
  const [form] = Form.useForm();
  const logger = ConsoleService.register('blog-ai-form');

  const {
    form: formState,
    setFormData,
    setFormLoading,
    setFormSubmitting,
  } = useBlogAIStore();

  const [currentItem, setCurrentItem] = useState<BlogAI | null>(null);
  const [analyzing, setAnalyzing] = useState(false);
  const [qualityScore, setQualityScore] = useState<number>();
  const [activeTab, setActiveTab] = useState('basic');

  const isReadonly = mode === 'view';
  const isEdit = mode === 'edit';
  const isCreate = mode === 'create';

  // Load item data for edit/view mode
  useEffect(() => {
    const loadItem = async () => {
      if (!itemId || isCreate) return;

      try {
        setFormLoading(true);
        const response = await getItem(itemId);
        if (response.success && response.data) {
          const item = response.data;
          setCurrentItem(item);
          setQualityScore(item.quality_score);

          // Set form values
          form.setFieldsValue({
            title: item.title,
            content: item.content,
            prompt: item.prompt,
            ai_model: item.ai_model,
            keywords: item.keywords?.join(', '),
            target_audience: item.target_audience,
            tone: item.tone,
            word_count: item.word_count,
            language: item.language,
            status: item.status,
            category_id: item.category_id,
            tags: item.tags,
            meta_title: item.meta_title,
            meta_description: item.meta_description,
            is_featured: item.is_featured,
            ...item.generation_settings,
          });
        }
      } catch (error: any) {
        logger('Error loading item:', error);
        message.error(error.message || 'Không thể tải dữ liệu');
      } finally {
        setFormLoading(false);
      }
    };

    if (visible) {
      loadItem();
    }
  }, [visible, itemId, isCreate, form, setFormLoading, logger]);

  const handleSubmit = useCallback(
    async (values: any) => {
      try {
        setFormSubmitting(true);

        const data = {
          ...values,
          keywords: values.keywords
            ?.split(',')
            .map((k: string) => k.trim())
            .filter(Boolean),
          generation_settings: {
            temperature: values.temperature,
            max_tokens: values.max_tokens,
            top_p: values.top_p,
            frequency_penalty: values.frequency_penalty,
            presence_penalty: values.presence_penalty,
          },
        };

        let response;
        if (isCreate) {
          response = await createItem(data);
        } else {
          response = await updateItem(itemId!, data);
        }

        if (response.success && response.data) {
          message.success(
            isCreate ? 'Tạo mới thành công' : 'Cập nhật thành công',
          );
          onSuccess(response.data);
        } else {
          throw new Error(response.message || 'Lưu dữ liệu thất bại');
        }
      } catch (error: any) {
        logger('Error submitting form:', error);
        message.error(error.message || 'Lưu dữ liệu thất bại');
      } finally {
        setFormSubmitting(false);
      }
    },
    [isCreate, itemId, onSuccess, setFormSubmitting, logger],
  );

  const handleAnalyzeQuality = useCallback(async () => {
    const content = form.getFieldValue('content');
    if (!content) {
      message.warning('Vui lòng nhập nội dung để phân tích');
      return;
    }

    try {
      setAnalyzing(true);
      const response = await analyzeQuality({ content });
      if (response.success && response.data) {
        setQualityScore(response.data.score);
        message.success(`Điểm chất lượng: ${response.data.score}/10`);
      }
    } catch (error: any) {
      logger('Error analyzing quality:', error);
      message.error('Không thể phân tích chất lượng');
    } finally {
      setAnalyzing(false);
    }
  }, [form, logger]);

  const renderQualityIndicator = () => {
    if (qualityScore === undefined) return null;

    const getQualityColor = (score: number) => {
      if (score >= 8) return '#52c41a';
      if (score >= 6) return '#faad14';
      return '#ff4d4f';
    };

    const getQualityText = (score: number) => {
      if (score >= 8) return 'Chất lượng cao';
      if (score >= 6) return 'Chất lượng trung bình';
      return 'Cần cải thiện';
    };

    return (
      <div className="quality-indicator">
        <div className="quality-score">
          <Rate
            disabled
            allowHalf
            value={qualityScore / 2}
            style={{ color: getQualityColor(qualityScore) }}
          />
          <span className="score-text">
            {qualityScore}/10 - {getQualityText(qualityScore)}
          </span>
        </div>
      </div>
    );
  };

  const renderBasicTab = () => (
    <div className="form-section">
      <Form.Item
        name="title"
        label="Tiêu đề"
        rules={[{ required: true, message: 'Vui lòng nhập tiêu đề' }]}
      >
        <Input placeholder="Nhập tiêu đề bài viết" disabled={isReadonly} />
      </Form.Item>

      <Form.Item
        name="content"
        label={
          <Space>
            <span>Nội dung</span>
            {!isReadonly && (
              <Button
                size="small"
                icon={<RobotOutlined />}
                loading={analyzing}
                onClick={handleAnalyzeQuality}
              >
                Phân tích chất lượng
              </Button>
            )}
          </Space>
        }
        rules={[{ required: true, message: 'Vui lòng nhập nội dung' }]}
      >
        <TextArea
          rows={12}
          placeholder="Nhập nội dung bài viết"
          disabled={isReadonly}
        />
      </Form.Item>

      {renderQualityIndicator()}

      <Form.Item name="status" label="Trạng thái">
        <Select disabled={isReadonly}>
          <Select.Option value={BLOG_AI_STATUS.DRAFT}>Bản nháp</Select.Option>
          <Select.Option value={BLOG_AI_STATUS.GENERATED}>Đã tạo</Select.Option>
          <Select.Option value={BLOG_AI_STATUS.PUBLISHED}>
            Đã xuất bản
          </Select.Option>
        </Select>
      </Form.Item>

      <Form.Item name="is_featured" label="Nổi bật" valuePropName="checked">
        <Switch disabled={isReadonly} />
      </Form.Item>
    </div>
  );

  const renderAITab = () => (
    <div className="form-section">
      <Form.Item name="prompt" label="Prompt gốc">
        <TextArea
          rows={4}
          placeholder="Prompt đã sử dụng để tạo nội dung"
          disabled={isReadonly}
        />
      </Form.Item>

      <Form.Item name="ai_model" label="Mô hình AI">
        <Radio.Group disabled={isReadonly} buttonStyle="solid">
          {Object.entries(AI_MODEL_LABELS).map(([value, label]) => (
            <Radio.Button key={value} value={value}>
              {label}
            </Radio.Button>
          ))}
        </Radio.Group>
      </Form.Item>

      <Form.Item name="language" label="Ngôn ngữ">
        <Select options={LANGUAGE_OPTIONS} disabled={isReadonly} />
      </Form.Item>

      <Form.Item name="tone" label="Giọng điệu">
        <Select options={TONE_OPTIONS} disabled={isReadonly} />
      </Form.Item>

      <Form.Item name="word_count" label="Số từ">
        <Select options={WORD_COUNT_OPTIONS} disabled={isReadonly} />
      </Form.Item>

      <Form.Item name="keywords" label="Từ khóa">
        <Input
          placeholder="Từ khóa (phân cách bằng dấu phẩy)"
          disabled={isReadonly}
        />
      </Form.Item>

      <Form.Item name="target_audience" label="Đối tượng mục tiêu">
        <Input placeholder="Đối tượng mục tiêu" disabled={isReadonly} />
      </Form.Item>
    </div>
  );

  const renderSEOTab = () => (
    <div className="form-section">
      <Form.Item name="meta_title" label="Meta Title">
        <Input
          placeholder="Tiêu đề SEO"
          disabled={isReadonly}
          showCount
          maxLength={60}
        />
      </Form.Item>

      <Form.Item name="meta_description" label="Meta Description">
        <TextArea
          rows={3}
          placeholder="Mô tả SEO"
          disabled={isReadonly}
          showCount
          maxLength={160}
        />
      </Form.Item>

      <Form.Item name="category_id" label="Danh mục">
        <Select placeholder="Chọn danh mục" disabled={isReadonly} allowClear>
          {/* Categories will be loaded from API */}
        </Select>
      </Form.Item>

      <Form.Item name="tags" label="Tags">
        <Select
          mode="tags"
          placeholder="Nhập tags"
          disabled={isReadonly}
          tokenSeparators={[',']}
        />
      </Form.Item>
    </div>
  );

  const renderAdvancedTab = () => (
    <div className="form-section">
      <div className="section-title">Cài đặt tạo nội dung</div>

      <Form.Item name="temperature" label="Temperature">
        <Input type="number" min={0} max={1} step={0.1} disabled={isReadonly} />
      </Form.Item>

      <Form.Item name="max_tokens" label="Max Tokens">
        <Input type="number" min={100} max={4000} disabled={isReadonly} />
      </Form.Item>

      <Form.Item name="top_p" label="Top P">
        <Input
          type="number"
          min={0.1}
          max={1}
          step={0.1}
          disabled={isReadonly}
        />
      </Form.Item>

      <Form.Item name="frequency_penalty" label="Frequency Penalty">
        <Input type="number" min={0} max={2} step={0.1} disabled={isReadonly} />
      </Form.Item>

      <Form.Item name="presence_penalty" label="Presence Penalty">
        <Input type="number" min={0} max={2} step={0.1} disabled={isReadonly} />
      </Form.Item>
    </div>
  );

  const getModalTitle = () => {
    const icons = {
      create: <PlusOutlined />,
      edit: <EditOutlined />,
      view: <EyeOutlined />,
    };

    const titles = {
      create: 'Tạo mới Blog AI',
      edit: 'Chỉnh sửa Blog AI',
      view: 'Xem chi tiết Blog AI',
    };

    return (
      <Space>
        {icons[mode]}
        {titles[mode]}
      </Space>
    );
  };

  const renderFooter = () => {
    if (isReadonly) {
      return [
        <Button key="close" onClick={onCancel}>
          Đóng
        </Button>,
      ];
    }

    return [
      <Button key="cancel" onClick={onCancel}>
        Hủy
      </Button>,
      <Button
        key="submit"
        type="primary"
        icon={<SaveOutlined />}
        loading={formState.submitting}
        onClick={() => form.submit()}
      >
        {isCreate ? 'Tạo mới' : 'Cập nhật'}
      </Button>,
    ];
  };

  return (
    <Modal
      title={getModalTitle()}
      open={visible}
      onCancel={onCancel}
      footer={renderFooter()}
      width={900}
      className="blog-ai-form-modal"
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="blog-ai-form"
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="form-tabs"
        >
          <TabPane tab="Thông tin cơ bản" key="basic">
            {renderBasicTab()}
          </TabPane>

          <TabPane tab="Cài đặt AI" key="ai">
            {renderAITab()}
          </TabPane>

          <TabPane tab="SEO & Metadata" key="seo">
            {renderSEOTab()}
          </TabPane>

          <TabPane tab="Nâng cao" key="advanced">
            {renderAdvancedTab()}
          </TabPane>
        </Tabs>
      </Form>
    </Modal>
  );
}
