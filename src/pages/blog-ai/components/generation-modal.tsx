import { LoadingOutlined, RobotOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Collapse,
  Form,
  Input,
  InputNumber,
  Modal,
  Progress,
  Radio,
  Select,
  Slider,
  Space,
  message,
} from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from 'services/console.service';
import { cancelGeneration, generateBlog, getGenerationStatus } from '../api';
import {
  DEFAULT_GENERATION_SETTINGS,
  GENERATION_TIMEOUT,
  LANGUAGE_OPTIONS,
  POLLING_INTERVAL,
  TONE_OPTIONS,
  WORD_COUNT_OPTIONS,
} from '../config';
import { useBlogAIStore } from '../store';
import {
  AI_MODEL_LABELS,
  AI_MODEL_TYPE,
  BLOG_AI_STATUS,
  GenerationRequest,
  GenerationResponse,
} from '../type';

const { TextArea } = Input;
const { Panel } = Collapse;

interface GenerationModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (result: GenerationResponse) => void;
}

export default function GenerationModal({
  visible,
  onCancel,
  onSuccess,
}: GenerationModalProps) {
  const { t } = useTranslation('blog-ai');
  const [form] = Form.useForm();
  const logger = ConsoleService.register('blog-ai-generation');

  const {
    generation,
    startGeneration,
    updateGenerationProgress,
    completeGeneration,
    failGeneration,
    cancelGeneration: cancelStore,
  } = useBlogAIStore();

  const [generating, setGenerating] = useState(false);
  const [currentGenerationId, setCurrentGenerationId] = useState<string>();
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<any>();
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Load prompt templates
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        // Mock templates for now
        setTemplates([]);
      } catch (error) {
        console.error('Error loading templates:', error);
      }
    };

    if (visible) {
      loadTemplates();
    }
  }, [visible]);

  // Polling for generation status
  useEffect(() => {
    let interval: NodeJS.Timeout;
    let timeout: NodeJS.Timeout;

    if (generating && currentGenerationId) {
      interval = setInterval(async () => {
        try {
          const response = await getGenerationStatus(currentGenerationId);
          if (response.status.success && response.data) {
            const {
              status,
              content,
              title,
              generation_time,
              quality_score,
              error_message,
            } = response.data;

            if (status === BLOG_AI_STATUS.GENERATED) {
              setGenerating(false);
              completeGeneration({
                id: currentGenerationId,
                title,
                content,
                status,
                generation_time,
                quality_score,
              } as any);
              message.success('Tạo nội dung thành công!');
              onSuccess(response.data);
            } else if (status === BLOG_AI_STATUS.FAILED) {
              setGenerating(false);
              failGeneration(error_message || 'Tạo nội dung thất bại');
              message.error(error_message || 'Tạo nội dung thất bại');
            } else if (status === BLOG_AI_STATUS.GENERATING) {
              // Update progress based on elapsed time
              const elapsed = Date.now() - (generation.startTime || Date.now());
              const progress = Math.min(90, (elapsed / 60000) * 100); // Max 90% until complete
              updateGenerationProgress(progress, 'Đang tạo nội dung...');
            }
          }
        } catch (error) {
          logger('Error checking generation status:', error);
        }
      }, POLLING_INTERVAL);

      // Timeout after 5 minutes
      timeout = setTimeout(() => {
        setGenerating(false);
        failGeneration('Quá thời gian chờ tạo nội dung');
        message.error('Quá thời gian chờ tạo nội dung');
      }, GENERATION_TIMEOUT);
    }

    return () => {
      if (interval) clearInterval(interval);
      if (timeout) clearTimeout(timeout);
    };
  }, [
    generating,
    currentGenerationId,
    generation.startTime,
    updateGenerationProgress,
    completeGeneration,
    failGeneration,
    logger,
    onSuccess,
  ]);

  const handleGenerate = useCallback(
    async (values: any) => {
      try {
        const request: GenerationRequest = {
          prompt: values.prompt,
          ai_model: values.ai_model,
          keywords: values.keywords
            ?.split(',')
            .map((k: string) => k.trim())
            .filter(Boolean),
          target_audience: values.target_audience,
          tone: values.tone,
          word_count: values.word_count,
          language: values.language,
          category_id: values.category_id,
          tags: values.tags,
          generation_settings: {
            temperature:
              values.temperature ?? DEFAULT_GENERATION_SETTINGS.temperature,
            max_tokens:
              values.max_tokens ?? DEFAULT_GENERATION_SETTINGS.max_tokens,
            top_p: values.top_p ?? DEFAULT_GENERATION_SETTINGS.top_p,
            frequency_penalty:
              values.frequency_penalty ??
              DEFAULT_GENERATION_SETTINGS.frequency_penalty,
            presence_penalty:
              values.presence_penalty ??
              DEFAULT_GENERATION_SETTINGS.presence_penalty,
          },
        };

        setGenerating(true);
        startGeneration(request);
        updateGenerationProgress(10, 'Đang khởi tạo...');

        const response = await generateBlog({
          ...request,
          prompt: request.prompt || '',
        });
        if (response.status.success && response.data) {
          setCurrentGenerationId(response.data.id);
          updateGenerationProgress(20, 'Đã gửi yêu cầu, đang xử lý...');
        } else {
          throw new Error(
            response.message || 'Không thể khởi tạo tạo nội dung',
          );
        }
      } catch (error: any) {
        setGenerating(false);
        failGeneration(error.message || 'Lỗi khi tạo nội dung');
        message.error(error.message || 'Lỗi khi tạo nội dung');
        logger('Error generating content:', error);
      }
    },
    [startGeneration, updateGenerationProgress, failGeneration, logger],
  );

  const handleCancel = useCallback(async () => {
    if (generating && currentGenerationId) {
      try {
        await cancelGeneration(currentGenerationId);
        setGenerating(false);
        cancelStore();
        message.info('Đã hủy tạo nội dung');
      } catch (error) {
        logger('Error cancelling generation:', error);
        message.error('Không thể hủy tạo nội dung');
      }
    }
    onCancel();
  }, [generating, currentGenerationId, cancelStore, onCancel, logger]);

  const handleTemplateSelect = useCallback(
    (template: any) => {
      setSelectedTemplate(template);
      form.setFieldsValue({
        prompt: template.prompt,
        tone: template.tone,
        word_count: template.word_count,
        language: template.language,
        ...template.generation_settings,
      });
    },
    [form],
  );

  const renderGenerationProgress = () => {
    if (!generating) return null;

    return (
      <Card className="generation-progress-card">
        <div className="generation-header">
          <LoadingOutlined spin />
          <span>Đang tạo nội dung...</span>
        </div>
        <Progress
          percent={generation.progress}
          status={generation.status === 'failed' ? 'exception' : 'active'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
        <div className="generation-info">
          <span>Thời gian ước tính: 2-5 phút</span>
          {generation.estimatedTime && (
            <span>Thời gian còn lại: {generation.estimatedTime}s</span>
          )}
        </div>
        {generation.errorMessage && (
          <Alert
            message="Lỗi tạo nội dung"
            description={generation.errorMessage}
            type="error"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Card>
    );
  };

  const renderForm = () => {
    if (generating) return null;

    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleGenerate}
        className="generation-form"
        initialValues={{
          ai_model: AI_MODEL_TYPE.GPT4,
          language: 'vi',
          tone: 'professional',
          word_count: 800,
          ...DEFAULT_GENERATION_SETTINGS,
        }}
      >
        {/* Template Selection */}
        {templates.length > 0 && (
          <Form.Item label="Mẫu prompt">
            <Select
              placeholder="Chọn mẫu prompt có sẵn"
              allowClear
              onChange={(value) => {
                const template = templates.find((t) => t.id === value);
                if (template) handleTemplateSelect(template);
              }}
            >
              {templates.map((template) => (
                <Select.Option key={template.id} value={template.id}>
                  <div>
                    <div>{template.name}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {template.description}
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {/* Main Prompt */}
        <Form.Item
          name="prompt"
          label="Prompt tạo nội dung"
          rules={[{ required: true, message: 'Vui lòng nhập prompt' }]}
          className="prompt-input"
        >
          <TextArea
            rows={6}
            placeholder="Mô tả chi tiết nội dung bạn muốn tạo...\n\nVí dụ: Viết một bài blog về lợi ích của việc sử dụng AI trong marketing, tập trung vào các ứng dụng thực tế và case study cụ thể."
          />
        </Form.Item>

        {/* AI Model Selection */}
        <Form.Item
          name="ai_model"
          label="Mô hình AI"
          initialValue={AI_MODEL_TYPE.GPT35}
        >
          <Radio.Group>
            {Object.values(AI_MODEL_TYPE).map((model) => (
              <Radio key={model} value={model}>
                {AI_MODEL_LABELS[model]}
              </Radio>
            ))}
            <Radio value={AI_MODEL_TYPE.GPT4}>
              {AI_MODEL_LABELS[AI_MODEL_TYPE.GPT4]}
            </Radio>
          </Radio.Group>
        </Form.Item>

        <div className="settings-section">
          <div className="section-title">Cài đặt cơ bản</div>

          <Form.Item name="language" label="Ngôn ngữ" initialValue="vi">
            <Select options={LANGUAGE_OPTIONS} />
          </Form.Item>

          <Form.Item name="tone" label="Giọng điệu" initialValue="professional">
            <Select options={TONE_OPTIONS} />
          </Form.Item>

          <Form.Item name="word_count" label="Số từ" initialValue={800}>
            <Select options={WORD_COUNT_OPTIONS} />
          </Form.Item>

          <Form.Item name="keywords" label="Từ khóa (phân cách bằng dấu phẩy)">
            <Input placeholder="AI, marketing, automation, technology" />
          </Form.Item>

          <Form.Item name="target_audience" label="Đối tượng mục tiêu">
            <Input placeholder="Marketer, doanh nghiệp vừa và nhỏ, chuyên gia IT" />
          </Form.Item>
        </div>

        {/* Advanced Settings */}
        <Collapse
          ghost
          onChange={(keys) => setShowAdvanced(keys.includes('advanced'))}
        >
          <Panel header="Cài đặt nâng cao" key="advanced">
            <div className="settings-section">
              <Form.Item
                name="temperature"
                label="Độ sáng tạo"
                tooltip="Giá trị cao hơn tạo ra nội dung sáng tạo hơn"
              >
                <Slider
                  min={0}
                  max={1}
                  step={0.1}
                  marks={{
                    0: 'Thận trọng',
                    0.5: 'Cân bằng',
                    1: 'Sáng tạo',
                  }}
                  defaultValue={DEFAULT_GENERATION_SETTINGS.temperature}
                />
              </Form.Item>

              <Form.Item
                name="max_tokens"
                label="Số token tối đa"
                tooltip="Giới hạn độ dài nội dung"
              >
                <InputNumber
                  min={100}
                  max={4000}
                  step={100}
                  style={{ width: '100%' }}
                  defaultValue={DEFAULT_GENERATION_SETTINGS.max_tokens}
                />
              </Form.Item>

              <Form.Item
                name="top_p"
                label={`Top P: ${form.getFieldValue('top_p') || DEFAULT_GENERATION_SETTINGS.top_p}`}
              >
                <Slider
                  min={0.1}
                  max={1}
                  step={0.1}
                  marks={{
                    0.1: '0.1',
                    0.5: '0.5',
                    1: '1.0',
                  }}
                />
              </Form.Item>

              <Form.Item
                name="frequency_penalty"
                label="Tần suất lặp lại"
                tooltip="Giảm việc lặp lại từ ngữ"
              >
                <Slider
                  min={0}
                  max={2}
                  step={0.1}
                  defaultValue={DEFAULT_GENERATION_SETTINGS.frequency_penalty}
                />
              </Form.Item>

              <Form.Item
                name="presence_penalty"
                label="Độ đa dạng chủ đề"
                tooltip="Khuyến khích thảo luận các chủ đề mới"
              >
                <Slider
                  min={0}
                  max={2}
                  step={0.1}
                  defaultValue={DEFAULT_GENERATION_SETTINGS.presence_penalty}
                />
              </Form.Item>
            </div>
          </Panel>
        </Collapse>

        <Form.Item style={{ marginTop: 24, marginBottom: 0 }}>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              icon={<RobotOutlined />}
              size="large"
            >
              Tạo nội dung
            </Button>
            <Button onClick={onCancel} size="large">
              Hủy
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <RobotOutlined />
          {generating ? 'Đang tạo nội dung AI' : 'Tạo nội dung AI'}
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      className="blog-ai-generation-modal"
      destroyOnClose
    >
      {generating ? renderGenerationProgress() : renderForm()}
    </Modal>
  );
}
