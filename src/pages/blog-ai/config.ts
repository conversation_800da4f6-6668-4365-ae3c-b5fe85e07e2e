// Blog AI Configuration
export const MODULE = 'blog-ai';
export const MODULE_NAME = 'Blog AI Management';
export const MODULE_POPUP = false;

// AI Generation Settings
export const DEFAULT_GENERATION_SETTINGS = {
  temperature: 0.7,
  max_tokens: 2000,
  top_p: 0.9,
  frequency_penalty: 0.1,
  presence_penalty: 0.1,
};

// Default values
export const DEFAULT_WORD_COUNT = 800;
export const DEFAULT_LANGUAGE = 'vi';
export const DEFAULT_TONE = 'professional';

// Available tones
export const TONE_OPTIONS = [
  { value: 'professional', label: '<PERSON><PERSON><PERSON>n nghiệp' },
  { value: 'casual', label: 'Thân thiện' },
  { value: 'formal', label: 'Trang trọng' },
  { value: 'conversational', label: 'Đối thoại' },
  { value: 'persuasive', label: 'Thuyết phục' },
  { value: 'informative', label: 'Thông tin' },
  { value: 'entertaining', label: 'Giải trí' },
];

// Available languages
export const LANGUAGE_OPTIONS = [
  { value: 'vi', label: 'Tiếng Việt' },
  { value: 'en', label: 'English' },
  { value: 'zh', label: '中文' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
];

// Word count options
export const WORD_COUNT_OPTIONS = [
  { value: 300, label: '300 từ' },
  { value: 500, label: '500 từ' },
  { value: 800, label: '800 từ' },
  { value: 1000, label: '1000 từ' },
  { value: 1500, label: '1500 từ' },
  { value: 2000, label: '2000 từ' },
];

// Quality score thresholds
export const QUALITY_THRESHOLDS = {
  EXCELLENT: 90,
  GOOD: 75,
  FAIR: 60,
  POOR: 40,
};

// Legacy export for backward compatibility
export const QUALITY_THRESHOLD = QUALITY_THRESHOLDS;

// Generation timeout (in milliseconds)
export const GENERATION_TIMEOUT = 300000; // 5 minutes

// Polling interval for generation status (in milliseconds)
export const POLLING_INTERVAL = 2000; // 2 seconds

// AI Models Configuration
export const AI_MODELS = [
  {
    id: 'gpt-4',
    name: 'GPT-4',
    description: 'Most capable model, best for complex tasks',
    max_tokens: 8192,
    cost_per_1k_tokens: 0.03,
    provider: 'openai',
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    description: 'Fast and efficient for most tasks',
    max_tokens: 4096,
    cost_per_1k_tokens: 0.002,
    provider: 'openai',
  },
  {
    id: 'claude-3-opus',
    name: 'Claude 3 Opus',
    description: 'Excellent for creative writing',
    max_tokens: 4096,
    cost_per_1k_tokens: 0.015,
    provider: 'anthropic',
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    description: 'Balanced performance and cost',
    max_tokens: 4096,
    cost_per_1k_tokens: 0.003,
    provider: 'anthropic',
  },
];
