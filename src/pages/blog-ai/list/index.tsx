// Blog AI List Page
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  FileOutlined,
  FileTextOutlined,
  LoadingOutlined,
  RestOutlined,
  RobotOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import { Badge, message } from 'antd';
import queryString from 'query-string';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks/index';
import ConsoleService from '../../../services/console.service';
import { getStatusStatistics, StatusCounts } from '../api';
import '../assets/styles.scss';
import { List } from './list';

export default function Index() {
  const { t } = useTranslation('blog-ai');
  const navigate = useNavigateTenant();
  const location = useLocation();
  const logger = ConsoleService.register('blog-ai-list');

  // Get status from query params
  const query = queryString.parse(location.search);
  const statusFilter = (query.status as string) || null;

  // State for status statistics
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    draft: 0,
    generating: 0,
    generated: 0,
    reviewing: 0,
    approved: 0,
    published: 0,
    failed: 0,
    cancelled: 0,
    total: 0,
  });

  // Fetch status statistics from API
  const fetchStatusStatistics = useCallback(async () => {
    try {
      const response = await getStatusStatistics();
      if (response.status.success && response.data) {
        setStatusCounts(response.data.status_counts);
      }
    } catch (error) {
      logger('Error fetching status statistics:', error);
      message.error(t('messages.error.load_failed'));
    }
  }, [logger]);

  // useEffect(() => {
  //   fetchStatusStatistics();
  // }, [fetchStatusStatistics]);

  // Handle menu click
  const handleMenuClick = useCallback(
    (key: string) => {
      const params = new URLSearchParams(location.search);

      if (key === 'all') {
        params.delete('status');
      } else {
        params.set('status', key);
      }

      const newSearch = params.toString();
      const newPath = newSearch
        ? `${location.pathname}?${newSearch}`
        : location.pathname;

      navigate(newPath);
    },
    [navigate, location],
  );

  // Menu items with status counts
  const menuItems = [
    {
      key: 'all',
      icon: <UnorderedListOutlined />,
      label: (
        <span>
          {t('menu.all')} <Badge count={statusCounts.total} showZero />
        </span>
      ),
    },
    {
      key: 'draft',
      icon: <FileOutlined />,
      label: (
        <span>
          {t('menu.draft')} <Badge count={statusCounts.draft} showZero />
        </span>
      ),
    },
    {
      key: 'generating',
      icon: <LoadingOutlined />,
      label: (
        <span>
          {t('menu.generating')}{' '}
          <Badge count={statusCounts.generating} showZero />
        </span>
      ),
    },
    {
      key: 'generated',
      icon: <RobotOutlined />,
      label: (
        <span>
          {t('menu.generated')}{' '}
          <Badge count={statusCounts.generated} showZero />
        </span>
      ),
    },
    {
      key: 'reviewing',
      icon: <EyeOutlined />,
      label: (
        <span>
          {t('menu.reviewing')}{' '}
          <Badge count={statusCounts.reviewing} showZero />
        </span>
      ),
    },
    {
      key: 'approved',
      icon: <CheckCircleOutlined />,
      label: (
        <span>
          {t('menu.approved')} <Badge count={statusCounts.approved} showZero />
        </span>
      ),
    },
    {
      key: 'published',
      icon: <FileTextOutlined />,
      label: (
        <span>
          {t('menu.published')}{' '}
          <Badge count={statusCounts.published} showZero />
        </span>
      ),
    },
    {
      key: 'failed',
      icon: <ExclamationCircleOutlined />,
      label: (
        <span>
          {t('menu.failed')} <Badge count={statusCounts.failed} showZero />
        </span>
      ),
    },
    {
      key: 'cancelled',
      icon: <RestOutlined />,
      label: (
        <span>
          {t('menu.cancelled')}{' '}
          <Badge count={statusCounts.cancelled} showZero />
        </span>
      ),
    },
  ];

  return (
    <div className="blog-ai-list-container">
      <div className="blog-ai-content">
        <List statusFilter={statusFilter} />
      </div>
    </div>
  );
}
