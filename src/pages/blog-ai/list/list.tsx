// Blog AI List Component
import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  RobotOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
  Tag,
  Progress,
  Tooltip,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { ButtonLink } from '../../../components/button';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  deleteItem,
  getItems,
  cancelGeneration,
  exportToBlog,
  regenerateBlog,
} from '../api';
import '../assets/styles.scss';
import { BackButton } from '../../../components/button';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
// GenerationModal removed - now using dedicated generation page
import { useBlogAIStore } from '../store';
import {
  BlogAI,
  BLOG_AI_STATUS,
  BLOG_AI_STATUS_LABELS,
  AI_MODEL_LABELS,
} from '../type';
import Search from './search';

function List({
  statusFilter,
  onStatusChange,
}: {
  statusFilter: string | null;
  onStatusChange?: () => void;
}) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useBlogAIStore() as any;

  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  // Removed showGenerationModal state - now using navigation
  const [items, setItems] = useState<BlogAI[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        limit: 10,
        after: afterKey,
        status: statusFilter,
        ...filters,
        ...payload,
      };

      try {
        const response = await getItems(params);
        if (response.status?.success) {
          // API trả về data là array trực tiếp, không có posts wrapper
          setItems(response.data || []);
          setTotal(response.data?.length || 0);
          // Tạm thời set pagination, cần cập nhật khi API có pagination
          setNextCursor(null);
        }
      } catch (error) {
        logger('Error fetching data:', error);
        message.error(t('messages.error.load_failed'));
      }
    },
    [afterKey, statusFilter, filters, setNextCursor, logger],
  );

  useEffectOnce(() => {
    fetchData();
  });

  const handleDelete = useCallback(
    async (id: string) => {
      try {
        await deleteItem(id);
        message.success(t('messages.success.deleted'));
        fetchData();
        onStatusChange?.();
      } catch (error) {
        logger('Error deleting item:', error);
        message.error(t('messages.error.delete_failed'));
      }
    },
    [fetchData, onStatusChange, logger],
  );

  const handleCancelGeneration = useCallback(
    async (id: string) => {
      try {
        await cancelGeneration(id);
        message.success(t('messages.success.cancelled'));
        fetchData();
        onStatusChange?.();
      } catch (error) {
        logger('Error cancelling generation:', error);
        message.error(t('messages.error.cancel_failed'));
      }
    },
    [fetchData, onStatusChange, logger],
  );

  const handleRegenerate = useCallback(
    async (id: string) => {
      try {
        await regenerateBlog(id, {});
        message.success(t('messages.success.generated'));
        fetchData();
        onStatusChange?.();
      } catch (error) {
        logger('Error regenerating:', error);
        message.error(t('messages.error.generation_failed'));
      }
    },
    [fetchData, onStatusChange, logger],
  );

  const handleExportToBlog = useCallback(
    async (id: string) => {
      try {
        await exportToBlog(id);
        message.success(t('messages.success.exported'));
        fetchData();
        onStatusChange?.();
      } catch (error) {
        logger('Error exporting to blog:', error);
        message.error(t('messages.error.export_failed'));
      }
    },
    [fetchData, onStatusChange, logger],
  );

  const getStatusTag = (status: BLOG_AI_STATUS) => {
    const colors = {
      [BLOG_AI_STATUS.DRAFT]: 'default',
      [BLOG_AI_STATUS.GENERATING]: 'processing',
      [BLOG_AI_STATUS.GENERATED]: 'success',
      [BLOG_AI_STATUS.REVIEWING]: 'warning',
      [BLOG_AI_STATUS.APPROVED]: 'success',
      [BLOG_AI_STATUS.PUBLISHED]: 'green',
      [BLOG_AI_STATUS.FAILED]: 'error',
      [BLOG_AI_STATUS.CANCELLED]: 'default',
    };

    return <Tag color={colors[status]}>{BLOG_AI_STATUS_LABELS[status]}</Tag>;
  };

  const getQualityScore = (score?: number) => {
    if (!score) return null;

    const color =
      score >= 90
        ? 'success'
        : score >= 75
          ? 'normal'
          : score >= 60
            ? 'exception'
            : 'exception';

    return (
      <Tooltip title={`${t('fields.quality_score')}: ${score}%`}>
        <Progress
          percent={score}
          size="small"
          status={color}
          showInfo={false}
          style={{ width: 60 }}
        />
      </Tooltip>
    );
  };

  const columns = [
    {
      title: t('fields.title'),
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: BlogAI) => (
        <div>
          <div className="font-medium">{text || t('messages.no_title')}</div>
          <div className="text-sm text-gray-500">
            {record.ai_model && <Tag>{AI_MODEL_LABELS[record.ai_model]}</Tag>}
            {record.word_count && (
              <span className="ml-2">
                {record.word_count} {t('fields.words')}
              </span>
            )}
          </div>
        </div>
      ),
    },
    {
      title: t('fields.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: BLOG_AI_STATUS) => getStatusTag(status),
    },
    {
      title: t('fields.quality'),
      dataIndex: 'quality_score',
      key: 'quality_score',
      render: (score: number) => getQualityScore(score),
    },
    {
      title: t('fields.author'),
      dataIndex: 'author',
      key: 'author',
      render: (author: any) =>
        author?.name || author?.first_name || author?.username || 'N/A',
    },
    {
      title: t('fields.ai_model'),
      dataIndex: ['ai_metadata', 'model'],
      key: 'ai_model',
      render: (model: string) => model || 'N/A',
    },
    {
      title: t('fields.tokens_used'),
      dataIndex: ['ai_metadata', 'tokens_used'],
      key: 'tokens_used',
      render: (tokens: number) => (tokens ? tokens.toLocaleString() : 'N/A'),
    },
    {
      title: t('fields.created_at'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
    },
    {
      title: t('actions.actions'),
      key: 'actions',
      render: (_: any, record: BlogAI) => {
        const menuItems = [
          {
            key: 'view',
            icon: <EyeOutlined />,
            label: t('actions.view_detail'),
            onClick: () => navigate(`/blog-ai/${record.id}`),
          },
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: t('actions.edit'),
            onClick: () => {
              setIdCurrent(record.id);
              setShowModal(true);
            },
          },
        ];

        // Add status-specific actions
        if (record.status === BLOG_AI_STATUS.GENERATING) {
          menuItems.push({
            key: 'cancel',
            icon: <StopOutlined />,
            label: t('actions.cancel_generation'),
            onClick: () => handleCancelGeneration(record.id!),
          });
        }

        if (
          [BLOG_AI_STATUS.FAILED, BLOG_AI_STATUS.CANCELLED].includes(
            record.status!,
          )
        ) {
          menuItems.push({
            key: 'regenerate',
            icon: <ReloadOutlined />,
            label: t('actions.regenerate'),
            onClick: () => handleRegenerate(record.id!),
          });
        }

        if (
          [BLOG_AI_STATUS.GENERATED, BLOG_AI_STATUS.APPROVED].includes(
            record.status!,
          )
        ) {
          menuItems.push({
            key: 'export',
            icon: <ExportOutlined />,
            label: t('actions.export_to_blog'),
            onClick: () => handleExportToBlog(record.id!),
          });
        }

        menuItems.push({
          key: 'delete',
          icon: <DeleteOutlined />,
          label: t('actions.delete'),
          onClick: () => handleDelete(record.id!),
        });

        return (
          <Space>
            <Dropdown
              menu={{
                items: menuItems.map((item) => ({
                  ...item,
                  onClick: () => item.onClick(),
                })),
              }}
              trigger={['click']}
            >
              <Button>{t('actions.actions')}</Button>
            </Dropdown>
            <Popconfirm
              title={t('messages.confirm.delete')}
              onConfirm={() => handleDelete(record.id!)}
              okText={t('actions.delete')}
              cancelText={t('common.cancel')}
            >
              <Button danger>
                <DeleteOutlined />
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div className="blog-ai-list">
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-4">
              <BackButton destination="back" />
              <h1 className="text-2xl font-bold">{t('title')}</h1>
            </div>
            <Space>
              <Button
                type="primary"
                icon={<RobotOutlined />}
                onClick={() => navigate('/blog-ai/generation')}
              >
                {t('actions.generate_content')}
              </Button>
              <Button
                type="primary"
                icon={<PlusCircleOutlined />}
                onClick={() => {
                  setIdCurrent(undefined);
                  setShowModal(true);
                }}
              >
                {t('actions.add_new')}
              </Button>
            </Space>
          </div>
        </Col>

        <Col span={24}>
          <Search onSearch={setFilters} />
        </Col>

        <Col span={24}>
          <Table
            columns={columns}
            dataSource={items}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1000 }}
          />
        </Col>

        <Col span={24}>
          <CursorPagination
            total={total}
            isBack={isBack}
            isNext={isNext}
            goNext={goNext}
            goBack={goBack}
          />
        </Col>
      </Row>

      {showModal && (
        <ModalForm
          mode="create"
          visible={showModal}
          onCancel={() => setShowModal(false)}
          onSuccess={() => {
            setShowModal(false);
            fetchData();
            onStatusChange?.();
          }}
        />
      )}

      {/* GenerationModal removed - now using dedicated generation page */}
    </div>
  );
}

export { List };
export default List;
