// Blog AI Search Component
import { SearchOutlined, ClearOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select, DatePicker, Space } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  AI_MODEL_TYPE,
  AI_MODEL_LABELS,
  BLOG_AI_STATUS,
  BLOG_AI_STATUS_LABELS,
} from '../type';
import { TONE_OPTIONS, LANGUAGE_OPTIONS } from '../config';

const { RangePicker } = DatePicker;

interface SearchProps {
  onSearch: (filters: any) => void;
}

export default function Search({ onSearch }: SearchProps) {
  const { t } = useTranslation('blog-ai');
  const [form] = Form.useForm();

  const handleSearch = useCallback(
    (values: any) => {
      const filters: any = {};

      // Text search
      if (values.keyword) {
        filters.keyword = values.keyword;
      }

      // Status filter
      if (values.status) {
        filters.status = values.status;
      }

      // AI Model filter
      if (values.ai_model) {
        filters.ai_model = values.ai_model;
      }

      // Language filter
      if (values.language) {
        filters.language = values.language;
      }

      // Tone filter
      if (values.tone) {
        filters.tone = values.tone;
      }

      // Author filter
      if (values.author) {
        filters.author = values.author;
      }

      // Date range filter
      if (values.dateRange && values.dateRange.length === 2) {
        filters.created_from = values.dateRange[0].format('YYYY-MM-DD');
        filters.created_to = values.dateRange[1].format('YYYY-MM-DD');
      }

      // Quality score range
      if (values.quality_min !== undefined) {
        filters.quality_min = values.quality_min;
      }
      if (values.quality_max !== undefined) {
        filters.quality_max = values.quality_max;
      }

      // Word count range
      if (values.word_count_min !== undefined) {
        filters.word_count_min = values.word_count_min;
      }
      if (values.word_count_max !== undefined) {
        filters.word_count_max = values.word_count_max;
      }

      onSearch(filters);
    },
    [onSearch],
  );

  const handleReset = useCallback(() => {
    form.resetFields();
    onSearch({});
  }, [form, onSearch]);

  const statusOptions = Object.entries(BLOG_AI_STATUS_LABELS).map(
    ([value, label]) => ({
      value,
      label,
    }),
  );

  const aiModelOptions = Object.entries(AI_MODEL_LABELS).map(
    ([value, label]) => ({
      value,
      label,
    }),
  );

  return (
    <div className="blog-ai-search">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSearch}
        className="search-form"
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item name="keyword" label={t('fields.keyword')}>
              <Input
                placeholder={t('placeholders.search_keyword')}
                allowClear
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item name="status" label={t('fields.status')}>
              <Select
                placeholder={t('placeholders.select_status')}
                allowClear
                options={statusOptions}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item name="ai_model" label={t('fields.ai_model')}>
              <Select
                placeholder={t('placeholders.select_ai_model')}
                allowClear
                options={aiModelOptions}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item name="language" label={t('fields.language')}>
              <Select
                placeholder={t('placeholders.select_language')}
                allowClear
                options={LANGUAGE_OPTIONS}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item name="tone" label={t('fields.tone')}>
              <Select
                placeholder={t('placeholders.select_tone')}
                allowClear
                options={TONE_OPTIONS}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item name="author" label={t('fields.author')}>
              <Input placeholder={t('placeholders.author_name')} allowClear />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item name="dateRange" label={t('fields.created_at')}>
              <RangePicker
                style={{ width: '100%' }}
                placeholder={[
                  t('placeholders.from_date'),
                  t('placeholders.to_date'),
                ]}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item label={t('fields.quality_score')}>
              <Input.Group compact>
                <Form.Item name="quality_min" noStyle>
                  <Input
                    style={{ width: '45%' }}
                    placeholder={t('placeholders.min')}
                    type="number"
                    min={0}
                    max={100}
                  />
                </Form.Item>
                <Input
                  style={{
                    width: '10%',
                    borderLeft: 0,
                    borderRight: 0,
                    pointerEvents: 'none',
                  }}
                  placeholder="~"
                  disabled
                />
                <Form.Item name="quality_max" noStyle>
                  <Input
                    style={{ width: '45%', borderLeft: 0 }}
                    placeholder={t('placeholders.max')}
                    type="number"
                    min={0}
                    max={100}
                  />
                </Form.Item>
              </Input.Group>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item label={t('fields.word_count')}>
              <Input.Group compact>
                <Form.Item name="word_count_min" noStyle>
                  <Input
                    style={{ width: '45%' }}
                    placeholder={t('placeholders.min')}
                    type="number"
                    min={0}
                  />
                </Form.Item>
                <Input
                  style={{
                    width: '10%',
                    borderLeft: 0,
                    borderRight: 0,
                    pointerEvents: 'none',
                  }}
                  placeholder="~"
                  disabled
                />
                <Form.Item name="word_count_max" noStyle>
                  <Input
                    style={{ width: '45%', borderLeft: 0 }}
                    placeholder={t('placeholders.max')}
                    type="number"
                    min={0}
                  />
                </Form.Item>
              </Input.Group>
            </Form.Item>
          </Col>

          <Col xs={24}>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                >
                  {t('actions.search')}
                </Button>
                <Button onClick={handleReset} icon={<ClearOutlined />}>
                  {t('actions.clear_filter')}
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
