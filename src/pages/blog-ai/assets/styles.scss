// Blog AI Module Styles
.blog-ai-container {
  .list-container {
    display: flex;
    gap: 24px;

    .sidebar {
      width: 280px;
      flex-shrink: 0;

      .status-menu {
        .ant-menu-item {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .count {
            background: #f0f0f0;
            color: #666;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            min-width: 20px;
            text-align: center;
          }

          &.ant-menu-item-selected .count {
            background: #1890ff;
            color: white;
          }
        }
      }
    }

    .main-content {
      flex: 1;
      min-width: 0;

      .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .title {
          margin: 0;
        }

        .actions {
          display: flex;
          gap: 8px;
        }
      }

      .table-container {
        .ant-table {
          .status-cell {
            .ant-tag {
              margin: 0;
            }
          }

          .quality-cell {
            .quality-score {
              display: flex;
              align-items: center;
              gap: 4px;

              .ant-rate {
                font-size: 12px;
                line-height: 1;
              }
            }
          }

          .actions-cell {
            .ant-space {
              .ant-btn {
                padding: 4px 8px;
                height: auto;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  .search-form {
    .ant-form {
      .ant-row {
        .ant-col {
          padding: 0 8px;
        }
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        margin-top: 16px;
      }
    }
  }
}

.blog-ai-list {
  .search-form {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .ant-table {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }
}

.blog-ai-search {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-input-group {
    .ant-input:first-child {
      border-radius: 6px 0 0 6px;
    }

    .ant-input:last-child {
      border-radius: 0 6px 6px 0;
    }
  }
}

// Generation Modal Styles
.blog-ai-generation-modal {
  .ant-modal-content {
    border-radius: 12px;
  }

  .generation-form {
    .ant-form-item {
      margin-bottom: 20px;
    }

    .prompt-input {
      .ant-input {
        min-height: 120px;
        resize: vertical;
      }
    }

    .settings-section {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      margin-top: 16px;

      .section-title {
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }
    }

    .model-selection {
      .ant-radio-group {
        width: 100%;

        .ant-radio-button-wrapper {
          flex: 1;
          text-align: center;

          &.ant-radio-button-wrapper-checked {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
          }
        }
      }
    }
  }

  .generation-progress {
    text-align: center;
    padding: 40px 20px;

    .progress-icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;

      &.generating {
        animation: spin 2s linear infinite;
      }
    }

    .progress-text {
      font-size: 16px;
      color: #666;
      margin-bottom: 20px;
    }

    .progress-bar {
      margin-bottom: 16px;
    }

    .estimated-time {
      font-size: 14px;
      color: #999;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Form Modal Styles
.blog-ai-form-modal {
  .ant-modal-content {
    border-radius: 12px;
  }

  .form-content {
    .ant-form-item {
      margin-bottom: 20px;
    }

    .content-editor {
      .ant-input {
        min-height: 200px;
        resize: vertical;
      }
    }

    .tags-input {
      .ant-select-multiple {
        .ant-select-selection-item {
          background: #e6f7ff;
          border-color: #91d5ff;
          color: #1890ff;
        }
      }
    }

    .seo-section {
      background: #f0f8ff;
      padding: 16px;
      border-radius: 8px;
      border: 1px solid #d6e4ff;

      .section-title {
        font-weight: 600;
        margin-bottom: 12px;
        color: #1890ff;
      }
    }
  }
}

// Detail Page Styles
.blog-ai-detail {
  .detail-header {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #333;
    }

    .meta-info {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          font-weight: 500;
          color: #666;
        }

        .value {
          color: #333;
        }
      }
    }

    .actions {
      margin-top: 16px;

      .ant-btn {
        margin-right: 8px;
      }
    }
  }

  .detail-content {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .content-section {
      margin-bottom: 32px;

      .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
        color: #333;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 8px;
      }

      .section-content {
        line-height: 1.6;
        color: #555;

        p {
          margin-bottom: 16px;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 24px;
          margin-bottom: 16px;
          color: #333;
        }

        ul,
        ol {
          margin-bottom: 16px;
          padding-left: 24px;
        }

        blockquote {
          border-left: 4px solid #1890ff;
          padding-left: 16px;
          margin: 16px 0;
          color: #666;
          font-style: italic;
        }
      }
    }
  }

  .generation-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .info-item {
        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 4px;
        }

        .value {
          color: #333;
          font-size: 16px;
        }
      }
    }
  }
}

// Quality Score Styles
.quality-score {
  display: inline-flex;
  align-items: center;
  gap: 8px;

  .score-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;

    &.excellent {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }

    &.good {
      background: #fff7e6;
      color: #fa8c16;
      border: 1px solid #ffd591;
    }

    &.fair {
      background: #fff1f0;
      color: #ff4d4f;
      border: 1px solid #ffadd2;
    }

    &.poor {
      background: #f5f5f5;
      color: #8c8c8c;
      border: 1px solid #d9d9d9;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .blog-ai-list-container {
    flex-direction: column;
    height: auto;

    .blog-ai-sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #f0f0f0;

      .blog-ai-status-menu {
        display: flex;
        overflow-x: auto;

        .ant-menu-item {
          white-space: nowrap;
          margin-right: 8px;
        }
      }
    }
  }

  .blog-ai-detail {
    .detail-header {
      .meta-info {
        flex-direction: column;
        gap: 12px;
      }
    }

    .generation-info {
      .info-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
