{"title": "AI Blog Management", "subtitle": "Create and manage AI-generated blog content", "menu": {"all": "All", "draft": "Draft", "generating": "Generating", "generated": "Generated", "reviewing": "Reviewing", "approved": "Approved", "published": "Published", "failed": "Failed", "cancelled": "Cancelled"}, "status": {"draft": "Draft", "generating": "Generating", "generated": "Generated", "published": "Published", "failed": "Failed"}, "actions": {"create": "Create New", "edit": "Edit", "view": "View", "delete": "Delete", "generate": "Generate Content", "regenerate": "Regenerate", "cancel": "Cancel Generation", "export": "Export to Blog", "search": "Search", "filter": "Filter", "clear": "Clear", "refresh": "Refresh", "add_new": "Add New", "generate_content": "Generate AI Content", "export_to_blog": "Export to Blog", "cancel_generation": "Cancel Generation", "view_detail": "View Detail", "actions": "Actions", "clear_filter": "Clear Filter"}, "fields": {"title": "Title", "content": "Content", "prompt": "Prompt", "ai_model": "AI Model", "language": "Language", "tone": "<PERSON><PERSON>", "word_count": "Word Count", "keywords": "Keywords", "target_audience": "Target Audience", "status": "Status", "quality_score": "Quality Score", "created_at": "Created At", "updated_at": "Updated At", "author": "Author", "category": "Category", "tags": "Tags", "meta_title": "Meta Title", "meta_description": "Meta Description", "is_featured": "Featured", "generation_time": "Generation Time", "creativity_level": "Creativity Level", "words": "words", "keyword": "Keyword"}, "placeholders": {"search_keyword": "Search by title, content...", "select_status": "Select status", "select_model": "Select AI model", "select_ai_model": "Select AI model", "select_language": "Select language", "select_tone": "Select tone", "select_author": "Select author", "author_name": "Author name", "from_date": "From date", "to_date": "To date", "min": "Min", "max": "Max", "enter_prompt": "Enter detailed description of the content you want to create...", "enter_keywords": "Enter keywords separated by commas", "enter_target_audience": "Enter target audience"}, "messages": {"success": {"created": "Blog AI created successfully", "updated": "Blog AI updated successfully", "deleted": "Blog AI deleted successfully", "generated": "Content generated successfully", "exported": "Exported to blog successfully", "cancelled": "Generation cancelled"}, "no_content": "No content available", "no_title": "No title", "error": {"create_failed": "Failed to create blog AI", "update_failed": "Failed to update blog AI", "delete_failed": "Failed to delete blog AI", "generation_failed": "Content generation failed", "export_failed": "Failed to export to blog", "cancel_failed": "Failed to cancel generation", "load_failed": "Failed to load data", "timeout": "Generation timeout"}, "confirm": {"delete": "Are you sure you want to delete this blog AI?", "cancel_generation": "Are you sure you want to cancel the generation?", "regenerate": "Are you sure you want to regenerate this content?"}}, "generation": {"title": "Generate AI Content", "generating": "Generating...", "generate": "Generate Content", "save": "Save", "saved": "Content saved successfully", "success": "Content generated successfully", "progress": {"initializing": "Initializing...", "processing": "Processing request...", "generating": "Generating content...", "finalizing": "Finalizing..."}, "estimated_time": "Estimated time: 2-5 minutes", "steps": {"basicInfo": "Basic Information", "basicInfoDesc": "Enter title, description and keywords", "aiSettings": "AI Settings", "aiSettingsDesc": "Choose AI model, language and tone", "advanced": "Advanced Settings", "advancedDesc": "Fine-tune generation parameters", "review": "Review & Generate", "reviewDesc": "Review settings and generate content"}, "form": {"title": "Title", "titleRequired": "Please enter title", "titlePlaceholder": "Enter blog title...", "contentPlaceholder": "Enter blog content...", "description": "Description", "descriptionRequired": "Please enter description", "descriptionPlaceholder": "Describe the content you want to create...", "keywords": "Keywords", "keywordsPlaceholder": "Enter keywords...", "model": "AI Model", "modelRequired": "Please select AI model", "language": "Language", "languagePlaceholder": "Select language", "tone": "<PERSON><PERSON>", "tonePlaceholder": "Select tone", "wordCount": "Word Count", "wordCountPlaceholder": "Select word count", "temperature": "Creativity Level", "conservative": "Conservative", "balanced": "Balanced", "creative": "Creative", "maxTokens": "<PERSON>", "frequencyPenalty": "Frequency Penalty", "presencePenalty": "Presence Penalty"}, "review": {"settings": "Generation Settings"}, "result": {"title": "Generated Content"}, "error": {"title": "Generation Failed", "failed": "Content generation failed"}, "settings": {"basic": "Basic Settings", "advanced": "Advanced Settings", "temperature": "Creativity", "max_tokens": "<PERSON>", "top_p": "Top P", "frequency_penalty": "Frequency Penalty", "presence_penalty": "Presence Penalty"}, "templates": {"select": "Select prompt template", "custom": "Custom prompt"}}, "quality": {"analyze": "Analyze Quality", "score": "Quality Score", "high": "High Quality", "medium": "Medium Quality", "low": "Needs Improvement"}, "models": {"gpt4": "GPT-4", "gpt35": "GPT-3.5", "claude": "<PERSON>", "gemini": "Gemini"}, "languages": {"vi": "Vietnamese", "en": "English", "zh": "Chinese", "ja": "Japanese", "ko": "Korean"}, "tones": {"professional": "Professional", "casual": "Casual", "friendly": "Friendly", "formal": "Formal", "humorous": "Humorous", "persuasive": "Persuasive", "informative": "Informative"}, "word_counts": {"300": "Short (300 words)", "500": "Medium (500 words)", "800": "Standard (800 words)", "1200": "Long (1200 words)", "1500": "Extended (1500 words)"}, "statistics": {"total": "Total", "this_month": "This Month", "success_rate": "Success Rate", "avg_quality": "Average Quality", "avg_generation_time": "Average Generation Time"}, "common": {"back": "Back", "previous": "Previous", "next": "Next", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "notFound": "Not Found", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?"}, "pleaseEnterData": "Please enter data", "breadcrumb": {"blog_ai": "Blog AI", "create": "Create", "edit": "Edit", "detail": "Detail"}, "form": {"create_title": "Create Blog AI", "edit_title": "Edit Blog AI", "detail_title": "Blog AI Detail", "manual_creation": "Manual Creation", "manual_description": "Create blog AI by manually entering information", "ai_creation": "AI Creation", "ai_description": "Use AI to automatically generate blog content", "loading_form": "Loading form..."}}