import React, { useEffect, useState, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { getItem } from '../api';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Descriptions,
  Progress,
  Alert,
  Divider,
  message,
  Modal,
  Spin,
  Statistic,
  Row,
  Col,
  Tooltip,
  Rate,
  Breadcrumb,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  ReloadOutlined,
  EyeOutlined,
  RobotOutlined,
  FileTextOutlined,
  CalendarOutlined,
  UserOutlined,
  SettingOutlined,
  ShareAltOutlined,
  DownloadOutlined,
  PrinterOutlined,
} from '@ant-design/icons';
import { useBlogAIStore } from '../store';
import { FormModal } from '../components';
import { deleteItem, exportToBlog } from '../api';
import { BlogAI, BLOG_AI_STATUS } from '../type';
import { blogAITranslations } from '../i18n';
import '../assets/styles.scss';

const { Title, Paragraph, Text } = Typography;
const { confirm } = Modal;

export default function BlogAIDetail() {
  const { t } = useTranslation('blog-ai');
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const logger = ConsoleService.register('blog-ai-detail');

  const {
    data: { loading },
    setDataLoading,
  } = useBlogAIStore();

  const [item, setItem] = useState<BlogAI | null>(null);
  const [formModalVisible, setFormModalVisible] = useState(false);
  // Removed generationModalVisible state - now using navigation
  const [exporting, setExporting] = useState(false);
  const [regenerating, setRegenerating] = useState(false);

  // Load item data
  useEffect(() => {
    const loadItem = async () => {
      if (!id) return;

      try {
        setDataLoading(true);
        const response = await getItem(id);
        if (response.status === 'success' && response.data?.post) {
          setItem(response.data.post);
        } else {
          message.error(t('messages.error.load_failed'));
          navigate('/blog-ai');
        }
      } catch (error: any) {
        logger('Error loading item:', error);
        message.error(error.message || t('messages.error.load_failed'));
        navigate('/blog-ai');
      } finally {
        setDataLoading(false);
      }
    };

    loadItem();
  }, [id, navigate, setDataLoading, logger]);

  const handleEdit = useCallback(() => {
    setFormModalVisible(true);
  }, []);

  const handleDelete = useCallback(() => {
    if (!item) return;

    Modal.confirm({
      title: t('messages.confirm.delete'),
      content: t('messages.confirm.delete'),
      okText: t('actions.delete'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: async () => {
        try {
          const response = await deleteItem(item.id);
          if (response.success) {
            message.success(t('messages.success.deleted'));
            navigate('/blog-ai');
          } else {
            throw new Error(
              response.message || t('messages.error.delete_failed'),
            );
          }
        } catch (error: any) {
          logger('Error deleting item:', error);
          message.error(error.message || t('messages.error.delete_failed'));
        }
      },
    });
  }, [item, navigate, logger]);

  const handleExport = useCallback(async () => {
    if (!item) return;

    try {
      setExporting(true);
      const response = await exportToBlog(item.id);
      if (response.success) {
        message.success(t('messages.success.exported'));
        // Reload item to update status
        const updatedResponse = await getItem(item.id);
        if (updatedResponse.success && updatedResponse.data) {
          setItem(updatedResponse.data);
        }
      } else {
        throw new Error(response.message || t('messages.error.export_failed'));
      }
    } catch (error: any) {
      logger('Error exporting to blog:', error);
      message.error(error.message || t('messages.error.export_failed'));
    } finally {
      setExporting(false);
    }
  }, [item, logger]);

  const handleRegenerate = useCallback(() => {
    if (!item) return;

    confirm({
      title: t('messages.confirm.regenerate'),
      content: t('messages.confirm.regenerate'),
      okText: t('actions.regenerate'),
      cancelText: t('common.cancel'),
      onOk: async () => {
        try {
          setRegenerating(true);
          const response = await regenerateBlog(item.id);
          if (response.success) {
            message.success(t('messages.success.generated'));
            navigate('/blog-ai/generation');
          } else {
            throw new Error(
              response.message || t('messages.error.generation_failed'),
            );
          }
        } catch (error: any) {
          logger('Error regenerating:', error);
          message.error(error.message || t('messages.error.generation_failed'));
        } finally {
          setRegenerating(false);
        }
      },
    });
  }, [item, logger]);

  const handleFormSuccess = useCallback((updatedItem: BlogAI) => {
    setItem(updatedItem);
    setFormModalVisible(false);
    message.success(t('messages.success.updated'));
  }, []);

  const handleGenerationSuccess = useCallback(
    (result: any) => {
      // Reload item to get updated data
      if (id) {
        getItem(id).then((response) => {
          if (response.success && response.data) {
            setItem(response.data);
          }
        });
      }
      // Navigation handled by route change
    },
    [id],
  );

  const getStatusColor = (status: BLOG_AI_STATUS) => {
    const colors = {
      [BLOG_AI_STATUS.DRAFT]: 'default',
      [BLOG_AI_STATUS.GENERATING]: 'processing',
      [BLOG_AI_STATUS.GENERATED]: 'success',
      [BLOG_AI_STATUS.PUBLISHED]: 'blue',
      [BLOG_AI_STATUS.FAILED]: 'error',
    };
    return colors[status] || 'default';
  };

  const getQualityColor = (score: number) => {
    if (score >= 8) return '#52c41a';
    if (score >= 6) return '#faad14';
    return '#ff4d4f';
  };

  const renderHeader = () => (
    <div className="detail-header">
      <Breadcrumb className="breadcrumb">
        <Breadcrumb.Item>
          <Button
            type="link"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/blog-ai')}
          >
            Blog AI
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>Chi tiết</Breadcrumb.Item>
      </Breadcrumb>

      <div className="header-content">
        <div className="title-section">
          <Title level={2} className="title">
            {item?.title || t('common.loading')}
          </Title>
          <Space className="status-info">
            <Tag color={getStatusColor(item?.status || BLOG_AI_STATUS.DRAFT)}>
              {item?.status || t('fields.status')}
            </Tag>
            {item?.quality_score && (
              <div className="quality-score">
                <Rate
                  disabled
                  allowHalf
                  value={item.quality_score / 2}
                  style={{ color: getQualityColor(item.quality_score) }}
                />
                <Text type="secondary">{item.quality_score}/10</Text>
              </div>
            )}
          </Space>
        </div>

        <Space className="actions">
          <Button
            icon={<EyeOutlined />}
            onClick={() => setFormModalVisible(true)}
          >
            Xem chi tiết
          </Button>
          <Button icon={<EditOutlined />} onClick={handleEdit}>
            {t('actions.edit')}
          </Button>
          {item?.status === BLOG_AI_STATUS.GENERATED && (
            <Button
              type="primary"
              icon={<ExportOutlined />}
              loading={exporting}
              onClick={handleExport}
            >
              {t('actions.export_to_blog')}
            </Button>
          )}
          <Button
            icon={<ReloadOutlined />}
            loading={regenerating}
            onClick={handleRegenerate}
          >
            {t('actions.regenerate')}
          </Button>
          <Button danger icon={<DeleteOutlined />} onClick={handleDelete}>
            {t('actions.delete')}
          </Button>
        </Space>
      </div>
    </div>
  );

  const renderMetadata = () => (
    <Card title={t('fields.metadata')} className="metadata-card">
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic
            title={t('fields.ai_model')}
            value={item?.ai_metadata?.model || item?.ai_model || 'N/A'}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title={t('fields.provider')}
            value={item?.ai_metadata?.provider || 'N/A'}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title={t('fields.tokens_used')}
            value={item?.ai_metadata?.tokens_used || 'N/A'}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title={t('fields.confidence_score')}
            value={
              item?.ai_metadata?.confidence_score
                ? `${(item.ai_metadata.confidence_score * 100).toFixed(1)}%`
                : 'N/A'
            }
          />
        </Col>
        <Col span={8}>
          <Statistic
            title={t('fields.generation_time')}
            value={
              item?.ai_metadata?.generation_time
                ? `${item.ai_metadata.generation_time}s`
                : 'N/A'
            }
          />
        </Col>
        <Col span={8}>
          <Statistic
            title={t('fields.language')}
            value={item?.language || 'N/A'}
          />
        </Col>
        <Col span={8}>
          <Statistic title={t('fields.tone')} value={item?.tone || 'N/A'} />
        </Col>
        <Col span={8}>
          <Statistic
            title={t('fields.word_count')}
            value={item?.word_count || 'N/A'}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="Thời gian tạo"
            value={item?.generation_time ? `${item.generation_time}s` : 'N/A'}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title={t('fields.created_at')}
            value={
              item?.created_at
                ? new Date(item.created_at).toLocaleDateString('vi-VN')
                : 'N/A'
            }
          />
        </Col>
      </Row>

      {item?.keywords && item.keywords.length > 0 && (
        <>
          <Divider />
          <div className="keywords-section">
            <Text strong>Từ khóa:</Text>
            <div className="keywords">
              {item.keywords.map((keyword, index) => (
                <Tag key={index} color="blue">
                  {keyword}
                </Tag>
              ))}
            </div>
          </div>
        </>
      )}

      {item?.target_audience && (
        <>
          <Divider />
          <div className="audience-section">
            <Text strong>Đối tượng mục tiêu:</Text>
            <Paragraph>{item.target_audience}</Paragraph>
          </div>
        </>
      )}
    </Card>
  );

  const renderAnalytics = () => (
    <Card title={t('fields.analytics')} className="analytics-card">
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Statistic
            title={t('fields.views')}
            value={item?.analytics?.views || 0}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('fields.shares')}
            value={item?.analytics?.shares || 0}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('fields.likes')}
            value={item?.analytics?.likes || 0}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('fields.comments')}
            value={item?.analytics?.comments_count || 0}
          />
        </Col>
      </Row>
      {item?.analytics?.engagement_rate && (
        <>
          <Divider />
          <Progress
            percent={Math.round(item.analytics.engagement_rate * 100)}
            format={(percent) => `${percent}% Engagement`}
          />
        </>
      )}
    </Card>
  );

  const renderSEOScore = () => (
    <Card title={t('fields.seo_score')} className="seo-card">
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Progress
            type="circle"
            percent={item?.seo_score?.overall || 0}
            format={(percent) => `${percent}`}
          />
          <div style={{ textAlign: 'center', marginTop: 8 }}>Overall</div>
        </Col>
        <Col span={6}>
          <Progress
            type="circle"
            percent={item?.seo_score?.readability || 0}
            format={(percent) => `${percent}`}
          />
          <div style={{ textAlign: 'center', marginTop: 8 }}>Readability</div>
        </Col>
        <Col span={6}>
          <Progress
            type="circle"
            percent={item?.seo_score?.keyword_density || 0}
            format={(percent) => `${percent}`}
          />
          <div style={{ textAlign: 'center', marginTop: 8 }}>Keywords</div>
        </Col>
        <Col span={6}>
          <Progress
            type="circle"
            percent={item?.seo_score?.meta_optimization || 0}
            format={(percent) => `${percent}`}
          />
          <div style={{ textAlign: 'center', marginTop: 8 }}>Meta</div>
        </Col>
      </Row>
    </Card>
  );

  const renderPrompt = () => (
    <Card title="Prompt gốc" className="prompt-card">
      <Paragraph className="prompt-content">
        {item?.prompt || 'Không có prompt'}
      </Paragraph>
    </Card>
  );

  const renderContent = () => (
    <Card
      title={t('fields.content')}
      className="content-card"
      extra={
        <Space>
          <Tooltip title="Chia sẻ">
            <Button icon={<ShareAltOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Tải xuống">
            <Button icon={<DownloadOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="In">
            <Button icon={<PrinterOutlined />} size="small" />
          </Tooltip>
        </Space>
      }
    >
      <div className="content-preview">
        {item?.content ? (
          <div
            className="content-html"
            dangerouslySetInnerHTML={{ __html: item.content }}
          />
        ) : (
          <Text type="secondary">{t('messages.no_content')}</Text>
        )}
      </div>
    </Card>
  );

  if (loading) {
    return (
      <div className="blog-ai-detail loading">
        <Spin size="large" />
      </div>
    );
  }

  if (!item) {
    return (
      <div className="blog-ai-detail error">
        <Text type="secondary">Không tìm thấy dữ liệu</Text>
      </div>
    );
  }

  return (
    <div className="blog-ai-detail">
      {renderHeader()}

      <div className="detail-content">
        <Row gutter={[24, 24]}>
          <Col span={24}>{renderMetadata()}</Col>
          <Col span={12}>{renderAnalytics()}</Col>
          <Col span={12}>{renderSEOScore()}</Col>
          <Col span={24}>{renderPrompt()}</Col>
          <Col span={24}>{renderContent()}</Col>
        </Row>
      </div>

      {/* Modals */}
      <FormModal
        visible={formModalVisible}
        mode="edit"
        itemId={item.id}
        onCancel={() => setFormModalVisible(false)}
        onSuccess={handleFormSuccess}
      />

      {/* GenerationModal removed - now using dedicated generation page */}
    </div>
  );
}
