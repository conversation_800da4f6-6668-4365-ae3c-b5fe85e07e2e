import { Card, Col, Form, Input, Row, Spin, message } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { Editor } from '../../../components/editor';
import { FormHeader } from '../../../components/form-header';
import { getItem, updateItem } from '../api';
import { BlogAI } from '../type';

const FormItem = Form.Item;

export default function BlogAIEdit() {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [blogData, setBlogData] = useState<BlogAI | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Load blog data
  useEffect(() => {
    const loadBlogData = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const response = await getItem(id);
        if (response.status.success && response.data) {
          setBlogData(response.data);
          form.setFieldsValue({
            title: response.data.title || '',
            content: response.data.content || '',
          });
        } else {
          console.error('Failed to load blog data:', response.status.message);
        }
      } catch (error) {
        console.error('Error loading blog data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadBlogData();
  }, [id, form]);

  // Handle form values change
  const handleFormChange = useCallback(() => {
    setHasChanges(true);
  }, []);

  // Save blog
  const handleSave = useCallback(
    async (values: any) => {
      if (!id) return;

      try {
        setSaving(true);
        const response = await updateItem(id, values);

        if (response.status.success) {
          setHasChanges(false);
          message.success('Blog saved successfully');
          // Update local blog data with the response
          if (response.data) {
            setBlogData(response.data);
          }
        } else {
          message.error(response.status.message || 'Failed to save blog');
        }
      } catch (error) {
        console.error('Error saving blog:', error);
        message.error('Error saving blog');
      } finally {
        setSaving(false);
      }
    },
    [id],
  );

  // Handle back navigation
  const handleBack = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(t('common.unsavedChanges'));
      if (!confirmed) return;
    }
    navigate(`/blog-ai/${id}`);
  }, [navigate, id, hasChanges, t]);

  if (loading) {
    return (
      <Spin
        spinning={true}
        style={{
          minHeight: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />
    );
  }

  if (!blogData) {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
        }}
      >
        <p>{t('common.notFound')}</p>
      </div>
    );
  }

  return (
    <Row gutter={24}>
      <Col xs={24} sm={24} md={24} lg={24} xl={24}>
        <div className="blog-form-container">
          <FormHeader
            title={t('blogAI.edit.title')}
            backDestination="/blog-ai"
          />
          <Spin spinning={loading || saving}>
            <Form
              style={{ marginTop: 8 }}
              form={form}
              name="blogAIForm"
              layout="vertical"
              onFinish={handleSave}
              autoComplete="off"
              onValuesChange={handleFormChange}
              className="form-blog-detail min-h-screen"
            >
              <Row gutter={16}>
                <Col xs={24} lg={24}>
                  <Card size="small" title="Thông tin chung">
                    <FormItem
                      label={t('blogAI.form.title')}
                      name="title"
                      rules={[
                        { required: true, message: t('pleaseEnterData') },
                      ]}
                    >
                      <Input placeholder={t('blogAI.form.titlePlaceholder')} />
                    </FormItem>
                  </Card>

                  <Card size="small" title="Nội dung">
                    <FormItem
                      label=""
                      name="content"
                      rules={[
                        { required: false, message: t('pleaseEnterData') },
                      ]}
                    >
                      {!loading && (
                        <Editor
                          placeholder={t('blogAI.form.contentPlaceholder')}
                          theme="light"
                        />
                      )}
                    </FormItem>
                  </Card>

                  {/* Metadata Section */}
                  <Card size="small" title="Thông tin bổ sung">
                    <div
                      style={{
                        display: 'grid',
                        gridTemplateColumns:
                          'repeat(auto-fit, minmax(200px, 1fr))',
                        gap: '16px',
                        fontSize: '14px',
                        color: '#666',
                      }}
                    >
                      <div>
                        <span style={{ fontWeight: 500 }}>
                          {t('common.status')}:
                        </span>
                        <span
                          style={{
                            marginLeft: '8px',
                            padding: '2px 8px',
                            backgroundColor: '#e6f7ff',
                            color: '#1890ff',
                            borderRadius: '12px',
                            fontSize: '12px',
                          }}
                        >
                          {blogData.status}
                        </span>
                      </div>
                      <div>
                        <span style={{ fontWeight: 500 }}>
                          {t('common.createdAt')}:
                        </span>
                        <span style={{ marginLeft: '8px' }}>
                          {new Date(
                            blogData.created_at || '',
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div>
                        <span style={{ fontWeight: 500 }}>
                          {t('common.updatedAt')}:
                        </span>
                        <span style={{ marginLeft: '8px' }}>
                          {new Date(
                            blogData.updated_at || '',
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>

              {/* Save Bar */}
              {hasChanges && (
                <div
                  style={{
                    position: 'fixed',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    backgroundColor: 'white',
                    borderTop: '1px solid #f0f0f0',
                    padding: '12px 16px',
                    display: 'flex',
                    justifyContent: 'flex-end',
                    gap: '12px',
                    zIndex: 1000,
                  }}
                >
                  <button
                    type="button"
                    onClick={handleBack}
                    style={{
                      padding: '8px 16px',
                      color: '#666',
                      backgroundColor: 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                    }}
                    disabled={saving}
                  >
                    {t('common.cancel')}
                  </button>
                  <button
                    type="submit"
                    disabled={saving}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#1890ff',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      opacity: saving ? 0.5 : 1,
                    }}
                  >
                    {saving ? t('common.saving') : t('common.save')}
                  </button>
                </div>
              )}
            </Form>
          </Spin>
        </div>
      </Col>
    </Row>
  );
}
