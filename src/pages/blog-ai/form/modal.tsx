import React from 'react';
import { Modal } from 'antd';
import { FormModal } from '../components';
import { useBlogAIStore } from '../store';
import { BlogAI } from '../type';

interface ModalFormProps {
  visible: boolean;
  mode: 'create' | 'edit' | 'view';
  data?: BlogAI | null;
  onCancel: () => void;
  onSuccess: (data: BlogAI) => void;
}

const ModalForm: React.FC<ModalFormProps> = ({
  visible,
  mode,
  data,
  onCancel,
  onSuccess,
}) => {
  const { setFormVisible, setFormMode, setFormData } = useBlogAIStore();

  const handleCancel = () => {
    setFormVisible(false);
    onCancel();
  };

  const handleSuccess = (result: BlogAI) => {
    setFormVisible(false);
    onSuccess(result);
  };

  return (
    <FormModal
      visible={visible}
      mode={mode}
      data={data}
      onCancel={handleCancel}
      onSuccess={handleSuccess}
    />
  );
};

export default ModalForm;
