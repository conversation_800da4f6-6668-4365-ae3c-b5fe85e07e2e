import React, { useEffect, useCallback } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Card,
  Button,
  Space,
  message,
  Typography,
  Spin,
  Breadcrumb,
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useState, useEffect, useCallback } from 'react';
import ConsoleService from '../../../services/console.service';
import { getItem } from '../api';
import { BlogAI } from '../type';
import { useBlogAIStore } from '../store';
import { FormModal } from '../components';
import { blogAITranslations } from '../i18n';
import '../assets/styles.scss';

const { Title } = Typography;

export default function BlogAIForm() {
  const { t } = useTranslation('blog-ai');
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const mode = searchParams.get('mode') || 'create';
  const logger = ConsoleService.register('blog-ai-form');

  const {
    data: { loading },
    setDataLoading,
  } = useBlogAIStore();

  const [item, setItem] = useState<BlogAI | null>(null);
  const [formModalVisible, setFormModalVisible] = useState(true);
  // Removed generationModalVisible state - now using navigation

  const isEdit = mode === 'edit' && id;
  const isView = mode === 'view' && id;
  const isCreate = mode === 'create';

  // Load item data for edit/view mode
  useEffect(() => {
    const loadItem = async () => {
      if (!id || isCreate) return;

      try {
        setDataLoading(true);
        const response = await getItem(id);
        if (response.status === 'success' && response.data?.post) {
          setItem(response.data.post);
        } else {
          message.error(t('messages.error.load_failed'));
          navigate('/blog-ai');
        }
      } catch (error: any) {
        logger('Error loading item:', error);
        message.error(error.message || t('messages.error.load_failed'));
        navigate('/blog-ai');
      } finally {
        setDataLoading(false);
      }
    };

    loadItem();
  }, [id, isCreate, navigate, setDataLoading, logger]);

  const handleFormSuccess = useCallback(
    (result: BlogAI) => {
      setFormModalVisible(false);
      if (isCreate) {
        message.success(t('messages.success.created'));
        navigate(`/blog-ai/${result.id}`);
      } else {
        message.success(t('messages.success.updated'));
        navigate(`/blog-ai/${result.id}`);
      }
    },
    [isCreate, navigate],
  );

  const handleFormCancel = useCallback(() => {
    setFormModalVisible(false);
    navigate('/blog-ai');
  }, [navigate]);

  const handleGenerateContent = useCallback(() => {
    navigate('/blog-ai/generation');
  }, []);

  const handleGenerationSuccess = useCallback(
    (result: any) => {
      // Navigation handled by route change
      message.success(t('messages.success.generated'));
      // Navigate to the created item
      if (result.id) {
        navigate(`/blog-ai/${result.id}`);
      }
    },
    [navigate],
  );

  const handleGenerationCancel = useCallback(() => {
    // Navigation handled by route change
  }, []);

  const getPageTitle = () => {
    if (isCreate) return t('actions.create_blog_ai');
    if (isEdit) return t('actions.edit_blog_ai');
    if (isView) return t('actions.view_blog_ai');
    return t('title');
  };

  const getBreadcrumbItems = () => {
    const items = [
      {
        title: (
          <Button
            type="link"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/blog-ai')}
          >
            {t('title')}
          </Button>
        ),
      },
    ];

    if (isCreate) {
      items.push({ title: t('actions.create') });
    } else if (isEdit) {
      items.push({ title: t('actions.edit') });
    } else if (isView) {
      items.push({ title: t('actions.view') });
    }

    return items;
  };

  const renderHeader = () => (
    <div className="form-header">
      <Breadcrumb items={getBreadcrumbItems()} className="breadcrumb" />

      <div className="header-content">
        <div className="title-section">
          <Title level={2} className="title">
            {getPageTitle()}
          </Title>
        </div>

        <Space className="actions">
          {isCreate && (
            <Button
              type="primary"
              icon={<RobotOutlined />}
              onClick={handleGenerateContent}
            >
              {t('actions.generate_content')}
            </Button>
          )}
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/blog-ai')}
          >
            {t('common.back')}
          </Button>
        </Space>
      </div>
    </div>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <div className="form-loading">
          <Spin size="large" />
        </div>
      );
    }

    return (
      <Card className="form-card">
        <div className="form-content">
          {isCreate ? (
            <div className="create-options">
              <div
                className="option-card"
                onClick={() => setFormModalVisible(true)}
              >
                <SaveOutlined className="option-icon" />
                <div className="option-title">{t('actions.create_manual')}</div>
                <div className="option-description">
                  {t('messages.create_manual_description')}
                </div>
              </div>

              <div className="option-card" onClick={handleGenerateContent}>
                <RobotOutlined className="option-icon" />
                <div className="option-title">
                  {t('actions.create_with_ai')}
                </div>
                <div className="option-description">
                  {t('messages.create_ai_description')}
                </div>
              </div>
            </div>
          ) : (
            <div className="edit-view-content">
              <p>{t('common.loading')}...</p>
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <div className="blog-ai-form">
      {renderHeader()}

      <div className="form-body">{renderContent()}</div>

      {/* Modals */}
      <FormModal
        visible={formModalVisible}
        mode={isView ? 'view' : isEdit ? 'edit' : 'create'}
        itemId={id}
        onCancel={handleFormCancel}
        onSuccess={handleFormSuccess}
      />

      {/* GenerationModal removed - now using dedicated generation page */}
    </div>
  );
}
