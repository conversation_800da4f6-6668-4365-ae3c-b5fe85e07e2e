// Blog AI Store
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useShallow } from 'zustand/shallow';
import ConsoleService from '../../services/console.service';
import { deleteItem, getOptions } from './api';
import { MODULE } from './config';
import {
  BlogAI,
  BlogAIDrawerData,
  extractDrawerFields,
  GenerationRequest,
} from './type';

const logger = ConsoleService.register(MODULE);

export interface StoreState {
  // UI State - tách riêng để tránh re-render không cần thiết
  ui: {
    loading: boolean;
    drawerVisible: boolean;
    generationVisible: boolean;
    previewVisible: boolean;
    generating: boolean;
  };

  // Data State - quản lý dữ liệu chính
  data: {
    items: BlogAI[];
    currentBlogAI?: BlogAI;
    options: any[];
    availableModels: any[];
    promptTemplates: any[];
  };

  // Form State - tách riêng để tối ưu performance
  form: {
    formValues?: BlogAI;
    drawerData?: BlogAIDrawerData;
    generationRequest?: GenerationRequest;
    selectedTemplate?: any;
    submitting?: boolean;
    loading?: boolean;
    visible?: boolean;
    mode?: 'create' | 'edit' | 'view';
  };

  // Generation State
  generation: {
    currentGenerationId?: string;
    progress: number;
    status: string;
    estimatedTime?: number;
    errorMessage?: string;
  };

  // Batch update actions
  setUIState: (state: Partial<StoreState['ui']>) => void;
  setDataState: (state: Partial<StoreState['data']>) => void;
  setFormState: (state: Partial<StoreState['form']>) => void;
  setGenerationState: (state: Partial<StoreState['generation']>) => void;

  // Legacy compatibility methods
  setLoading: (loading: boolean) => void;
  delete: (id: string) => void;
  setOptions: (options: any) => void;
  deleteItem: (id: string) => Promise<void>;
  getOptions: () => Promise<void>;
  updateDrawerData: (data: Partial<BlogAI>) => void;

  // Form state methods
  setFormData: (data: BlogAI | null) => void;
  setFormLoading: (loading: boolean) => void;
  setFormSubmitting: (submitting: boolean) => void;
  setFormVisible: (visible: boolean) => void;
  setFormMode: (mode: 'create' | 'edit' | 'view') => void;

  // Optimized specific actions
  updateCurrentBlogAI: (blogAI: Partial<BlogAI>) => void;
  updateFormValues: (values: Partial<BlogAI>) => void;
  updateGenerationRequest: (request: Partial<GenerationRequest>) => void;

  // Generation actions
  startGeneration: (request: GenerationRequest) => void;
  updateGenerationProgress: (progress: number, status: string) => void;
  completeGeneration: (result: BlogAI) => void;
  failGeneration: (error: string) => void;
  cancelGeneration: () => void;

  // Template actions
  selectTemplate: (template: any) => void;
  applyTemplate: (template: any) => void;

  // Memoized selectors
  getUIState: () => StoreState['ui'];
  getDataState: () => StoreState['data'];
  getFormState: () => StoreState['form'];
  getGenerationState: () => StoreState['generation'];
}

const useBlogAIStore = create<StoreState>()(
  devtools(
    (set, get) => ({
      // Initial state
      ui: {
        loading: false,
        drawerVisible: false,
        generationVisible: false,
        previewVisible: false,
        generating: false,
      },
      data: {
        items: [],
        currentBlogAI: undefined,
        options: [],
        availableModels: [],
        promptTemplates: [],
      },
      form: {
        formValues: undefined,
        drawerData: undefined,
        generationRequest: undefined,
        selectedTemplate: undefined,
        submitting: false,
        loading: false,
        visible: false,
        mode: 'create',
      },
      generation: {
        currentGenerationId: undefined,
        progress: 0,
        status: '',
        estimatedTime: undefined,
        errorMessage: undefined,
      },

      // Batch update actions
      setUIState: (state) => {
        set(
          (prev) => ({
            ui: { ...prev.ui, ...state },
          }),
          false,
          'setUIState',
        );
      },

      setDataState: (state) => {
        set(
          (prev) => ({
            data: { ...prev.data, ...state },
          }),
          false,
          'setDataState',
        );
      },

      setFormState: (state) => {
        set(
          (prev) => ({
            form: { ...prev.form, ...state },
          }),
          false,
          'setFormState',
        );
      },

      setGenerationState: (state) => {
        set(
          (prev) => ({
            generation: { ...prev.generation, ...state },
          }),
          false,
          'setGenerationState',
        );
      },

      // Legacy compatibility methods
      setLoading: (loading) => {
        get().setUIState({ loading });
      },

      delete: (id) => {
        const { data } = get();
        const updatedItems = data.items.filter((item) => item.id !== id);
        get().setDataState({ items: updatedItems });
      },

      setOptions: (options) => {
        get().setDataState({ options });
      },

      deleteItem: async (id) => {
        try {
          await deleteItem(id);
          get().delete(id);
          logger('Item deleted successfully', id);
        } catch (error) {
          logger('Error deleting item', error);
          throw error;
        }
      },

      getOptions: async () => {
        try {
          const response = await getOptions();
          get().setOptions(response.data);
        } catch (error) {
          logger('Error fetching options', error);
        }
      },

      updateDrawerData: (data) => {
        const { form } = get();
        const updatedData = { ...form.drawerData, ...data };
        get().setFormState({ drawerData: updatedData });
      },

      // Optimized specific actions
      updateCurrentBlogAI: (blogAI) => {
        const { data } = get();
        const updatedBlogAI = { ...data.currentBlogAI, ...blogAI };
        get().setDataState({ currentBlogAI: updatedBlogAI });
      },

      updateFormValues: (values) => {
        const { form } = get();
        const updatedValues = { ...form.formValues, ...values };
        get().setFormState({ formValues: updatedValues });
      },

      updateGenerationRequest: (request) => {
        const { form } = get();
        const updatedRequest = { ...form.generationRequest, ...request };
        get().setFormState({ generationRequest: updatedRequest });
      },

      // Generation actions
      startGeneration: (request) => {
        get().setFormState({ generationRequest: request });
        get().setUIState({ generating: true });
        get().setGenerationState({
          progress: 0,
          status: 'Đang khởi tạo...',
          errorMessage: undefined,
        });
      },

      updateGenerationProgress: (progress, status) => {
        get().setGenerationState({ progress, status });
      },

      completeGeneration: (result) => {
        get().setUIState({ generating: false });
        get().setDataState({ currentBlogAI: result });
        get().setGenerationState({
          progress: 100,
          status: 'Hoàn thành',
          currentGenerationId: undefined,
        });
      },

      failGeneration: (error) => {
        get().setUIState({ generating: false });
        get().setGenerationState({
          progress: 0,
          status: 'Thất bại',
          errorMessage: error,
          currentGenerationId: undefined,
        });
      },

      cancelGeneration: () => {
        get().setUIState({ generating: false });
        get().setGenerationState({
          progress: 0,
          status: 'Đã hủy',
          currentGenerationId: undefined,
        });
      },

      // Template actions
      selectTemplate: (template) => {
        get().setFormState({ selectedTemplate: template });
      },

      applyTemplate: (template) => {
        const { form } = get();
        const updatedRequest = {
          ...form.generationRequest,
          prompt: template.prompt,
          ...template.settings,
        };
        get().setFormState({
          generationRequest: updatedRequest,
          selectedTemplate: template,
        });
      },

      // Form state methods
      setFormData: (data) => {
        get().setFormState({ formValues: data });
      },

      setFormLoading: (loading) => {
        get().setUIState({ loading });
      },

      setFormSubmitting: (submitting) => {
        get().setFormState({ submitting });
      },

      setFormVisible: (visible) => {
        get().setUIState({ drawerVisible: visible });
      },

      setFormMode: (mode) => {
        get().setUIState({ drawerMode: mode });
      },

      // Memoized selectors
      getUIState: () => get().ui,
      getDataState: () => get().data,
      getFormState: () => get().form,
      getGenerationState: () => get().generation,
    }),
    {
      name: 'blog-ai-store',
    },
  ),
);

// Selector hooks for optimized re-renders
export const useBlogAIUIState = () =>
  useBlogAIStore(useShallow((state) => state.ui));

export const useBlogAIDataState = () =>
  useBlogAIStore(useShallow((state) => state.data));

export const useBlogAIFormState = () =>
  useBlogAIStore(useShallow((state) => state.form));

export const useBlogAIGenerationState = () =>
  useBlogAIStore(useShallow((state) => state.generation));

// Export the main store hook
export { useBlogAIStore };
export default useBlogAIStore;
