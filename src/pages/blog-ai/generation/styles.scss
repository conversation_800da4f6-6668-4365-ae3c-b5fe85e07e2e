.blog-ai-generation-container {
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .header-content {
      flex: 1;

      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      .page-subtitle {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.blog-ai-generation-page {
  .generation-steps {
    margin-bottom: 32px;

    .ant-steps-item-title {
      font-weight: 600;
    }

    .ant-steps-item-description {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .step-content {
    margin-top: 32px;

    .step-card {
      margin-bottom: 24px;

      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
      }

      .ant-form-item {
        margin-bottom: 24px;
      }

      .ant-radio-button-wrapper {
        height: auto;
        padding: 12px 16px;
        border-radius: 8px;
        margin-right: 12px;
        margin-bottom: 12px;

        &:hover {
          border-color: #1890ff;
        }

        &.ant-radio-button-wrapper-checked {
          background: #e6f7ff;
          border-color: #1890ff;

          &:hover {
            border-color: #40a9ff;
          }
        }
      }
    }
  }

  .step-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
    text-align: right;

    .ant-btn {
      margin-left: 8px;
    }
  }

  .review-section {
    .ant-typography h4 {
      margin-bottom: 16px;
      color: #262626;
    }

    .ant-row {
      margin-bottom: 8px;

      .ant-col {
        padding: 8px 0;
      }
    }
  }

  .generation-progress {
    margin: 24px 0;
    text-align: center;

    .ant-progress {
      margin-bottom: 16px;
    }

    .ant-typography {
      color: #8c8c8c;
    }
  }

  .generation-result {
    margin-top: 24px;

    .ant-typography h4 {
      margin-bottom: 16px;
      color: #262626;
    }

    .ant-card {
      background: #fafafa;
      border: 1px solid #e8e8e8;

      .ant-typography {
        margin-bottom: 0;
        line-height: 1.6;
        white-space: pre-wrap;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .generation-steps {
      .ant-steps-item-description {
        display: none;
      }
    }

    .step-content {
      .ant-radio-button-wrapper {
        width: 100%;
        margin-right: 0;
        text-align: center;
      }
    }

    .step-actions {
      text-align: center;

      .ant-btn {
        margin: 4px;
      }
    }
  }
}

// Animation for step transitions
.step-content {
  .step-card {
    animation: fadeInUp 0.3s ease-in-out;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom slider styles
.ant-slider {
  .ant-slider-mark-text {
    font-size: 12px;
    color: #8c8c8c;
  }

  .ant-slider-handle {
    border-color: #1890ff;

    &:hover {
      border-color: #40a9ff;
    }

    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
    }
  }

  .ant-slider-track {
    background-color: #1890ff;
  }
}

// Custom progress styles
.ant-progress {
  .ant-progress-bg {
    transition: all 0.3s ease;
  }
}
