import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Steps,
  Card,
  Button,
  Form,
  Input,
  Select,
  Slider,
  Radio,
  Space,
  Typography,
  Progress,
  Alert,
  Spin,
  message,
  Row,
  Col,
  Divider,
} from 'antd';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined,
  RobotOutlined,
  SettingOutlined,
  FileTextOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { useBlogAIStore } from '../store';
import {
  GenerationRequest,
  GenerationResponse,
  BLOG_AI_STATUS,
  AI_MODEL_TYPE,
  AI_MODEL_LABELS,
} from '../type';
import {
  DEFAULT_GENERATION_SETTINGS,
  LANGUAGE_OPTIONS,
  TONE_OPTIONS,
  WORD_COUNT_OPTIONS,
  POLLING_INTERVAL,
  GENERATION_TIMEOUT,
  AI_MODELS,
} from '../config';
import { generateBlog, getGenerationStatus } from '../api';
import '../assets/styles.scss';
import './styles.scss';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;
const { TextArea } = Input;
const { Option } = Select;

interface GenerationFormData {
  title: string;
  description: string;
  keywords: string[];
  model: AI_MODEL_TYPE;
  language: string;
  tone: string;
  wordCount: string;
  temperature: number;
  maxTokens: number;
  frequencyPenalty: number;
  presencePenalty: number;
}

const logger = ConsoleService.register('blog-ai-generation');

export default function BlogAIGeneration() {
  const { t } = useTranslation('blog-ai');
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm<GenerationFormData>();

  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Partial<GenerationFormData>>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationResult, setGenerationResult] = useState<string>('');
  const [generationError, setGenerationError] = useState<string>('');

  const blogAIStore = useBlogAIStore();

  // Steps configuration
  const steps = [
    {
      title: t('generation.steps.basicInfo'),
      icon: <FileTextOutlined />,
      description: t('generation.steps.basicInfoDesc'),
    },
    {
      title: t('generation.steps.aiSettings'),
      icon: <RobotOutlined />,
      description: t('generation.steps.aiSettingsDesc'),
    },
    {
      title: t('generation.steps.advanced'),
      icon: <SettingOutlined />,
      description: t('generation.steps.advancedDesc'),
    },
    {
      title: t('generation.steps.review'),
      icon: <EyeOutlined />,
      description: t('generation.steps.reviewDesc'),
    },
  ];

  // Initialize form with default values
  useEffect(() => {
    form.setFieldsValue({
      ...DEFAULT_GENERATION_SETTINGS,
      keywords: [],
    });
  }, [form]);

  // Handle step navigation
  const handleNext = async () => {
    try {
      const values = await form.validateFields();
      setFormData({ ...formData, ...values });
      setCurrentStep(currentStep + 1);
    } catch (error) {
      logger.error('Form validation failed:', error);
    }
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  // Generation logic
  const handleGenerate = async () => {
    try {
      setIsGenerating(true);
      setGenerationProgress(0);
      setGenerationError('');

      const values = await form.validateFields();
      const finalFormData = { ...formData, ...values };

      const request: GenerationRequest = {
        topic: finalFormData.title!,
        description: finalFormData.description!,
        keywords: finalFormData.keywords || [],
        model: finalFormData.model!,
        language: finalFormData.language!,
        tone: finalFormData.tone!,
        wordCount: finalFormData.wordCount!,
        temperature: finalFormData.temperature!,
        maxTokens: finalFormData.maxTokens!,
        frequencyPenalty: finalFormData.frequencyPenalty!,
        presencePenalty: finalFormData.presencePenalty!,
      };

      console.log('Starting content generation:', request);

      const response = await generateBlog(request);

      if (response && response.data) {
        await pollGenerationStatus(response.data.id);
      } else {
        throw new Error('Generation failed');
      }
    } catch (error) {
      console.error('Generation error:', error);
      setGenerationError(
        error instanceof Error ? error.message : 'Unknown error',
      );
      message.error(t('generation.error.failed'));
    } finally {
      setIsGenerating(false);
    }
  };

  // Poll generation status
  const pollGenerationStatus = async (taskId: string) => {
    const startTime = Date.now();

    const poll = async (): Promise<void> => {
      try {
        if (Date.now() - startTime > GENERATION_TIMEOUT) {
          throw new Error('Generation timeout');
        }

        const response = await getGenerationStatus(taskId);

        if (response && response.data) {
          const progress = response.data?.status;
          const result = response.data?.content;

          setGenerationProgress(progress || 0);

          if (response.data?.status === 'completed') {
            setGenerationResult(result || '');
            message.success(t('generation.success'));
            return;
          }

          if (status === BLOG_AI_STATUS.FAILED) {
            throw new Error(error || 'Generation failed');
          }

          // Continue polling
          setTimeout(poll, POLLING_INTERVAL);
        } else {
          throw new Error('Status check failed');
        }
      } catch (error) {
        console.error('Polling error:', error);
        throw error;
      }
    };

    await poll();
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card title={t('generation.steps.basicInfo')} className="step-card">
            <Form.Item
              name="title"
              label={t('generation.form.title')}
              rules={[
                { required: true, message: t('generation.form.titleRequired') },
              ]}
            >
              <Input placeholder={t('generation.form.titlePlaceholder')} />
            </Form.Item>

            <Form.Item
              name="description"
              label={t('generation.form.description')}
              rules={[
                {
                  required: true,
                  message: t('generation.form.descriptionRequired'),
                },
              ]}
            >
              <TextArea
                rows={4}
                placeholder={t('generation.form.descriptionPlaceholder')}
              />
            </Form.Item>

            <Form.Item name="keywords" label={t('generation.form.keywords')}>
              <Select
                mode="tags"
                placeholder={t('generation.form.keywordsPlaceholder')}
                tokenSeparators={[',', ' ']}
              />
            </Form.Item>
          </Card>
        );

      case 1:
        return (
          <Card title={t('generation.steps.aiSettings')} className="step-card">
            <Form.Item
              name="model"
              label={t('generation.form.model')}
              rules={[
                { required: true, message: t('generation.form.modelRequired') },
              ]}
            >
              <Radio.Group>
                {AI_MODELS.map((model) => (
                  <Radio.Button key={model.id} value={model.id}>
                    <Space>
                      <RobotOutlined />
                      <div>
                        <div>{model.name}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {model.description}
                        </Text>
                      </div>
                    </Space>
                  </Radio.Button>
                ))}
              </Radio.Group>
            </Form.Item>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="language"
                  label={t('generation.form.language')}
                  rules={[{ required: true }]}
                >
                  <Select
                    placeholder={t('generation.form.languagePlaceholder')}
                  >
                    {LANGUAGE_OPTIONS.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="tone"
                  label={t('generation.form.tone')}
                  rules={[{ required: true }]}
                >
                  <Select placeholder={t('generation.form.tonePlaceholder')}>
                    {TONE_OPTIONS.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  name="wordCount"
                  label={t('generation.form.wordCount')}
                  rules={[{ required: true }]}
                >
                  <Select
                    placeholder={t('generation.form.wordCountPlaceholder')}
                  >
                    {WORD_COUNT_OPTIONS.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case 2:
        return (
          <Card title={t('generation.steps.advanced')} className="step-card">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="temperature"
                  label={t('generation.form.temperature')}
                >
                  <Slider
                    min={0}
                    max={2}
                    step={0.1}
                    marks={{
                      0: t('generation.form.conservative'),
                      1: t('generation.form.balanced'),
                      2: t('generation.form.creative'),
                    }}
                    defaultValue={DEFAULT_GENERATION_SETTINGS.temperature}
                  />
                </Form.Item>

                <Form.Item
                  name="maxTokens"
                  label={t('generation.form.maxTokens')}
                >
                  <Slider
                    min={100}
                    max={4000}
                    step={100}
                    defaultValue={DEFAULT_GENERATION_SETTINGS.maxTokens}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="frequencyPenalty"
                  label={t('generation.form.frequencyPenalty')}
                >
                  <Slider
                    min={0}
                    max={2}
                    step={0.1}
                    defaultValue={DEFAULT_GENERATION_SETTINGS.frequencyPenalty}
                  />
                </Form.Item>

                <Form.Item
                  name="presencePenalty"
                  label={t('generation.form.presencePenalty')}
                >
                  <Slider
                    min={0}
                    max={2}
                    step={0.1}
                    defaultValue={DEFAULT_GENERATION_SETTINGS.presencePenalty}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        );

      case 3:
        return (
          <Card title={t('generation.steps.review')} className="step-card">
            <div className="review-section">
              <Title level={4}>{t('generation.review.settings')}</Title>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>{t('generation.form.title')}: </Text>
                  <Text>{formData.title}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>{t('generation.form.model')}: </Text>
                  <Text>
                    {AI_MODEL_LABELS[formData.model as AI_MODEL_TYPE]}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text strong>{t('generation.form.language')}: </Text>
                  <Text>{formData.language}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>{t('generation.form.tone')}: </Text>
                  <Text>{formData.tone}</Text>
                </Col>
              </Row>

              <Divider />

              {isGenerating && (
                <div className="generation-progress">
                  <Progress
                    percent={generationProgress}
                    status={generationError ? 'exception' : 'active'}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                  <Text type="secondary">
                    {t('generation.progress.generating')}
                  </Text>
                </div>
              )}

              {generationError && (
                <Alert
                  message={t('generation.error.title')}
                  description={generationError}
                  type="error"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}

              {generationResult && (
                <div className="generation-result">
                  <Title level={4}>{t('generation.result.title')}</Title>
                  <Card>
                    <Paragraph>{generationResult}</Paragraph>
                  </Card>
                </div>
              )}
            </div>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="blog-ai-generation-container">
      <div className="page-header">
        <div className="header-content">
          <h1 className="page-title">{t('generation.title')}</h1>
        </div>
        <div className="header-actions">
          <Button
            key="back"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/blog-ai')}
          >
            {t('common.back')}
          </Button>
        </div>
      </div>
      <div className="blog-ai-generation-page">
        <Card>
          <Steps current={currentStep} className="generation-steps">
            {steps.map((step, index) => (
              <Step
                key={index}
                title={step.title}
                description={step.description}
                icon={step.icon}
              />
            ))}
          </Steps>

          <div className="step-content">
            <Form
              form={form}
              layout="vertical"
              onFinish={
                currentStep === steps.length - 1 ? handleGenerate : handleNext
              }
            >
              {renderStepContent()}

              <div className="step-actions">
                <Space>
                  {currentStep > 0 && (
                    <Button onClick={handlePrev}>
                      <ArrowLeftOutlined /> {t('common.previous')}
                    </Button>
                  )}

                  {currentStep < steps.length - 1 && (
                    <Button type="primary" onClick={handleNext}>
                      {t('common.next')} <ArrowRightOutlined />
                    </Button>
                  )}

                  {currentStep === steps.length - 1 && (
                    <Button
                      type="primary"
                      loading={isGenerating}
                      onClick={handleGenerate}
                      disabled={!!generationResult}
                    >
                      <RobotOutlined />
                      {isGenerating
                        ? t('generation.generating')
                        : t('generation.generate')}
                    </Button>
                  )}

                  {generationResult && (
                    <Button
                      type="default"
                      icon={<CheckOutlined />}
                      onClick={() => {
                        message.success(t('generation.saved'));
                        navigate('/blog-ai');
                      }}
                    >
                      {t('generation.save')}
                    </Button>
                  )}
                </Space>
              </div>
            </Form>
          </div>
        </Card>
      </div>
    </div>
  );
}
