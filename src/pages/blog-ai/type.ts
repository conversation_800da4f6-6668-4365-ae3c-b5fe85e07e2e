// Blog AI Types
import { BlogCategory } from '../blog-category/type';
import { SeoMeta } from '../seo-meta/type';

export enum BLOG_AI_STATUS {
  DRAFT = 'draft',
  GENERATING = 'generating',
  GENERATED = 'generated',
  REVIEWING = 'reviewing',
  APPROVED = 'approved',
  PUBLISHED = 'published',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export const BLOG_AI_STATUS_LABELS = {
  [BLOG_AI_STATUS.DRAFT]: 'Bản nháp',
  [BLOG_AI_STATUS.GENERATING]: 'Đang tạo',
  [BLOG_AI_STATUS.GENERATED]: 'Đã tạo',
  [BLOG_AI_STATUS.REVIEWING]: 'Đang xem xét',
  [BLOG_AI_STATUS.APPROVED]: 'Đã duyệt',
  [BLOG_AI_STATUS.PUBLISHED]: 'Đã xuất bản',
  [BLOG_AI_STATUS.FAILED]: 'Thất bại',
  [BLOG_AI_STATUS.CANCELLED]: 'Đã hủy',
};

export enum AI_MODEL_TYPE {
  GPT4 = 'gpt-4',
  GPT35 = 'gpt-3.5-turbo',
  CLAUDE = 'claude-3',
  GEMINI = 'gemini-pro',
}

export const AI_MODEL_LABELS = {
  [AI_MODEL_TYPE.GPT4]: 'GPT-4',
  [AI_MODEL_TYPE.GPT35]: 'GPT-3.5 Turbo',
  [AI_MODEL_TYPE.CLAUDE]: 'Claude 3',
  [AI_MODEL_TYPE.GEMINI]: 'Gemini Pro',
};

export interface User {
  id?: number;
  name?: string;
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  avatar?: string;
  avatar_url?: string;
}

export interface AIMetadata {
  provider?: string;
  model?: string;
  prompt_used?: string;
  generated_at?: string;
  tokens_used?: number;
  confidence_score?: number;
  generation_time?: number;
  iterations?: number;
  human_edited?: boolean;
}

export interface Analytics {
  views?: number;
  shares?: number;
  likes?: number;
  comments_count?: number;
  engagement_rate?: number;
}

export interface SEOScore {
  overall?: number;
  readability?: number;
  keyword_density?: number;
  meta_optimization?: number;
}

export interface EditHistory {
  id?: number;
  action?: string;
  by?: string;
  at?: string;
  changes?: string;
}

export interface Tag {
  id?: number;
  name?: string;
  slug?: string;
}

export interface BlogAI {
  id?: string;
  title?: string;
  content?: string;
  excerpt?: string;
  slug?: string;
  status?: BLOG_AI_STATUS;
  meta_title?: string;
  meta_description?: string;
  featured_image?: string;
  ai_metadata?: AIMetadata;
  author?: User;
  category?: BlogCategory;
  tags?: Tag[];
  analytics?: Analytics;
  seo_score?: SEOScore;
  edit_history?: EditHistory[];
  published_at?: string;
  created_at?: string;
  updated_at?: string;
  // Legacy fields for backward compatibility
  ai_model?: AI_MODEL_TYPE;
  prompt?: string;
  keywords?: string[];
  target_audience?: string;
  tone?: string;
  word_count?: number;
  language?: string;
  category_id?: string;
  seo_meta?: SeoMeta;
  author_id?: string;
  generated_at?: string;
  reviewed_at?: string;
  generation_time?: number;
  quality_score?: number;
  ai_confidence?: number;
  revision_count?: number;
  original_prompt?: string;
  generation_settings?: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    frequency_penalty?: number;
    presence_penalty?: number;
  };
  estimated_reading_time?: number;
}

export interface BlogAIDrawerData {
  title?: string;
  prompt?: string;
  ai_model?: AI_MODEL_TYPE;
  keywords?: string[];
  target_audience?: string;
  tone?: string;
  word_count?: number;
  language?: string;
  category_id?: string;
  tags?: string[];
  generation_settings?: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    frequency_penalty?: number;
    presence_penalty?: number;
  };
}

export function extractDrawerFields(blog: BlogAI): BlogAIDrawerData {
  return {
    title: blog.title,
    prompt: blog.prompt,
    ai_model: blog.ai_model,
    keywords: blog.keywords,
    target_audience: blog.target_audience,
    tone: blog.tone,
    word_count: blog.word_count,
    language: blog.language,
    category_id: blog.category_id,
    tags: blog.tags,
    generation_settings: blog.generation_settings,
  };
}

export interface GenerationRequest {
  prompt: string;
  ai_model: AI_MODEL_TYPE;
  keywords?: string[];
  target_audience?: string;
  tone?: string;
  word_count?: number;
  language?: string;
  category_id?: string;
  tags?: string[];
  generation_settings?: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    frequency_penalty?: number;
    presence_penalty?: number;
  };
}

export interface GenerationResponse {
  id: string;
  status: BLOG_AI_STATUS;
  content?: string;
  title?: string;
  excerpt?: string;
  generation_time?: number;
  quality_score?: number;
  ai_confidence?: number;
  error_message?: string;
}
