import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  cleanParams,
} from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { ProductAttributeModal } from '../form/modal';
import useProductAttributeStore from '../store';
import { AttributeType, ProductAttribute } from '../type';
import { ProductAttributeSearch } from './search';

interface ProductAttributeListProps {
  showOptions?: boolean;
}

function ProductAttributeList({
  showOptions = false,
}: ProductAttributeListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const { attributeId } = useParams<{ attributeId: string }>();
  const query = queryString.parse(search);
  const { loading } = useProductAttributeStore();

  const [pagination, setPagination] = useState<any>({
    page: 1,
    page_size: 10,
  });
  const [meta, setMeta] = useState<any>({
    next_cursor: '',
    has_more: false,
  });
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<ProductAttribute[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...pagination,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setMeta(response.meta);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname, pagination],
  );

  useEffectOnce(() => {
    if (!showOptions) {
      fetchData();
    }
  });

  useEffect(() => {
    if (!showOptions && pagination.cursor !== '') {
      fetchData();
    }
  }, [pagination, showOptions, fetchData]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData({ ...values });
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.attribute_id.toString());
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.attribute_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    } else if (action === 'options') {
      navigate(`/${MODULE}/${record.attribute_id}/options`);
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handlePagination = (page: number, pageSize: number) => {
    setPagination({ cursor: meta.next_cursor });
  };

  const columns = [
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('code'),
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: t('type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <span>{t(`attributeType.${type}`)}</span>,
    },
    {
      title: t('isConfigurable'),
      dataIndex: 'is_configurable',
      key: 'is_configurable',
      render: (isConfigurable: boolean) => (
        <Tag color={isConfigurable ? 'green' : 'red'}>
          {isConfigurable ? 'Có' : 'Không'}
        </Tag>
      ),
    },
    {
      title: t('isFilterable'),
      dataIndex: 'is_filterable',
      key: 'is_filterable',
      render: (isFilterable: boolean) => (
        <Tag color={isFilterable ? 'green' : 'red'}>
          {isFilterable ? 'Có' : 'Không'}
        </Tag>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'options',
                label: (
                  <div onClick={() => handleActions('options', record)}>
                    <SettingOutlined /> {t('btnOptions')}
                  </div>
                ),
                disabled: ![
                  AttributeType.SELECT,
                  AttributeType.MULTISELECT,
                ].includes(record.type as AttributeType),
              },
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() =>
                      handleDelete(record.attribute_id.toString())
                    }
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  // Nếu đang ở trang options, chuyển hướng đến trang quản lý options
  if (showOptions && attributeId) {
    // Redirect to the attribute options page
    navigate(`/ecom-product-attribute-option?attribute_id=${attributeId}`);
    return null;
  }

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>

      <div className="p-4 bg-white shadow">
        <ProductAttributeSearch onSearch={handleFilters} />
      </div>

      <div className="mt-4">
        <Table
          columns={columns}
          dataSource={items}
          rowKey="attribute_id"
          loading={loading}
          pagination={{
            current: pagination.page,
            pageSize: pagination.page_size,
            total: meta.total,
            onChange: handlePagination,
            showSizeChanger: true,
          }}
        />
      </div>

      {showModal && (
        <ProductAttributeModal
          open={showModal}
          id={idCurrent}
          onClose={handleModal}
        />
      )}
    </div>
  );
}

export default ProductAttributeList;
