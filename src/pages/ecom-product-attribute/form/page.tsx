import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, message, Space } from 'antd';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import { MODULE } from '../config';
import ProductAttributeForm from './form';

export default function ProductAttributeFormPage() {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { attributeId } = useParams();
  const formRef = useRef<any>();

  const handleBack = () => {
    navigate(`/${MODULE}`);
  };

  const handleSubmit = () => {
    if (formRef.current) {
      formRef.current.submitForm();
    }
  };

  const handleChange = (reload: boolean) => {
    if (reload) {
      message.success(
        attributeId ? t('form.updateSuccess') : t('form.addSuccess'),
      );
      navigate(`/${MODULE}`);
    }
  };

  const isEdit = attributeId && attributeId !== 'create';
  const title = isEdit ? t('form.updateTitle') : t('form.addTitle');

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{title}</div>
        <div className="flex gap-4">
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              {t('btnBack')}
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
            >
              {t('btnSave')}
            </Button>
          </Space>
        </div>
      </div>

      <div className="p-6 bg-white">
        <ProductAttributeForm
          id={isEdit ? attributeId : undefined}
          onChange={handleChange}
          ref={formRef}
        />
      </div>
    </div>
  );
}
