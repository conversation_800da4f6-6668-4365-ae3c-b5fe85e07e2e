import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  QuestionOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { CrawlWebCategoryListModal } from '../../crawl-web-category';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_GROUP, MODULE_POPUP } from '../config';
import { CrawlWebModal } from '../form/modal';
import useCrawlWebStore from '../store';
import { CrawlWeb } from '../type';
import { CrawlWebSearch } from './search';

function CrawlWebList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useCrawlWebStore();

  const {
    afterKey,
    isNext,
    isBack,
    setNextCursor,
    goNext,
    goBack,
    resetPagination,
  } = useCursorPagination({
    defaultLimit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<CrawlWeb[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [showModalCategory, setShowModalCategory] = useState(false);

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.meta.total);
        setNextCursor(response.meta.cursors.after);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname],
  );

  useEffect(() => {
    fetchData({ after: afterKey });
  }, [fetchData, afterKey]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    resetPagination();
    setFilters(values);
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('form.deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    {
      title: t('form.id'),
      dataIndex: 'id',
      key: 'id',
      render: (dom: any, record: any) => <span>{dom}</span>,
    },
    {
      title: t('form.domain'),
      dataIndex: 'domain',
      key: 'domain',
    },
    {
      title: t('list.categories'),
      dataIndex: 'categories',
      key: 'categories',
      render: (dom: any, record: any) => (
        <a
          onClick={() => {
            setIdCurrent(record.id);
            setShowModalCategory(true);
          }}
        >
          {dom ? dom.length : 0}
        </a>
      ),
    },

    {
      title: t('form.status'),
      dataIndex: 'status',
      render: (dom: string) => <span>{t(`status.${dom}`)}</span>,
    },
    {
      title: t('list.actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('list.deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('list.btnDelete')}
                  </Popconfirm>
                ),
              },
              {
                key: 'logs',
                label: (
                  <Link to={`/crawl/crawl-log?topic=${record.id}`}>
                    <QuestionOutlined /> {t('list.btnLogs')}
                  </Link>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('list.btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('list.btnAdd')}
          </Button>
        </div>
      </div>
      <CrawlWebSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></CrawlWebSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <CrawlWebModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></CrawlWebModal>
        )}
        {showModalCategory && (
          <CrawlWebCategoryListModal
            showModal={showModalCategory}
            onChange={() => setShowModalCategory(false)}
            crawlWebId={idCurrent}
          ></CrawlWebCategoryListModal>
        )}
      </Space>
    </div>
  );
}
export { CrawlWebList };
