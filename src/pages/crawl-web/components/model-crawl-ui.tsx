import { SaveOutlined } from '@ant-design/icons';
import { <PERSON>ff<PERSON>, But<PERSON>, Divider, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { getHtml } from '../api';
import '../assets/styles.scss';
import { MODULE } from '../config';
import { CrawlWebForm } from '../form';
import { CrawlWeb } from '../type';
interface Props {
  config: CrawlWeb;
  onChange?: (event: React.ChangeEvent<HTMLDivElement>) => void;
}

export const CrawlUI: React.FC<Props> = ({ config, onChange }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);

  const divRef = useRef<HTMLDivElement>(null);
  const [content, setContent] = useState<string>('');
  const containerRef = useRef(null);
  const formRef = useRef<any>(null);

  useEffect(() => {
    console.log(config);
    getItemData(config.urlListTest);
  }, [config]);

  const getItemData = async (url: string) => {
    setContent('Loading...');
    const res = await getHtml({ url: url, crawlWebId: config.id });
    if (res.status.success) {
      console.log(res);
      setContent(res.data.html);
    } else {
      message.error(res.status.message);
    }
  };
  function handleActions(action: string): void {
    logger('[handleActions]', action);
    if (action === 'save') {
      formRef.current?.submitForm();
    }
  }
  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
  };
  return (
    <div
      className="mb-4 model_crawl"
      ref={containerRef}
      style={{ height: '100vh', overflow: 'auto' }}
    >
      <div>
        {/* <p>Url: {config.urlListTest}</p> */}
        <div className="p-4">
          <CrawlWebForm
            ref={formRef}
            onChange={handleChangeForm}
            id={config.id}
          ></CrawlWebForm>
          <div className="p-4">
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={() => handleActions('save')}
            >
              {t('form.btnSave')}
            </Button>
          </div>
        </div>

        <Divider />

        <Affix offsetTop={0} target={() => containerRef.current}>
          <div className="bg-white p-4">
            <Button
              type="primary"
              onClick={() => getItemData(config.urlListTest)}
              className="mr-4"
            >
              {t('form.btnGetLinks')}
            </Button>
            <Button
              type="primary"
              onClick={() => getItemData(config.urlDetailTest)}
            >
              {t('form.btnGetContent')}
            </Button>
          </div>
        </Affix>

        <div
          ref={divRef}
          onChange={onChange}
          dangerouslySetInnerHTML={{ __html: content }}
        ></div>
      </div>
    </div>
  );
};
