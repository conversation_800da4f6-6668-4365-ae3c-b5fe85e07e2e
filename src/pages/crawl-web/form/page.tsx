import {
  ReloadOutlined,
  SaveOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Button } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE, MODULE_GROUP } from '../config';
import { CrawlWebForm } from './form';

const CrawlWebPage: React.FC = () => {
  const { id } = useParams();
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const formRef = useRef<any>(null);
  const navigate = useNavigateTenant();

  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    navigate(`/${MODULE_GROUP}/`);
  };

  function handleActions(action: string): void {
    logger('[handleActions]', action);
    if (action === 'save') {
      formRef.current?.submitForm();
    } else if (action === 'cancel') {
      navigate(`/${MODULE_GROUP}/`);
    } else if (action === 'import') {
      formRef.current?.showModalConfig();
    } else if (action === 'modalCrawl') {
      formRef.current?.showModalCrawl();
    }
  }

  return (
    <div className="bg-white">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="dashed"
            icon={<SettingOutlined />}
            onClick={() => handleActions('modalCrawl')}
          >
            {t('list.btnModalCrawl')}
          </Button>

          <Button
            type="dashed"
            icon={<ReloadOutlined />}
            onClick={() => handleActions('import')}
          >
            {t('list.btnImport')}
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => handleActions('save')}
          >
            {t('list.btnSave')}
          </Button>
        </div>
      </div>
      <div className="p-4">
        <CrawlWebForm
          ref={formRef}
          onChange={handleChangeForm}
          id={id}
        ></CrawlWebForm>
      </div>
    </div>
  );
};

export { CrawlWebPage };
