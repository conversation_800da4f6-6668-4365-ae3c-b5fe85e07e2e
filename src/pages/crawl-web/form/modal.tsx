import { ReloadOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Modal } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE, MODULE_GROUP } from '../config';
import { CrawlWebForm } from './form';

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  showModal: boolean;
  id?: string;
}

const CrawlWebModal: React.FC<IndexFormProps> = (props) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { showModal, onChange, id } = props;
  const navigate = useNavigateTenant();
  const formRef = useRef<any>(null);

  const handleModal = () => {
    onChange(false);
  };
  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    onChange(false);
  };
  function handleActions(action: string): void {
    logger('[handleActions]', action);
    if (action === 'save') {
      formRef.current?.submitForm();
    } else if (action === 'cancel') {
      navigate(`/${MODULE_GROUP}/`);
    } else if (action === 'import') {
      formRef.current?.showModalConfig();
    }
  }
  return (
    <Modal
      title={t('module')}
      open={showModal}
      onCancel={handleModal}
      footer={false}
      className="form_modal"
      width={'90%'}
    >
      <CrawlWebForm
        ref={formRef}
        onChange={handleChangeForm}
        id={id}
      ></CrawlWebForm>

      <div className="form_footer flex gap-4">
        <Button
          type="dashed"
          icon={<ReloadOutlined />}
          onClick={() => handleActions('cancel')}
        >
          {t('form.btnCancel')}
        </Button>

        <Button
          type="primary"
          icon={<SaveOutlined />}
          onClick={() => handleActions('save')}
        >
          {t('form.btnSave')}
        </Button>
      </div>
    </Modal>
  );
};

export { CrawlWebModal };
