import {
  BuildOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LogoutOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Divider,
  List,
  Space,
  Typography,
} from 'antd';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { getUserTenants } from './api';
import { useAuthStore } from './auth';
import useLoginStore from './store';
import { Tenant } from './type';

const { Title, Text } = Typography;

// Di chuyển hàm getUserTenants từ api.ts

const TenantSelectionPage: React.FC = () => {
  const { t } = useTranslation(['auth', 'common']);
  const navigate = useNavigate();
  const { tenants, setTenants, setSelectedTenant } = useLoginStore();
  const { logout } = useAuthStore();

  useEffect(() => {
    getUserTenants().then((res) => {
      setTenants(res.data.tenants);
    });
  }, [setTenants]);

  const handleSelectTenant = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    // Sau khi chọn tenant, chuyển hướng đến trang dashboard với tenant_id
    navigate(`/dashboard/${tenant.tenant_id}`);
  };

  const handleLogout = () => {
    logout();
    navigate('/auth/login');
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'inactive':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <BuildOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      default:
        return 'warning';
    }
  };

  // Nếu không có tenants hoặc đã chọn tenant, chuyển hướng đến trang chính
  // React.useEffect(() => {
  //   if (!tenants || tenants.length === 0) {
  //     navigate('/');
  //   }

  //   if (tenantService.hasTenantSelected()) {
  //     navigate('/');
  //   }
  // }, [tenants, navigate]);

  return (
    <div className="max-w-[600px] mx-auto p-5">
      <Card bordered={false} className="shadow-md">
        <Title level={4}>{t('tenant_selection.title')}</Title>
        <Text type="secondary">{t('tenant_selection.subtitle')}</Text>

        <Divider />

        <List
          itemLayout="horizontal"
          dataSource={tenants}
          renderItem={(tenant) => (
            <List.Item
              key={tenant.tenant_id}
              actions={[
                <Button
                  key={`select-tenant-${tenant.tenant_id}`}
                  type="primary"
                  onClick={() => handleSelectTenant(tenant)}
                >
                  {t('tenant_selection.select_button')}
                </Button>,
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar style={{ backgroundColor: '#1890ff' }} size="large">
                    {tenant.tenant_name.charAt(0).toUpperCase()}
                  </Avatar>
                }
                title={
                  <Space>
                    {tenant.tenant_name}
                    <Badge
                      status={getStatusColor(tenant.status) as any}
                      text={tenant.status}
                    />
                  </Space>
                }
                description={
                  <Text type="secondary">
                    {t('tenant_selection.role')}: {tenant.role_name}
                  </Text>
                }
              />
            </List.Item>
          )}
        />

        <Divider />

        <div className="flex justify-end mt-4">
          <Button icon={<LogoutOutlined />} onClick={handleLogout}>
            {t('tenant_selection.logout_button')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default TenantSelectionPage;
