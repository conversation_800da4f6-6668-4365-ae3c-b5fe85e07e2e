import { apiService } from '../../services/api.service';
import { UserTenantsResponse } from './type';

interface LoginRequest {
  email: string;
  password: string;
  otp?: string;
}

interface RegisterRequest {
  email: string;
  username: string;
  full_name: string;
  password: string;
  confirmPassword: string;
}

interface StatusResponse {
  code: number;
  message: string;
  success: boolean;
  error_code?: string;
  path: string;
  timestamp: string;
  details?: Array<{
    message: string;
  }>;
}

interface LoginResponseData {
  user_id: string;
  username: string;
  access_token: string;
  refresh_token: string;
  access_token_expires_in: number;
  refresh_token_expires_in: number;
  status?: string;
  login_token?: string;
}

export interface LoginResponse {
  status: StatusResponse;
  data: LoginResponseData;
}

export const apiLogin = async (
  payload: LoginRequest,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/admin/v1/auth/login',
    payload,
  );
  return response.data;
};

export const apiRegister = async (
  payload: RegisterRequest,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/admin/v1/auth/register',
    payload,
  );
  return response.data;
};

export const otpConfig = async (
  payload: LoginRequest,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/admin/v1/auth/otp-config',
    payload,
  );
  return response.data;
};

export const otpConfirm = async (
  payload: LoginRequest,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/admin/v1/auth/otp-config-confirm',
    payload,
  );
  return response.data;
};

interface VerifyOtpRequest {
  login_token: string;
  token: string;
}

export const verifyOtp = async (
  payload: VerifyOtpRequest,
): Promise<LoginResponse> => {
  try {
    const response = await apiService.post<any>(
      '/api/admin/v1/auth/verify-otp',
      payload,
    );
    return response.data;
  } catch (error: any) {
    if (error.response.data.status.code === 400) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

interface RefreshTokenRequest {
  refresh_token: string;
}

export const refreshToken = async (
  refreshToken: string,
): Promise<LoginResponse> => {
  try {
    const response = await apiService.post<any>(
      '/api/admin/v1/auth/refresh-token',
      {
        refresh_token: refreshToken,
      },
    );
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.data) {
      return error.response.data;
    }
    throw error;
  }
};

export const verifyEmail = async (token: string): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/admin/v1/auth/verify-email',
    { token },
  );
  return response.data;
};

export const resendVerification = async (
  email: string,
): Promise<LoginResponse> => {
  const response = await apiService.post<any>(
    '/api/admin/v1/auth/resend-verification',
    { email },
  );
  return response.data;
};

export const getUserTenants = async (): Promise<{
  status: {
    code: number;
    message: string;
    success: boolean;
    error_code?: string;
    path: string;
    timestamp: string;
    details?: Array<{ message: string }>;
  };
  data: UserTenantsResponse;
}> => {
  const response = await apiService.get<any>(`/api/admin/v1/users/me/tenants`);
  return response.data;
};
