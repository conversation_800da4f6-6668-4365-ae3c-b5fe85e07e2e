{"username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "email": "Email", "fullName": "Họ và tên", "submit": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON> ký", "login": "<PERSON><PERSON><PERSON>", "loginTitle": "<PERSON><PERSON><PERSON>", "registerTitle": "<PERSON><PERSON><PERSON> ký", "loginWithEmail": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> v<PERSON><PERSON>", "registerWithEmail": "Đ<PERSON>ng ký với Email", "loginWithGoogle": "T<PERSON><PERSON>p t<PERSON> v<PERSON>i Google", "loginWithFacebook": "T<PERSON><PERSON><PERSON> t<PERSON> với Facebook", "loginWithApple": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> với <PERSON>", "loginWithSSO": "<PERSON><PERSON><PERSON><PERSON> tụ<PERSON> bằng SSO", "haveAccount": "Bạn đã có tài k<PERSON>n?", "noAccount": "Bạn không có tài khoản?", "or": "hoặc", "termsAgreement": "Bằng việc đăng ký, bạn đồng ý với Điều khoản dịch vụ và Ch<PERSON>h sách bảo mật của chúng tôi", "termsOfService": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ", "privacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật", "loggingIn": "<PERSON><PERSON> đăng nhập...", "loginError": "Lỗi đăng nhập", "backToLogin": "Quay lại đăng nh<PERSON>p", "confirm": "<PERSON><PERSON><PERSON>", "goBack": "Quay lại", "validation": {"emailRequired": "Vui lòng nhập email!", "emailInvalid": "Email không hợp lệ!", "passwordRequired": "<PERSON>ui lòng nhập mật khẩu!", "passwordMinLength": "<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự!", "usernameRequired": "<PERSON>ui lòng nhập tên đăng nhập!", "usernameMinLength": "Tên đ<PERSON>ng nhập phải có ít nhất 3 ký tự!", "fullNameRequired": "Vui lòng nhập họ và tên!", "confirmPasswordRequired": "<PERSON>ui lòng xác nhận mật khẩu!", "passwordMismatch": "<PERSON><PERSON><PERSON> khẩu xác nhận không khớp!", "otpRequired": "<PERSON>ui lòng nhập mã OTP!", "otpLength": "Mã OTP phải có 6 chữ số!", "otpDigitsOnly": "Mã OTP chỉ được chứa chữ số!"}, "messages": {"registerSuccess": "<PERSON><PERSON>ng ký thành công!", "registerFailed": "<PERSON><PERSON><PERSON> ký thất bại. <PERSON><PERSON> lòng thử lại sau.", "passwordMismatch": "<PERSON><PERSON><PERSON> khẩu xác nhận không khớp!", "loginSuccess": "<PERSON><PERSON><PERSON> nhập thành công!", "loginFailed": "<PERSON><PERSON><PERSON> nhập thất bại. <PERSON><PERSON> lòng kiểm tra thông tin đăng nhập.", "otpVerifySuccess": "<PERSON><PERSON><PERSON> thực OTP thành công!", "otpVerifyFailed": "<PERSON><PERSON><PERSON> thực <PERSON> thất bại!", "otpVerifyError": "Đ<PERSON> xảy ra lỗi khi xác thực OTP"}, "otp": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> lòng nhập mã OTP từ ứng dụng xác thực của bạn", "placeholder": "<PERSON><PERSON><PERSON><PERSON> mã 6 chữ số", "setup": "<PERSON><PERSON><PERSON><PERSON>", "setupDescription": "<PERSON><PERSON><PERSON><PERSON> lập xác thực hai yếu tố cho tài kho<PERSON>n của bạn", "scanQR": "Quét mã QR này bằng ứng dụng xác thực của bạn", "enterCode": "<PERSON><PERSON><PERSON><PERSON> mã từ ứng dụng để xác minh thiết lập", "setupSuccess": "<PERSON><PERSON><PERSON><PERSON> lập OTP thành công!", "setupFailed": "<PERSON><PERSON><PERSON><PERSON> lập OTP thất bại!"}, "emailVerification": {"verifying": "<PERSON><PERSON> xác thực email...", "pleaseWait": "<PERSON>ui lòng đợi trong gi<PERSON> l<PERSON>t.", "success": "<PERSON><PERSON><PERSON> thực email thành công!", "failed": "<PERSON><PERSON><PERSON> thực email thất bại", "invalidToken": "Token xác thực không hợp l<PERSON>.", "loginNow": "<PERSON><PERSON><PERSON> nh<PERSON> ngay", "resendVerification": "G<PERSON>i lại email x<PERSON>c thực", "resendTitle": "<PERSON><PERSON><PERSON> lại x<PERSON>c thực", "resendDescription": "<PERSON>hập email của bạn để nhận liên kết xác thực mới", "resendSuccess": "Email xác thực đã được gửi thành công!", "resendFailed": "Gửi email xác thực thất bại. <PERSON><PERSON> lòng thử lại sau.", "resendButton": "Gửi email xác thực", "checkEmail": "<PERSON><PERSON> lòng kiểm tra email để nhận liên kết xác thực."}, "tenant_selection": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON> lòng chọn một tenant để tiếp tục", "select_button": "<PERSON><PERSON><PERSON>", "logout_button": "<PERSON><PERSON><PERSON> xu<PERSON>", "role": "<PERSON>ai trò"}}