import { <PERSON><PERSON>, <PERSON>, Result, Spin, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import * as api from '../api';

const { Title, Text } = Typography;

const VerifyEmail: React.FC = () => {
  const { t } = useTranslation('auth');
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [message, setMessage] = useState('');
  const [isAutoLoggingIn, setIsAutoLoggingIn] = useState(false);
  const navigate = useNavigateTenant();
  const location = useLocation();
  const { setIsLoggedIn } = useAuthStore();
  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Lấy token từ URL query parameter
        const query = new URLSearchParams(location.search);
        const token = query.get('token');

        if (!token) {
          setMessage(t('emailVerification.invalidToken'));
          setIsSuccess(false);
          setIsVerifying(false);
          return;
        }

        // Call email verification API
        const response = await api.verifyEmail(token);
        console.log(response);
        if (response.status.success && response.data) {
          setIsSuccess(true);
          setMessage(response.status.message || 'Email verified successfully');

          // Auto login if tokens are provided
          if (response.data.access_token && response.data.refresh_token) {
            setIsAutoLoggingIn(true);

            // Save tokens
            tokenService.saveToken(response.data.access_token);
            tokenService.saveRefreshToken(response.data.refresh_token);
            setAuthToken(response.data.access_token);

            // Save token expiry dates if provided
            if (response.data.access_token_expires_in) {
              const accessTokenExpiry = new Date(Date.now() + response.data.access_token_expires_in * 1000).getTime();
              tokenService.saveExpiryDate(accessTokenExpiry);
            }
            if (response.data.refresh_token_expires_in) {
              const refreshTokenExpiry = new Date(Date.now() + response.data.refresh_token_expires_in * 1000).getTime();
              tokenService.saveRefreshExpiryDate(refreshTokenExpiry);
            }

            // Set logged in state
            setIsLoggedIn(true);

            // Check onboarding status and redirect accordingly
            if (response.data.onboarding_status === 'not_started' || response.data.onboarding_status === 'in_progress') {
              // Redirect to onboarding
              setTimeout(() => {
                navigate('/tenant-onboarding');
              }, 2000);
            } else if (response.data.tenant_id) {
              // Save tenant info if provided
              tenantService.setCurrentTenantIdFromUrl(response.data.tenant_id.toString());
              // Redirect to dashboard
              setTimeout(() => {
                navigate(`/dashboard/${response.data.tenant_id}`);
              }, 2000);
            } else {
              // Redirect to tenant selection
              setTimeout(() => {
                navigate('/auth/tenant-selection');
              }, 2000);
            }
          }
        } else {
          setIsSuccess(false);
          setMessage(response.status.message || 'Email verification failed');
        }
      } catch (error: any) {
        console.error(error);
        setIsSuccess(false);
        if (error.response && error.response.data && error.response.status) {
          setMessage(
            error.response.status.message || 'Email verification failed',
          );
        } else {
          setMessage('Email verification failed');
        }
      } finally {
        setIsVerifying(false);
        setIsAutoLoggingIn(false);
      }
    };

    verifyEmail();
  }, [location.search]);

  const goToLogin = () => {
    navigate('/auth/login');
  };

  const goToResendVerification = () => {
    navigate('/auth/resend-verification');
  };

  if (isVerifying || isAutoLoggingIn) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Card style={{ width: 400, textAlign: 'center' }}>
          <Spin size="large" />
          <Title level={4} style={{ marginTop: 20 }}>
            {isAutoLoggingIn ? 'Đang đăng nhập...' : 'Đang xác thực email...'}
          </Title>
          <Text>{isAutoLoggingIn ? 'Vui lòng đợi trong giây lát' : 'Vui lòng đợi trong giây lát'}</Text>
        </Card>
      </div>
    );
  }

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <Card style={{ width: 500, textAlign: 'center' }}>
        {isSuccess ? (
          <Result
            status="success"
            title="Email đã được xác thực thành công!"
            subTitle={isAutoLoggingIn ? "Đang tự động đăng nhập và chuyển hướng..." : message}
            extra={!isAutoLoggingIn ? [
              <Button type="primary" key="login" onClick={goToLogin}>
                Đăng nhập ngay
              </Button>,
            ] : undefined}
          />
        ) : (
          <Result
            status="error"
            title="Xác thực email thất bại"
            subTitle={message}
            extra={[
              <Button type="primary" key="login" onClick={goToLogin}>
                Đăng nhập
              </Button>,
              <Button key="resend" onClick={goToResendVerification}>
                Gửi lại email xác thực
              </Button>,
            ]}
          />
        )}
      </Card>
    </div>
  );
};

export default VerifyEmail;
