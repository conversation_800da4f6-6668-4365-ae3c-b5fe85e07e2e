import React, { useEffect, useState } from 'react';
import useSystemSettingStore from '../../system-setting/store';
import LoginForm from './form';
import OTPForm from './otpConfigForm';
import OTPVerifyForm from './otpVerifyForm';
import RegisterForm from './register';

const Login: React.FC = () => {
  const { itemPublicCMS, getPublicCMS } = useSystemSettingStore();
  const [step, setStep] = useState<
    'login' | 'register' | 'otp_config' | 'otp_verify' | 'tenant_selection'
  >('login'); // 'login', 'register', 'otp_config', 'otp_verify'

  useEffect(() => {
    getPublicCMS();
  }, [getPublicCMS]);

  const handleLogin = (action: string) => {
    console.log('handleLogin', action);
    if (action === 'register') {
      setStep('register');
    } else if (action === 'login') {
      setStep('login');
    } else if (action === 'otp_config') {
      setStep('otp_config');
    } else if (action === 'otp_verify') {
      setStep('otp_verify');
    } else if (action === 'logout') {
      setStep('login');
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      {step === 'login' && (
        <LoginForm
          onChange={handleLogin}
          otpEnabled={itemPublicCMS?.otpEnabled}
        />
      )}
      {step === 'register' && <RegisterForm onChange={handleLogin} />}
      {step === 'otp_config' && <OTPForm onChange={handleLogin} />}
      {step === 'otp_verify' && <OTPVerifyForm onChange={handleLogin} />}
    </div>
  );
};

export default Login;
