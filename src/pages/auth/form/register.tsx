import { Button, Divider, Form, Input, message } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { apiRegister } from '../api';

interface RegisterFormProps {
  onChange: (action: string) => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onChange }) => {
  const { t } = useTranslation('auth');
  const logger = ConsoleService.register('register-form');
  const [password, setPassword] = useState('');

  const onFinish = async (values: any) => {
    if (values.password !== values.confirmPassword) {
      message.error(t('messages.passwordMismatch'));
      return;
    }

    try {
      const response = await apiRegister({
        email: values.email,
        username: values.username,
        full_name: values.fullName,
        password: values.password,
        confirmPassword: values.confirmPassword,
      });

      if (response.status.success) {
        message.success(t('messages.registerSuccess'));
        onChange('login');
      } else {
        message.error(response.status.message);
      }
    } catch (error: any) {
      logger('Register error:', error);
      message.error(t('messages.registerFailed'));
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow min-w-96">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">{t('registerTitle')}</h2>
        <p className="text-gray-500">
          {t('haveAccount')}{' '}
          <a
            href="#"
            onClick={() => onChange('login')}
            className="text-blue-500"
          >
            {t('login')}
          </a>
        </p>
      </div>

      <Form
        name="register"
        layout="vertical"
        initialValues={{ remember: true }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Form.Item
          label={t('username')}
          name="username"
          rules={[
            { required: true, message: t('validation.usernameRequired') },
            { min: 3, message: t('validation.usernameMinLength') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('fullName')}
          name="fullName"
          rules={[
            { required: true, message: t('validation.fullNameRequired') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('email')}
          name="email"
          rules={[
            { required: true, message: t('validation.emailRequired') },
            { type: 'email', message: t('validation.emailInvalid') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('password')}
          name="password"
          rules={[
            { required: true, message: t('validation.passwordRequired') },
            { min: 6, message: t('validation.passwordMinLength') },
          ]}
        >
          <Input.Password onChange={(e) => setPassword(e.target.value)} />
          {/* <PasswordTooltip password={password}>
          </PasswordTooltip> */}
        </Form.Item>

        <Form.Item
          label={t('confirmPassword')}
          name="confirmPassword"
          rules={[
            {
              required: true,
              message: t('validation.confirmPasswordRequired'),
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t('validation.passwordMismatch')),
                );
              },
            }),
          ]}
        >
          <Input.Password />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" className="w-full">
            {t('registerWithEmail')}
          </Button>
        </Form.Item>

        <Divider plain>{t('or')}</Divider>

        <div className="space-y-3">
          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img
                src="/google-icon.svg"
                alt="Google"
                className="mr-2 h-5 w-5"
              />
            }
          >
            {t('loginWithGoogle')}
          </Button>

          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img
                src="/facebook-icon.svg"
                alt="Facebook"
                className="mr-2 h-5 w-5"
              />
            }
          >
            {t('loginWithFacebook')}
          </Button>

          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img src="/apple-icon.svg" alt="Apple" className="mr-2 h-5 w-5" />
            }
          >
            {t('loginWithApple')}
          </Button>
        </div>

        <div className="mt-4 text-center">
          <a href="#" className="text-blue-500">
            {t('loginWithSSO')}
          </a>
        </div>

        <div className="mt-6 text-xs text-center text-gray-500">
          <p>{t('termsAgreement')}</p>
        </div>
      </Form>
    </div>
  );
};

export default RegisterForm;
