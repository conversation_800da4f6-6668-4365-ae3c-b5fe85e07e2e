import { Button, Form, Input, message } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import tokenService from '../../../services/token.service';
import * as api from '../api';
import useLoginStore from '../store';

interface OTPVerifyFormProps {
  onChange: (action: string) => void;
}

const OTPVerifyForm: React.FC<OTPVerifyFormProps> = ({ onChange }) => {
  const { t } = useTranslation('auth');
  const { loginPayload } = useLoginStore();

  const onFinish = async (values: any) => {
    try {
      const response = await api.verifyOtp({
        login_token: loginPayload.login_token || '',
        token: values.otp,
      });

      if (response.status.success) {
        message.success(t('messages.otpVerifySuccess'));

        tokenService.saveToken(response.data.access_token);
        tokenService.saveRefreshToken(response.data.refresh_token);
        const accessTokenExpiry = new Date(
          response.data.access_token_expiry_date,
        ).getTime();
        const refreshTokenExpiry = new Date(
          response.data.refresh_token_expiry_date,
        ).getTime();
        tokenService.saveExpiryDate(accessTokenExpiry);
        tokenService.saveRefreshExpiryDate(refreshTokenExpiry);
        onChange('success');
      } else {
        message.error(response.status.message || t('messages.otpVerifyFailed'));
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      message.error(t('messages.otpVerifyError'));
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <Form
      name="otp-verify"
      layout="vertical"
      initialValues={{ remember: true }}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      className="bg-white p-6 rounded-lg shadow min-w-96"
    >
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold">{t('otp.title')}</h2>
        <p className="text-gray-500">{t('otp.description')}</p>
      </div>

      <Form.Item
        name="otp"
        rules={[
          { required: true, message: t('validation.otpRequired') },
          { len: 6, message: t('validation.otpLength') },
          { pattern: /^\d+$/, message: t('validation.otpDigitsOnly') },
        ]}
      >
        <Input
          size="large"
          placeholder={t('otp.placeholder')}
          maxLength={6}
          className="text-center text-lg tracking-widest"
        />
      </Form.Item>

      <Form.Item>
        <Button
          type="default"
          className="w-full mb-4"
          onClick={() => onChange('login')}
        >
          {t('goBack')}
        </Button>
        <Button type="primary" htmlType="submit" className="w-full">
          {t('confirm')}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default OTPVerifyForm;
