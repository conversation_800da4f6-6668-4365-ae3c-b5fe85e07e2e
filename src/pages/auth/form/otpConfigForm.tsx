import { Button, Form, message, QRCode } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as api from '../api';
import useLoginStore from '../store';

interface LoginFormProps {
  onChange: (action: string) => void;
}

const OTPForm: React.FC<LoginFormProps> = ({ onChange }) => {
  const { t } = useTranslation('login');
  const { loginPayload } = useLoginStore();
  const [otpConfig, setOtpConfig] = useState<any>();
  const [deviceId, setDeviceId] = useState<string>('');

  const getOtpConfig = useCallback(async () => {
    const response = await api.otpConfig();
    console.log('[response]', response);
    if (response.status.success) {
      setDeviceId(response.data.device);
      setOtpConfig(response.data);
    } else {
      message.error(response.status.message);
    }
  }, []);

  useEffect(() => {
    getOtpConfig();
  }, [getOtpConfig]);

  const onFinish = async (values: any) => {
    console.log('Success:', values);
    const response = await api.otpConfirm(loginPayload);
    console.log('[response]', response);
    if (response.status.success) {
      message.success(t('Cấu hình OTP thành công'));
      onChange('login');
    } else {
      message.error(response.status.message);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <Form
      name="login"
      layout="vertical"
      initialValues={{ remember: true }}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      className="bg-white p-6 rounded-lg shadow min-w-96"
    >
      <div className="mb-4">
        <p>
          - Sửa dụng{' '}
          <a
            href="https://chromewebstore.google.com/detail/authenticator/bhghoamapcdpbohphigoooaddinpkbai?hl=vi"
            target="_blank"
            rel="noreferrer"
          >
            Authenticator
          </a>{' '}
          để cấu hình trên Chrome
        </p>
        <p>
          - Sử dụng{' '}
          <a
            href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=vi"
            target="_blank"
            rel="noreferrer"
          >
            Google Authenticator
          </a>{' '}
          trên Android
        </p>
        <p>
          - Sử dụng{' '}
          <a
            href="https://apps.apple.com/us/app/google-authenticator/id388497605"
            target="_blank"
            rel="noreferrer"
          >
            Google Authenticator
          </a>{' '}
          trên IOS
        </p>
      </div>
      {otpConfig && (
        <div className="flex justify-center mb-4">
          <QRCode value={otpConfig.otpUrl} size={400} />
        </div>
      )}
      <Form.Item>
        <Button
          type="default"
          className="w-full mb-4"
          onClick={() => onChange('login')}
        >
          {t('Quay lại')}
        </Button>
        <Button type="primary" htmlType="submit" className="w-full">
          {t('Xác nhận đã cấu hình')}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default OTPForm;
