import { <PERSON><PERSON>, <PERSON><PERSON>, Divider, Form, Input } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { useAuthStore } from '../auth';
import useLoginStore from '../store';
import { Tenant } from '../type';

interface LoginFormProps {
  onChange: (action: string) => void;
  otpEnabled: boolean;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onChange,
  otpEnabled: _otpEnabled,
}) => {
  const { t } = useTranslation('auth');
  const { setLoginPayload } = useLoginStore();
  const { login, isLoading, error, clearError } = useAuthStore();
  const logger = ConsoleService.register('login-form');
  const [password, setPassword] = useState('');

  // Clear errors when component mounts or when starting a new login attempt
  useEffect(() => {
    clearError();
  }, [clearError]);
  const onFinish = async (values: any) => {
    console.log('Success:', values);

    // Clear any existing errors before attempting login
    clearError();

    // Store login payload for potential OTP flow
    setLoginPayload({
      email: values.email,
      password: values.password,
    });

    try {
      // Use the enhanced auth store login function
      const result = await login({
        email: values.email,
        password: values.password,
        otp: values.otp,
      });

      if (result.success) {
        // Successful login - redirect to success page
        logger('Login successful');
        onChange('success');
      } else if (result.requiresOtp) {
        // OTP setup required
        onChange('otp_config');
      } else if (result.requiresOtpVerify) {
        // OTP verification required
        setLoginPayload({
          ...values,
          login_token: result.loginToken,
        });
        onChange('otp_verify');
      } else if (result.requiresTenantSelection) {
        // Set tenants in store for tenant selection
        if (result.tenants && result.tenants.length > 0) {
          useLoginStore.getState().setTenants(result.tenants as Tenant[]);
        }
        onChange('tenant_selection');
      }
      // Error handling is already done by the auth store
    } catch (error) {
      // Additional error handling if needed
      logger('Login error:', error);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow min-w-96">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">{t('loginTitle')}</h2>
        <p className="text-gray-500">
          {t('noAccount')}{' '}
          <a
            href="#"
            onClick={() => onChange('register')}
            className="text-blue-500"
          >
            {t('register')}
          </a>
        </p>
      </div>

      {/* Display error message from auth store */}
      {error && (
        <Alert
          message={t('loginError')}
          description={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        name="login"
        layout="vertical"
        initialValues={{ remember: true }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Form.Item
          label={t('email')}
          name="email"
          rules={[
            { required: true, message: t('validation.emailRequired') },
            { type: 'email', message: t('validation.emailInvalid') },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label={t('password')}
          name="password"
          rules={[
            { required: true, message: t('validation.passwordRequired') },
          ]}
        >
          {/* <PasswordTooltip password={password}></PasswordTooltip> */}
          <Input.Password onChange={(e) => setPassword(e.target.value)} />
        </Form.Item>

        {/* <Form.Item
        label={t('OTP')}
        name="otp"
        rules={[{ required: true, message: 'Please input your password!' }]}
      >
        <Input />
      </Form.Item> */}

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            className="w-full"
            loading={isLoading}
            disabled={isLoading}
          >
            {isLoading ? t('loggingIn') : t('login')}
          </Button>
        </Form.Item>

        <Divider plain>{t('or')}</Divider>

        <div className="space-y-3">
          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img
                src="/google-icon.svg"
                alt="Google"
                className="mr-2 h-5 w-5"
              />
            }
          >
            {t('loginWithGoogle')}
          </Button>

          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img
                src="/facebook-icon.svg"
                alt="Facebook"
                className="mr-2 h-5 w-5"
              />
            }
          >
            {t('loginWithFacebook')}
          </Button>

          <Button
            className="w-full flex items-center justify-center"
            icon={
              <img src="/apple-icon.svg" alt="Apple" className="mr-2 h-5 w-5" />
            }
          >
            {t('loginWithApple')}
          </Button>
        </div>

        <div className="mt-4 text-center">
          <a href="#" className="text-blue-500">
            {t('loginWithSSO')}
          </a>
        </div>
      </Form>
    </div>
  );
};

export default LoginForm;
