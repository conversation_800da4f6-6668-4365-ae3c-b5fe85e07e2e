import { MailOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Form, Input, Typography } from 'antd';
import React, { useState } from 'react';
import { ButtonLink } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import * as api from '../api';

const { Title, Text, Paragraph } = Typography;

const ResendVerify: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigateTenant();

  const onFinish = async (values: { email: string }) => {
    setLoading(true);
    setError('');
    try {
      const response = await api.resendVerification(values.email);
      if (response.status.success) {
        setSuccess(true);
      } else {
        setError(
          response.status.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.',
        );
      }
    } catch (err: any) {
      console.error(err);
      if (err.response && err.response.data && err.response.data.status) {
        setError(
          err.response.data.status.message ||
            'Có lỗi xảy ra. Vui lòng thử lại sau.',
        );
      } else {
        setError('Không thể kết nối đến máy chủ. Vui lòng thử lại sau.');
      }
    } finally {
      setLoading(false);
    }
  };

  const goToLogin = () => {
    navigate('/auth/login');
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <Card style={{ width: 500, textAlign: 'center' }}>
        <Title level={3}>Gửi lại email xác thực</Title>

        {success ? (
          <>
            <Alert
              message="Gửi thành công!"
              description="Một email xác thực đã được gửi đến địa chỉ email của bạn. Vui lòng kiểm tra hộp thư và làm theo hướng dẫn."
              type="success"
              showIcon
              style={{ marginBottom: 20 }}
            />
            <Paragraph>
              Nếu bạn không nhận được email trong vòng vài phút, vui lòng kiểm
              tra thư mục spam hoặc thử lại.
            </Paragraph>
            <Button type="primary" onClick={goToLogin}>
              Quay lại đăng nhập
            </Button>
          </>
        ) : (
          <>
            <Paragraph style={{ marginBottom: 20 }}>
              Nhập địa chỉ email bạn đã đăng ký để nhận email xác thực mới.
            </Paragraph>

            {error && (
              <Alert
                message="Lỗi"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 20 }}
              />
            )}

            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              autoComplete="off"
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: 'Vui lòng nhập email của bạn' },
                  { type: 'email', message: 'Email không hợp lệ' },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="Email"
                  size="large"
                  disabled={loading}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={loading}
                >
                  Gửi yêu cầu
                </Button>
              </Form.Item>

              <Form.Item>
                <ButtonLink
                  to="/auth/login"
                  type="link"
                  disabled={loading}
                  absolute={true}
                >
                  Quay lại đăng nhập
                </ButtonLink>
              </Form.Item>
            </Form>
          </>
        )}
      </Card>
    </div>
  );
};

export default ResendVerify;
