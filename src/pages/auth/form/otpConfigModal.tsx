import { Modal } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import IndexForm from './otpConfigForm';

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  showModal: boolean;
}

const ModalForm: React.FC<IndexFormProps> = (props) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { showModal, onChange } = props;

  const handleModal = () => {
    onChange(false);
  };
  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    onChange(false);
  };
  return (
    <Modal
      title={t('Cấu hình OTP')}
      open={showModal}
      onCancel={handleModal}
      footer={false}
      className="form_modal"
    >
      <IndexForm onChange={handleChangeForm}></IndexForm>
    </Modal>
  );
};

export default ModalForm;
