import { message } from 'antd';
import { create } from 'zustand';
import { setAuthToken } from '../../services/api.service';
import tokenService from '../../services/token.service';
import { apiLogin } from './api';

interface LoginCredentials {
  email: string;
  password: string;
  otp?: string;
}

interface LoginResult {
  success: boolean;
  requiresOtp?: boolean;
  requiresOtpVerify?: boolean;
  requiresTenantSelection?: boolean;
  loginToken?: string;
  userId?: string | number;
  tenants?: any[];
}

interface AuthState {
  isLoggedIn: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<LoginResult>;
  logout: () => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  isLoggedIn: !!tokenService.getToken(),
  isLoading: false,
  error: null,

  login: async (credentials: LoginCredentials) => {
    set({ isLoading: true, error: null });

    try {
      const response = await apiLogin(credentials);

      // Check if the response status code is not 200 (success)
      if (response.status.code !== 200) {
        // Extract and display the error message from status.message
        const errorMessage = response.status.message || 'Đăng nhập thất bại';

        // Handle specific error cases
        if (response.status.code === 403) {
          // Handle email not verified case
          if (response.status.error_code === 'EMAIL_NOT_VERIFIED') {
            message.error(errorMessage); // Display Vietnamese message like "Email chưa được xác thực"
          } else {
            message.error(errorMessage);
          }
        } else {
          // Handle other error status codes
          message.error(errorMessage);
        }

        set({
          isLoading: false,
          error: errorMessage,
          isLoggedIn: false,
        });
        return { success: false };
      }

      // Success case - status code is 200
      if (response.status.success && response.data) {
        // Handle different response scenarios
        if (response.data.status === 'require_otp') {
          // OTP setup required
          set({ isLoading: false, error: null });
          return { success: false, requiresOtp: true };
        } else if (response.data.status === 'require_otp_verify') {
          // OTP verification required
          set({ isLoading: false, error: null });
          return {
            success: false,
            requiresOtpVerify: true,
            loginToken: response.data.login_token,
          };
        } else if (response.data.access_token) {
          // Successful login with tokens
          tokenService.saveToken(response.data.access_token);
          tokenService.saveRefreshToken(response.data.refresh_token);

          // Set expiry dates if provided
          if (response.data.access_token_expires_in) {
            const expiryTime =
              Date.now() + response.data.access_token_expires_in * 1000;
            tokenService.saveExpiryDate(expiryTime);
          }
          if (response.data.refresh_token_expires_in) {
            const refreshExpiryTime =
              Date.now() + response.data.refresh_token_expires_in * 1000;
            tokenService.saveRefreshExpiryDate(refreshExpiryTime);
          }

          // Set auth token for future requests
          setAuthToken(response.data.access_token);

          // // Check if user has multiple tenants
          // try {
          //   const userId = response.data.user.id;
          //   const tenantsResponse = await getUserTenants(userId);

          //   if (
          //     tenantsResponse.status.code === 200 &&
          //     tenantsResponse.data.tenants &&
          //     tenantsResponse.data.tenants.length > 1
          //   ) {
          //     set({
          //       isLoggedIn: true,
          //       isLoading: false,
          //       error: null,
          //     });
          //     message.success('Đăng nhập thành công!');
          //     return {
          //       success: true,
          //       requiresTenantSelection: true,
          //       userId: userId,
          //       tenants: tenantsResponse.data.tenants,
          //     };
          //   }

          //   // If user has only one tenant, select it automatically
          //   if (
          //     tenantsResponse.status.code === 200 &&
          //     tenantsResponse.data.tenants &&
          //     tenantsResponse.data.tenants.length === 1
          //   ) {
          //     const tenant = tenantsResponse.data.tenants[0];
          //     // Save selected tenant to localStorage
          //     localStorage.setItem(
          //       'selectedTenantId',
          //       tenant.tenant_id.toString(),
          //     );
          //     localStorage.setItem('selectedTenantName', tenant.tenant_name);
          //     localStorage.setItem('selectedTenantRole', tenant.role_name);
          //   }
          // } catch (tenantError) {
          //   console.error('Error fetching tenants:', tenantError);
          //   // Continue with login even if tenant fetch fails
          // }

          set({
            isLoggedIn: true,
            isLoading: false,
            error: null,
          });

          message.success('Đăng nhập thành công!');
          return { success: true };
        }
      }

      // Fallback error case
      const fallbackError = 'Đăng nhập thất bại. Vui lòng thử lại.';
      message.error(fallbackError);
      set({
        isLoading: false,
        error: fallbackError,
        isLoggedIn: false,
      });
      return { success: false };
    } catch (error: any) {
      // Handle network errors or other exceptions
      let errorMessage = 'Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.';

      // Try to extract error message from response
      if (error.response?.data?.status?.message) {
        errorMessage = error.response.data.status.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
      set({
        isLoading: false,
        error: errorMessage,
        isLoggedIn: false,
      });
      return { success: false };
    }
  },

  logout: () => {
    set({ isLoggedIn: false, error: null });
    tokenService.removeAll();
    // Remove tenant information
    localStorage.removeItem('selectedTenantId');
    localStorage.removeItem('selectedTenantName');
    localStorage.removeItem('selectedTenantRole');
  },

  clearError: () => {
    set({ error: null });
  },
}));
