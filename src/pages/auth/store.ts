import { create } from 'zustand';
import ConsoleService from '../../services/console.service';
import { Tenant } from './type';
const logger = ConsoleService.register('seo-meta.store');

export interface LoginPayload {
  email: string;
  password: string;
  login_token?: string;
}
export interface StoreState {
  loading: boolean;
  permissions: [];
  user: any;
  loginPayload: LoginPayload;
  tenants: Tenant[];
  selectedTenant: Tenant | null;
  setLoginPayload: (loginPayload: LoginPayload) => void;
  setTenants: (tenants: Tenant[]) => void;
  setSelectedTenant: (tenant: Tenant) => void;
}

const useLoginStore = create<StoreState>((set) => ({
  loading: false,
  permissions: [],
  user: undefined,
  loginPayload: { email: '', password: '' },
  tenants: [],
  selectedTenant: null,
  setLoginPayload: (payload: LoginPayload) => {
    set({ loginPayload: payload });
  },
  setTenants: (tenants) => set({ tenants }),
  setSelectedTenant: (tenant) => {
    // Save selected tenant to localStorage
    localStorage.setItem('selectedTenantId', tenant.tenant_id.toString());
    localStorage.setItem('selectedTenantName', tenant.tenant_name);
    localStorage.setItem('selectedTenantRole', tenant.role_name);
    set({ selectedTenant: tenant });
  },
}));

export default useLoginStore;
