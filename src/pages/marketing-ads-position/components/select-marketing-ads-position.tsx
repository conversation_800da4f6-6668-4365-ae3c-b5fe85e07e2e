import { Select, SelectProps, Spin } from 'antd';
import debounce from 'lodash/debounce';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import ConsoleService from '../../../services/console.service';
import { getPositionOptions } from '../api';
import { MODULE } from '../config';
import { MarketingAdsPosition } from '../type';

interface Props extends SelectProps {
  isOptionAll?: boolean;
}

export const SelectMarketingAdsPosition: React.FC<Props> = (props) => {
  const logger = ConsoleService.register(MODULE);
  const { isOptionAll = false } = props;
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<
    { label: React.ReactNode; value: string }[]
  >([]);

  logger('[SelectMarketingAdsPosition]', props);

  const fetchPositions = useCallback(
    async (search?: string) => {
      setLoading(true);
      try {
        const response = await getPositionOptions(search);
        logger('[Positions response]', response);

        const positions = response.data || [];

        const optionItems = positions.map((position: MarketingAdsPosition) => ({
          value: String(position.id),
          label: (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <span style={{ fontWeight: 500 }}>{position.name}</span>
                {position.code && (
                  <span style={{ fontSize: '12px', color: '#999' }}>
                    {position.code}
                  </span>
                )}
              </div>
            </div>
          ),
        }));

        if (isOptionAll) {
          optionItems.unshift({
            label: <span>Tất cả</span>,
            value: '',
          });
        }

        logger('[Option items]', optionItems);
        setOptions(optionItems);
      } catch (error) {
        logger('[Error fetching positions]', error);
      } finally {
        setLoading(false);
      }
    },
    [logger, isOptionAll],
  );

  // Sử dụng debounce để tối ưu việc search
  const debouncedFetchPositions = useMemo(
    () =>
      debounce((value: string) => {
        fetchPositions(value);
      }, 300),
    [fetchPositions],
  );

  // Xử lý khi search thay đổi
  const handleSearch = (value: string) => {
    debouncedFetchPositions(value);
  };

  // Fetch positions lần đầu khi component mount
  useEffect(() => {
    fetchPositions();

    // Clean up debounce khi unmount
    return () => {
      debouncedFetchPositions.cancel();
    };
  }, [fetchPositions, debouncedFetchPositions]);

  return (
    <Select
      showSearch
      filterOption={false}
      options={options}
      loading={loading}
      onSearch={handleSearch}
      notFoundContent={loading ? <Spin size="small" /> : null}
      placeholder="Chọn vị trí quảng cáo"
      style={{ width: '100%' }}
      {...props}
    />
  );
};
