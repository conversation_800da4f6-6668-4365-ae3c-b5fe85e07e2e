import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';

const { Option } = Select;

function SearchForm(props: any) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    logger('[values]', values);
    props.onChange(values);
  };

  function handlerRefresh() {
    form.resetFields();
    props.onChange({});
  }

  return (
    <Form
      layout="vertical"
      form={form}
      name="frmSearch"
      onFinish={onFinish}
      className="p-4 bg-white rounded-lg mb-4"
    >
      <Row>
        <Col span={24}>
          <Row gutter={16}>
            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="search"
                label={t('search')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Input placeholder={t('searchPlaceholder')} />
              </Form.Item>
            </Col>

            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="name"
                label={t('name')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Input placeholder={t('namePlaceholder')} />
              </Form.Item>
            </Col>

            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="code"
                label={t('code')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Input placeholder={t('codePlaceholder')} />
              </Form.Item>
            </Col>

            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="status"
                label={t('status')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder={t('statusPlaceholder')} allowClear>
                  <Option value="active">{t('statusActive')}</Option>
                  <Option value="inactive">{t('statusInactive')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col>
          <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
            {t('btnSearch')}
          </Button>
        </Col>
        <Col>
          <Button onClick={handlerRefresh} icon={<ReloadOutlined />}>
            {t('btnReset')}
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export default SearchForm;
