import { create } from 'zustand';
import { MarketingAdsPosition } from './type';

interface MarketingAdsPositionStore {
  items: MarketingAdsPosition[];
  currentItem: MarketingAdsPosition | null;
  loading: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  searchKeyword: string;

  // Actions
  setItems: (items: MarketingAdsPosition[]) => void;
  setCurrentItem: (item: MarketingAdsPosition | null) => void;
  setLoading: (loading: boolean) => void;
  setTotal: (total: number) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setSearchKeyword: (keyword: string) => void;

  // Reset
  reset: () => void;
}

const useMarketingAdsPositionStore = create<MarketingAdsPositionStore>(
  (set) => ({
    items: [],
    currentItem: null,
    loading: false,
    total: 0,
    currentPage: 1,
    pageSize: 10,
    searchKeyword: '',

    setItems: (items) => set({ items }),
    setCurrentItem: (item) => set({ currentItem: item }),
    setLoading: (loading) => set({ loading }),
    setTotal: (total) => set({ total }),
    setCurrentPage: (page) => set({ currentPage: page }),
    setPageSize: (size) => set({ pageSize: size }),
    setSearchKeyword: (keyword) => set({ searchKeyword: keyword }),

    reset: () =>
      set({
        items: [],
        currentItem: null,
        loading: false,
        total: 0,
        currentPage: 1,
        pageSize: 10,
        searchKeyword: '',
      }),
  }),
);

export default useMarketingAdsPositionStore;
