import { Button, Col, Form, Input, message, Row, Select } from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormHeader } from '../../../components/form-header';
import { InputSlug } from '../../../components/input';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import { MarketingAdsPosition } from '../type';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);

  const initForm = useMemo(
    () => ({
      status: 'active',
    }),
    [],
  );

  const getItemData = useCallback(
    async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    },
    [form],
  );

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
      form.setFieldsValue(initForm);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id, form, getItemData, initForm]);

  const onFinish = async (values: MarketingAdsPosition) => {
    logger('[onFinish]', values);

    const payload = {
      ...values,
    };

    let response;
    if (isNew) {
      response = await createItem(payload);
    } else {
      response = await updateItem(id as string, payload);
    }

    if (response.status.success) {
      message.success(isNew ? t('createItemSuccess') : t('updateItemSuccess'));
      onChange(true);
    } else {
      message.error(response.status.message);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    logger('[onFinishFailed]', errorInfo);
  };

  const onFormChange = (changedFields: any, allFields: any) => {
    logger('[onFormChange]', { changedFields, allFields });
  };

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        name="frmDetail"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        onFieldsChange={onFormChange}
        initialValues={initForm}
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <FormItem
              name="name"
              label={t('name')}
              rules={[
                { required: true, message: t('nameRequired') },
                { min: 2, message: t('nameMinLength') },
                { max: 100, message: t('nameMaxLength') },
              ]}
            >
              <Input placeholder={t('namePlaceholder')} />
            </FormItem>
          </Col>

          <Col xs={24} md={12}>
            <FormItem
              name="code"
              label={t('code')}
              rules={[
                { required: true, message: t('codeRequired') },
                { pattern: /^[a-zA-Z0-9_-]+$/, message: t('codePattern') },
              ]}
            >
              <InputSlug name="code" sourceField="name" form={form} />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24}>
            <FormItem
              name="description"
              label={t('description')}
              rules={[{ max: 500, message: t('descriptionMaxLength') }]}
            >
              <TextArea rows={4} placeholder={t('descriptionPlaceholder')} />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <FormItem
              name="status"
              label={t('status')}
              rules={[{ required: true, message: t('statusRequired') }]}
            >
              <Select placeholder={t('statusPlaceholder')}>
                <Option value="active">{t('statusActive')}</Option>
                <Option value="inactive">{t('statusInactive')}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>

        <div className="form_footer">
          <FormItem>
            <Button type="primary" htmlType="submit">
              {isNew ? t('btnAdd') : t('btnSave')}
            </Button>
          </FormItem>
        </div>
      </Form>
    </div>
  );
};

export default IndexForm;
