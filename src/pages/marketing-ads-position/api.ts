import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MarketingAdsPosition } from './type';

const url = `/api/admin/v1/marketing/ads-positions`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<MarketingAdsPosition[]>> {
  const response = await apiService.get<
    ApiResponsePagination<MarketingAdsPosition[]>
  >(url, { params });
  return response.data;
}

export async function getItem(
  id: string,
): Promise<ApiResponse<MarketingAdsPosition>> {
  const response = await apiService.get<ApiResponse<MarketingAdsPosition>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<MarketingAdsPosition>> {
  const response = await apiService.post<ApiResponse<MarketingAdsPosition>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<MarketingAdsPosition>> {
  const response = await apiService.put<ApiResponse<MarketingAdsPosition>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(
  id: string,
): Promise<ApiResponse<MarketingAdsPosition>> {
  const response = await apiService.delete<ApiResponse<MarketingAdsPosition>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getPositionOptions(
  search?: string,
): Promise<ApiResponse<MarketingAdsPosition[]>> {
  const params = search ? { search } : {};
  const response = await apiService.get<ApiResponse<MarketingAdsPosition[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<MarketingAdsPosition[]>> {
  const response = await apiService.get<ApiResponse<MarketingAdsPosition[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<MarketingAdsPosition[]>> {
  const response = await apiService.get<ApiResponse<MarketingAdsPosition[]>>(
    `${url}/all`,
  );
  return response.data;
}

export async function getActivePositions(): Promise<
  ApiResponse<MarketingAdsPosition[]>
> {
  const response = await apiService.get<ApiResponse<MarketingAdsPosition[]>>(
    `${url}/active`,
  );
  return response.data;
}
