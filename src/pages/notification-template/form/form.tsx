import { But<PERSON>, Card, Col, Form, Input, message, Row, Switch } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { VariableManager } from '../components';
import {
  DEFAULT_EMAIL_TEMPLATE,
  DEFAULT_PUSH_TEMPLATE,
  DEFAULT_SMS_TEMPLATE,
  MODULE,
} from '../config';
import useNotificationTemplateStore from '../store';
import { NotificationTemplate } from '../type';

const FormItem = Form.Item;
const { TextArea } = Input;

interface NotificationTemplateFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const NotificationTemplateForm: React.FC<NotificationTemplateFormProps> = ({
  onChange,
  id,
}) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading, setLoading } = useNotificationTemplateStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<NotificationTemplate>();
  const [selectedChannel, setSelectedChannel] = useState<string>('email');
  const contentRef = useRef<any>(null);

  const initForm = {
    is_active: true,
    channel_code: 'email',
  };

  const getItemData = async (_id: string) => {
    setLoading(true);
    try {
      const res = await getItem(parseInt(_id));
      if (res.status.success) {
        setItem(res.data);
        setSelectedChannel(res.data.channel_code);
        form.setFieldsValue({
          template_code: res.data.template_code,
          subject: res.data.subject,
          content: res.data.content,
          is_active: res.data.is_active,
          channel_code: res.data.channel_code,
        });
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      logger('Error fetching item:', error);
      message.error('Failed to fetch template data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
      form.setFieldsValue(initForm);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id]);

  const onFinish = async (values: any) => {
    setLoading(true);
    try {
      let res;
      const payload = {
        template_code: values.template_code,
        subject: values.subject,
        content: values.content,
        is_active: values.is_active,
      };

      if (isNew) {
        res = await createItem(payload);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        res = await updateItem(parseInt(id!), payload);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }

      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleChannelChange = (channel: string) => {
    setSelectedChannel(channel);

    // Set default template content based on channel
    let defaultContent = '';
    let defaultSubject = '';

    switch (channel) {
      case 'email':
        defaultContent = DEFAULT_EMAIL_TEMPLATE;
        defaultSubject = 'Welcome to {{app_name}}!';
        break;
      case 'sms':
        defaultContent = DEFAULT_SMS_TEMPLATE;
        defaultSubject = '';
        break;
      case 'push':
        defaultContent = DEFAULT_PUSH_TEMPLATE;
        defaultSubject = 'Welcome!';
        break;
      default:
        defaultContent = 'Hello {{user_name}}!';
        defaultSubject = 'Notification';
    }

    if (isNew) {
      form.setFieldsValue({
        content: defaultContent,
        subject: defaultSubject,
      });
    }
  };

  const handleInsertVariable = (variable: string) => {
    const contentValue = form.getFieldValue('content') || '';
    const newContent = contentValue + variable;
    form.setFieldsValue({ content: newContent });
  };

  const validateTemplateCode = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error(t('templateCodeRequired')));
    }

    // Check format: only letters, numbers, and underscores
    const regex = /^[a-zA-Z0-9_]+$/;
    if (!regex.test(value)) {
      return Promise.reject(new Error(t('invalidTemplateCode')));
    }

    return Promise.resolve();
  };

  return (
    <Form
      style={{ marginTop: 8 }}
      form={form}
      name="notification-template-form"
      layout="vertical"
      onFinish={onFinish}
      autoComplete="off"
      initialValues={initForm}
    >
      <div className="form_content">
        <Row gutter={16}>
          <Col xs={24} lg={12}>
            <FormItem
              label={t('template_code')}
              name="template_code"
              rules={[{ validator: validateTemplateCode }]}
              tooltip="Template code must contain only letters, numbers, and underscores"
            >
              <Input placeholder="welcome_email" disabled={!isNew} />
            </FormItem>
          </Col>

          <Col xs={24} lg={12}>
            <FormItem
              label={t('is_active')}
              name="is_active"
              valuePropName="checked"
            >
              <Switch
                checkedChildren={t('statusActive')}
                unCheckedChildren={t('statusDeActive')}
              />
            </FormItem>
          </Col>

          <Col xs={24} lg={24}>
            <FormItem
              label={t('subject')}
              name="subject"
              rules={[{ required: true, message: t('subjectRequired') }]}
              tooltip="You can use variables like {{user_name}} in the subject"
            >
              <Input placeholder="Welcome to {{app_name}}!" />
            </FormItem>
          </Col>

          <Col xs={24} lg={24}>
            <Card
              title={t('variables')}
              size="small"
              style={{ marginBottom: 16 }}
            >
              <VariableManager onInsertVariable={handleInsertVariable} />
            </Card>
          </Col>

          <Col xs={24} lg={24}>
            <FormItem
              label={t('content')}
              name="content"
              rules={[{ required: true, message: t('contentRequired') }]}
            >
              <TextArea
                ref={contentRef}
                rows={12}
                placeholder="Enter your template content here. Use {{variable_name}} for dynamic content."
                style={{ fontFamily: 'monospace' }}
              />
            </FormItem>
          </Col>
        </Row>
      </div>

      <div className="form_footer">
        <FormItem>
          <Button type="primary" htmlType="submit" loading={loading}>
            {isNew ? t('btnAdd') : t('btnUpdate')}
          </Button>
        </FormItem>
      </div>
    </Form>
  );
};

export default NotificationTemplateForm;
