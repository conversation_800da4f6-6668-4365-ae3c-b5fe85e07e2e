import {
  ArrowLeftOutlined,
  EditOutlined,
  EyeOutlined,
  SendOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Row,
  Space,
  Tag,
  Typography,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { getItem } from '../api';
import { TemplatePreview, TestNotification } from '../components';
import { MODULE, NOTIFICATION_TYPES } from '../config';
import { NotificationTemplateModal } from '../form';
import useNotificationTemplateStore from '../store';
import { NotificationTemplate } from '../type';

const { Title, Paragraph } = Typography;

const NotificationTemplateDetail: React.FC = () => {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { id } = useParams<{ id: string }>();
  const logger = ConsoleService.register(MODULE);
  const { loading, setLoading } = useNotificationTemplateStore();

  const [item, setItem] = useState<NotificationTemplate>();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [testVisible, setTestVisible] = useState(false);

  const fetchData = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const res = await getItem(parseInt(id));
      if (res.status.success) {
        setItem(res.data);
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      logger('Error fetching item:', error);
      message.error('Failed to fetch template details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [id]);

  const getChannelInfo = (channelCode: string) => {
    const channel = NOTIFICATION_TYPES.find((c) => c.value === channelCode);
    return channel || { icon: '📄', label: channelCode, value: channelCode };
  };

  const handleBack = () => {
    navigate('/notification-template');
  };

  const handleEdit = () => {
    setEditModalVisible(true);
  };

  const handlePreview = () => {
    setPreviewVisible(true);
  };

  const handleTest = () => {
    setTestVisible(true);
  };

  if (!item) {
    return <div>Loading...</div>;
  }

  const channel = getChannelInfo(item.channel_code);

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              Back to List
            </Button>
            <Title level={3} style={{ margin: 0 }}>
              {item.template_name}
            </Title>
          </Space>
        </Col>

        <Col span={24}>
          <Space>
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>
              {t('btnEdit')}
            </Button>
            <Button icon={<EyeOutlined />} onClick={handlePreview}>
              {t('btnPreview')}
            </Button>
            <Button icon={<SendOutlined />} onClick={handleTest}>
              {t('btnTest')}
            </Button>
          </Space>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="Template Information" loading={loading}>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label={t('template_id')}>
                {item.template_id}
              </Descriptions.Item>
              <Descriptions.Item label={t('template_code')}>
                <code
                  style={{
                    background: '#f5f5f5',
                    padding: '2px 6px',
                    borderRadius: '3px',
                  }}
                >
                  {item.template_code}
                </code>
              </Descriptions.Item>
              <Descriptions.Item label={t('channel')}>
                <Tag color="blue">
                  {channel.icon} {channel.label}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('is_active')}>
                <Tag color={item.is_active ? 'green' : 'red'}>
                  {item.is_active ? t('statusActive') : t('statusDeActive')}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label={t('created_at')}>
                {dayjs(item.created_at).format('DD/MM/YYYY HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('updated_at')}>
                {dayjs(item.updated_at).format('DD/MM/YYYY HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title={t('subject')} loading={loading}>
            <Paragraph
              copyable
              style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                fontFamily: 'monospace',
                minHeight: '60px',
              }}
            >
              {item.subject}
            </Paragraph>
          </Card>
        </Col>

        <Col span={24}>
          <Card title={t('content')} loading={loading}>
            <Paragraph
              copyable
              style={{
                background: '#f5f5f5',
                padding: '16px',
                borderRadius: '4px',
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap',
                minHeight: '200px',
                maxHeight: '400px',
                overflow: 'auto',
              }}
            >
              {item.content}
            </Paragraph>
          </Card>
        </Col>
      </Row>

      {/* Modals */}
      <NotificationTemplateModal
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onChange={(reload) => {
          if (reload) {
            fetchData();
          }
        }}
        id={id}
      />

      <TemplatePreview
        template={item}
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
      />

      <TestNotification
        template={item}
        visible={testVisible}
        onClose={() => setTestVisible(false)}
      />
    </div>
  );
};

export default NotificationTemplateDetail;
