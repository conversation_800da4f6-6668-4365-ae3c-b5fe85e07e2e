# Notification Template Module

A comprehensive React module for managing notification templates across multiple channels (email, SMS, push notifications, etc.).

## Features

- ✅ **Multi-channel support**: Email, SMS, Push, In-App, WebSocket, Telegram
- ✅ **Template management**: Create, read, update, delete templates
- ✅ **Variable system**: Dynamic content with simple `{{variable}}` syntax
- ✅ **Preview functionality**: Preview templates with sample data
- ✅ **Testing capabilities**: Send test notifications
- ✅ **Search and filtering**: Find templates by channel, status, etc.
- ✅ **Responsive design**: Works on desktop and mobile
- ✅ **Internationalization**: English and Vietnamese support

## File Structure

```
src/pages/notification-template/
├── api.ts                     # API service functions
├── config.ts                  # Configuration constants
├── type.ts                    # TypeScript interfaces
├── store.ts                   # Zustand state management
├── index.tsx                  # Main component with routing
├── README.md                  # This file
├── components/                # Reusable components
│   ├── index.tsx
│   ├── channel-selector.tsx   # Channel selection component
│   ├── variable-manager.tsx   # Variable management UI
│   ├── template-preview.tsx   # Template preview modal
│   ├── test-notification.tsx  # Test notification modal
│   └── select-notification-template.tsx # Template selector
├── form/                      # Form components
│   ├── index.tsx
│   ├── form.tsx              # Main form component
│   └── modal.tsx             # Form modal wrapper
├── list/                      # List view components
│   ├── index.tsx             # Main list component
│   └── search.tsx            # Search/filter component
├── detail/                    # Detail view
│   └── index.tsx             # Template detail page
└── i18n/                      # Internationalization
    ├── en.json               # English translations
    └── vi.json               # Vietnamese translations
```

## Usage

### Basic Import

```tsx
import NotificationTemplate from './pages/notification-template';

// In your router
<Route path="/notification-template/*" element={<NotificationTemplate />} />;
```

### Using Components

```tsx
import {
  ChannelSelector,
  VariableManager,
  SelectNotificationTemplate
} from './pages/notification-template';

// Channel selector
<ChannelSelector
  value={selectedChannel}
  onChange={setSelectedChannel}
/>

// Variable manager
<VariableManager
  onInsertVariable={(variable) => console.log(variable)}
/>

// Template selector
<SelectNotificationTemplate
  value={templateId}
  onChange={setTemplateId}
  channelFilter="email"
/>
```

### Using Store

```tsx
import useNotificationTemplateStore from './pages/notification-template/store';

const MyComponent = () => {
  const { loading, setLoading, selectedChannel, setSelectedChannel } =
    useNotificationTemplateStore();

  // Use store state and actions
};
```

### API Functions

```tsx
import {
  getItems,
  createItem,
  updateItem,
  deleteItem,
  testEmailTemplate,
} from './pages/notification-template/api';

// Get templates
const templates = await getItems({ limit: 20 });

// Create template
const newTemplate = await createItem({
  template_code: 'welcome_email',
  subject: 'Welcome!',
  content: 'Hello {{user_name}}!',
  is_active: true,
});

// Test email
await testEmailTemplate({
  tenant_id: 1,
  user_id: 1,
  to: '<EMAIL>',
  subject: 'Test',
  template_id: 'welcome_email',
  variables: { user_name: 'John' },
});
```

## Configuration

### Environment Variables

Make sure these API endpoints are configured:

```env
# Template management
REACT_APP_API_TEMPLATES=/api/admin/v1/notifications/templates

# Testing endpoints
REACT_APP_API_TEST_EMAIL=/api/admin/v1/notifications/test-email-template
REACT_APP_API_TEST_SMS=/api/admin/v1/notifications/test-sms
REACT_APP_API_TEST_PUSH=/api/admin/v1/notifications/test-push
```

### Authentication

The module expects these headers to be set by your API service:

```typescript
{
  'Authorization': 'Bearer <access_token>',
  'X-Tenant-ID': '<tenant_id>',
  'Content-Type': 'application/json'
}
```

## Template Variables

The module supports simple variable replacement using `{{variable_name}}` syntax:

### Common Variables

- `{{user_name}}` - User's name
- `{{app_name}}` - Application name
- `{{login_url}}` - Login URL
- `{{verification_url}}` - Email verification URL
- `{{reset_password_url}}` - Password reset URL
- `{{company_name}}` - Company name
- `{{support_email}}` - Support email

### Custom Variables

Users can add custom variables through the Variable Manager component.

## Supported Channels

1. **Email** - HTML email templates
2. **SMS** - Plain text messages
3. **Push** - Mobile push notifications
4. **In-App** - In-application notifications

## Testing

The module includes testing capabilities for:

- **Email testing**: Send test emails with sample data
- **SMS testing**: Send test SMS messages
- **Push testing**: Send test push notifications

## Internationalization

The module supports multiple languages:

- **English** (`en.json`)
- **Vietnamese** (`vi.json`)

Add new languages by creating additional JSON files in the `i18n/` directory.

## Dependencies

- React 18+
- Ant Design 5+
- React Router 6+
- React i18next
- Zustand
- Dayjs
- Lodash

## API Integration

The module is designed to work with the Notification Module API documented in `docs-api/Notification Module/`. Make sure your backend implements the required endpoints:

- `GET /api/admin/v1/notifications/templates` - List templates
- `POST /api/admin/v1/notifications/templates` - Create template
- `PUT /api/admin/v1/notifications/templates/:id` - Update template
- `DELETE /api/admin/v1/notifications/templates/:id` - Delete template
- `GET /api/admin/v1/notifications/templates/code/:code` - Get by code
- `POST /api/admin/v1/notifications/test-*` - Testing endpoints
