import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SendOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import { BackButton } from '../../../components/button';
import ConsoleService from '../../../services/console.service';
import { deleteItem, getItems } from '../api';
import { TemplatePreview, TestNotification } from '../components';
import { MODULE, NOTIFICATION_TYPES } from '../config';
import { NotificationTemplateModal } from '../form';
import useNotificationTemplateStore from '../store';
import { NotificationTemplate } from '../type';
import NotificationTemplateSearch from './search';

const NotificationTemplateList: React.FC = () => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading, setLoading } = useNotificationTemplateStore();

  const [data, setData] = useState<NotificationTemplate[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
  });
  const [searchParams, setSearchParams] = useState<any>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Modal states
  const [modalVisible, setModalVisible] = useState(false);
  const [editingId, setEditingId] = useState<string>();
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewTemplate, setPreviewTemplate] =
    useState<NotificationTemplate>();
  const [testVisible, setTestVisible] = useState(false);
  const [testTemplate, setTestTemplate] = useState<NotificationTemplate>();

  const fetchData = async (params: any = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        limit: params.pageSize || pagination.pageSize,
        ...searchParams,
        ...params,
      };

      const res = await getItems(queryParams);
      if (res.status.success) {
        setData(res.data);
        setPagination((prev) => ({
          ...prev,
          current: params.current || 1,
          total: res.meta?.total || res.data.length,
        }));
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      logger('Error fetching data:', error);
      message.error('Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleTableChange = (paginationConfig: any) => {
    fetchData({
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    });
  };

  const handleSearch = (params: any) => {
    setSearchParams(params);
    fetchData({ ...params, current: 1 });
  };

  const handleDelete = async (id: number) => {
    try {
      const res = await deleteItem(id);
      if (res.status.success) {
        message.success(t('deleteSuccess'));
        fetchData();
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      logger('Error deleting item:', error);
      message.error('Failed to delete template');
    }
  };

  const handleEdit = (id: number) => {
    setEditingId(id.toString());
    setModalVisible(true);
  };

  const handleAdd = () => {
    setEditingId('create');
    setModalVisible(true);
  };

  const handlePreview = (template: NotificationTemplate) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  const handleTest = (template: NotificationTemplate) => {
    setTestTemplate(template);
    setTestVisible(true);
  };

  const getChannelInfo = (channelCode: string) => {
    const channel = NOTIFICATION_TYPES.find((c) => c.value === channelCode);
    return channel || { icon: '📄', label: channelCode, value: channelCode };
  };

  const columns: ColumnsType<NotificationTemplate> = [
    {
      title: t('template_code'),
      dataIndex: 'template_code',
      key: 'template_code',
      width: 200,
      render: (text: string) => (
        <code
          style={{
            background: '#f5f5f5',
            padding: '2px 6px',
            borderRadius: '3px',
          }}
        >
          {text}
        </code>
      ),
    },
    {
      title: t('subject'),
      dataIndex: 'subject',
      key: 'subject',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: t('channel'),
      dataIndex: 'channel_code',
      key: 'channel_code',
      width: 120,
      render: (channelCode: string) => {
        const channel = getChannelInfo(channelCode);
        return (
          <Tag color="blue">
            {channel.icon} {channel.label}
          </Tag>
        );
      },
    },
    {
      title: t('is_active'),
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? t('statusActive') : t('statusDeActive')}
        </Tag>
      ),
    },
    {
      title: t('created_at'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm'),
    },
    {
      title: t('actions'),
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title={t('btnPreview')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record)}
            />
          </Tooltip>

          <Tooltip title={t('btnTest')}>
            <Button
              type="text"
              icon={<SendOutlined />}
              onClick={() => handleTest(record)}
            />
          </Tooltip>

          <Tooltip title={t('btnEdit')}>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record.template_id)}
            />
          </Tooltip>

          <Popconfirm
            title={t('deleteConfirm')}
            onConfirm={() => handleDelete(record.template_id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title={t('btnDelete')}>
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div className="bg-white p-4">
      <div className="mb-4">
        <BackButton destination="dashboard" />
      </div>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <NotificationTemplateSearch onSearch={handleSearch} />
        </Col>

        <Col span={24}>
          <div
            style={{
              marginBottom: 16,
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                {t('btnAdd')}
              </Button>
            </div>

            {selectedRowKeys.length > 0 && (
              <div>
                <span style={{ marginRight: 8 }}>
                  Selected {selectedRowKeys.length} items
                </span>
              </div>
            )}
          </div>
        </Col>

        <Col span={24}>
          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={data}
            rowKey="template_id"
            pagination={pagination}
            loading={loading}
            onChange={handleTableChange}
            scroll={{ x: 1000 }}
          />
        </Col>
      </Row>

      {/* Modals */}
      <NotificationTemplateModal
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onChange={(reload) => reload && fetchData()}
        id={editingId}
      />

      {previewTemplate && (
        <TemplatePreview
          template={previewTemplate}
          visible={previewVisible}
          onClose={() => setPreviewVisible(false)}
        />
      )}

      {testTemplate && (
        <TestNotification
          template={testTemplate}
          visible={testVisible}
          onClose={() => setTestVisible(false)}
        />
      )}
    </div>
  );
};

export default NotificationTemplateList;
