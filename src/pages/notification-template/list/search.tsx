import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Form, Input, Row, Select, Space } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE, NOTIFICATION_TYPES } from '../config';

interface NotificationTemplateSearchProps {
  onSearch: (params: any) => void;
}

const NotificationTemplateSearch: React.FC<NotificationTemplateSearchProps> = ({
  onSearch,
}) => {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const searchParams: any = {};

    if (values.search) {
      searchParams.search = values.search;
    }
    if (values.notification_type) {
      searchParams.notification_type = values.notification_type;
    }
    if (values.is_active !== undefined) {
      searchParams.is_active = values.is_active;
    }

    onSearch(searchParams);
  };

  const handleReset = () => {
    form.resetFields();
    onSearch({});
  };

  return (
    <Card size="small">
      <Form form={form} layout="vertical" onFinish={handleSearch}>
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item label="Search" name="search" style={{ marginBottom: 0 }}>
              <Input
                placeholder={t('searchPlaceholder')}
                allowClear
                onPressEnter={handleSearch}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item
              label={t('filterByChannel')}
              name="notification_type"
              style={{ marginBottom: 0 }}
            >
              <Select placeholder={t('allChannels')} allowClear>
                {NOTIFICATION_TYPES.map((type) => (
                  <Select.Option key={type.value} value={type.value}>
                    {type.icon} {t(`channel${type.label.replace(/\s+/g, '')}`)}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item
              label={t('filterByStatus')}
              name="is_active"
              style={{ marginBottom: 0 }}
            >
              <Select placeholder={t('allStatuses')} allowClear>
                <Select.Option value={true}>{t('statusActive')}</Select.Option>
                <Select.Option value={false}>
                  {t('statusDeActive')}
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Form.Item label=" " style={{ marginBottom: 0 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                >
                  Search
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  Reset
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default NotificationTemplateSearch;
