import { Select, Tag } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE, NOTIFICATION_TYPES } from '../config';

interface ChannelSelectorProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

const ChannelSelector: React.FC<ChannelSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder,
}) => {
  const { t } = useTranslation(MODULE);

  const handleChange = (selectedValue: string) => {
    onChange?.(selectedValue);
  };

  return (
    <Select
      value={value}
      onChange={handleChange}
      disabled={disabled}
      placeholder={placeholder || t('filterByChannel')}
      style={{ width: '100%' }}
      allowClear
    >
      {NOTIFICATION_TYPES.map((channel) => (
        <Select.Option key={channel.value} value={channel.value}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span>{channel.icon}</span>
            <span>{t(`channel${channel.label.replace(/\s+/g, '')}`)}</span>
            <Tag size="small" color="blue">
              {channel.value}
            </Tag>
          </div>
        </Select.Option>
      ))}
    </Select>
  );
};

export default ChannelSelector;
