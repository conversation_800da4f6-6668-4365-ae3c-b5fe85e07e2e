import { CopyOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, Input, Menu, Space, Tag, Tooltip } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { COMMON_VARIABLES, MODULE } from '../config';
import { TemplateVariable } from '../type';

interface VariableManagerProps {
  onInsertVariable?: (variable: string) => void;
  showInsertButtons?: boolean;
}

const VariableManager: React.FC<VariableManagerProps> = ({
  onInsertVariable,
  showInsertButtons = true,
}) => {
  const { t } = useTranslation(MODULE);
  const [customVariables, setCustomVariables] = useState<TemplateVariable[]>(
    [],
  );
  const [newVariableName, setNewVariableName] = useState('');

  const handleInsertVariable = (variableName: string) => {
    const variableTag = `{{${variableName}}}`;
    onInsertVariable?.(variableTag);
  };

  const handleCopyVariable = (variableName: string) => {
    const variableTag = `{{${variableName}}}`;
    navigator.clipboard.writeText(variableTag);
  };

  const addCustomVariable = () => {
    if (newVariableName.trim()) {
      const newVariable: TemplateVariable = {
        name: newVariableName.trim(),
        description: 'Custom variable',
        example: 'Custom value',
      };
      setCustomVariables([...customVariables, newVariable]);
      setNewVariableName('');
    }
  };

  const removeCustomVariable = (index: number) => {
    const updated = customVariables.filter((_, i) => i !== index);
    setCustomVariables(updated);
  };

  const renderVariableTag = (
    variable: TemplateVariable,
    isCustom = false,
    index?: number,
  ) => {
    const menu = (
      <Menu>
        <Menu.Item
          key="copy"
          icon={<CopyOutlined />}
          onClick={() => handleCopyVariable(variable.name)}
        >
          Copy variable
        </Menu.Item>
        {showInsertButtons && (
          <Menu.Item
            key="insert"
            icon={<PlusOutlined />}
            onClick={() => handleInsertVariable(variable.name)}
          >
            {t('insertVariable')}
          </Menu.Item>
        )}
        {isCustom && (
          <Menu.Item
            key="remove"
            danger
            onClick={() => removeCustomVariable(index!)}
          >
            {t('removeVariable')}
          </Menu.Item>
        )}
      </Menu>
    );

    return (
      <Dropdown overlay={menu} trigger={['contextMenu']} key={variable.name}>
        <Tooltip
          title={
            <div>
              <div>
                <strong>{variable.description}</strong>
              </div>
              <div>Example: {variable.example}</div>
              <div>Usage: {`{{${variable.name}}}`}</div>
            </div>
          }
        >
          <Tag
            color={variable.required ? 'red' : 'blue'}
            style={{ cursor: 'pointer', margin: '2px' }}
            onClick={() =>
              showInsertButtons && handleInsertVariable(variable.name)
            }
          >
            {`{{${variable.name}}}`}
            {variable.required && <span style={{ color: 'red' }}>*</span>}
          </Tag>
        </Tooltip>
      </Dropdown>
    );
  };

  return (
    <div
      style={{ padding: '12px', background: '#f6f8fa', borderRadius: '6px' }}
    >
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0' }}>{t('commonVariables')}</h4>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
          {COMMON_VARIABLES.map((variable) => renderVariableTag(variable))}
        </div>
      </div>

      {customVariables.length > 0 && (
        <div style={{ marginBottom: '12px' }}>
          <h4 style={{ margin: '0 0 8px 0' }}>Custom Variables</h4>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {customVariables.map((variable, index) =>
              renderVariableTag(variable, true, index),
            )}
          </div>
        </div>
      )}

      <div>
        <h4 style={{ margin: '0 0 8px 0' }}>{t('addVariable')}</h4>
        <Space.Compact style={{ width: '100%' }}>
          <Input
            placeholder="Enter variable name (e.g., custom_field)"
            value={newVariableName}
            onChange={(e) => setNewVariableName(e.target.value)}
            onPressEnter={addCustomVariable}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addCustomVariable}
          >
            Add
          </Button>
        </Space.Compact>
      </div>

      <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
        <div>• Right-click on variables for more options</div>
        <div>• Red variables are required, blue are optional</div>
        <div>• Click variables to insert them into template</div>
      </div>
    </div>
  );
};

export default VariableManager;
