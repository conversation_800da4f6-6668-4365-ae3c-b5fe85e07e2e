import { SendOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Modal, Space } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { testEmailTemplate, testPushNotification, testSMS } from '../api';
import { COMMON_VARIABLES, MODULE } from '../config';
import useNotificationTemplateStore from '../store';
import { NotificationTemplate } from '../type';

interface TestNotificationProps {
  template: NotificationTemplate;
  visible: boolean;
  onClose: () => void;
}

const TestNotification: React.FC<TestNotificationProps> = ({
  template,
  visible,
  onClose,
}) => {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { addTestResult } = useNotificationTemplateStore();

  const handleTestEmail = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      const testData = {
        tenant_id: 1, // This should come from auth context
        user_id: 1, // This should come from auth context
        to: values.recipient_email,
        subject: values.test_subject || template.subject,
        template_id: template.template_code,
        variables: {
          user_name: values.user_name || 'Test User',
          app_name: values.app_name || 'WNCMS',
          login_url: values.login_url || 'https://app.wncms.com/login',
          company_name: values.company_name || 'WNCMS Corp',
          ...values.custom_variables,
        },
      };

      const response = await testEmailTemplate(testData);
      if (response.status === 'ok') {
        message.success(t('testEmailSuccess'));
        addTestResult({
          type: 'email',
          template: template.template_code,
          recipient: values.recipient_email,
          taskId: response.task_id,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error: any) {
      message.error(error.message || t('testEmailError'));
    } finally {
      setLoading(false);
    }
  };

  const handleTestSMS = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      const testData = {
        tenant_id: 1,
        user_id: 1,
        to: values.recipient_phone,
        message: values.test_message || template.content,
      };

      const response = await testSMS(testData);
      if (response.status === 'ok') {
        message.success(t('testSMSSuccess'));
        addTestResult({
          type: 'sms',
          template: template.template_code,
          recipient: values.recipient_phone,
          taskId: response.task_id,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error: any) {
      message.error(error.message || t('testSMSError'));
    } finally {
      setLoading(false);
    }
  };

  const handleTestPush = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      const testData = {
        tenant_id: 1,
        user_id: 1,
        title: values.test_title || template.subject,
        body: values.test_body || template.content,
        data: {
          template_id: template.template_code,
          action: 'test',
          ...values.custom_data,
        },
      };

      const response = await testPushNotification(testData);
      if (response.status === 'ok') {
        message.success(t('testPushSuccess'));
        addTestResult({
          type: 'push',
          template: template.template_code,
          recipient: 'Current User',
          taskId: response.task_id,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error: any) {
      message.error(error.message || t('testPushError'));
    } finally {
      setLoading(false);
    }
  };

  const renderEmailTest = () => (
    <Form form={form} layout="vertical">
      <Form.Item
        label={t('recipientEmail')}
        name="recipient_email"
        rules={[
          { required: true, message: 'Please enter recipient email' },
          { type: 'email', message: 'Please enter valid email' },
        ]}
      >
        <Input placeholder="<EMAIL>" />
      </Form.Item>

      <Form.Item label="Test Subject" name="test_subject">
        <Input placeholder={template.subject} />
      </Form.Item>

      {COMMON_VARIABLES.slice(0, 4).map((variable) => (
        <Form.Item
          key={variable.name}
          label={variable.description}
          name={variable.name}
        >
          <Input placeholder={variable.example} />
        </Form.Item>
      ))}

      <Form.Item>
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleTestEmail}
          loading={loading}
          block
        >
          {t('testEmail')}
        </Button>
      </Form.Item>
    </Form>
  );

  const renderSMSTest = () => (
    <Form form={form} layout="vertical">
      <Form.Item
        label={t('recipientPhone')}
        name="recipient_phone"
        rules={[{ required: true, message: 'Please enter recipient phone' }]}
      >
        <Input placeholder="+84123456789" />
      </Form.Item>

      <Form.Item label={t('testMessage')} name="test_message">
        <Input.TextArea rows={3} placeholder={template.content} />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleTestSMS}
          loading={loading}
          block
        >
          {t('testSMS')}
        </Button>
      </Form.Item>
    </Form>
  );

  const renderPushTest = () => (
    <Form form={form} layout="vertical">
      <Form.Item label={t('testTitle')} name="test_title">
        <Input placeholder={template.subject} />
      </Form.Item>

      <Form.Item label={t('testBody')} name="test_body">
        <Input.TextArea rows={3} placeholder={template.content} />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleTestPush}
          loading={loading}
          block
        >
          {t('testPush')}
        </Button>
      </Form.Item>
    </Form>
  );

  const getTestContent = () => {
    switch (template.channel_code) {
      case 'email':
        return renderEmailTest();
      case 'sms':
        return renderSMSTest();
      case 'push':
        return renderPushTest();
      default:
        return <div>Test not available for this channel type.</div>;
    }
  };

  return (
    <Modal
      title={
        <Space>
          <SendOutlined />
          {t('btnTest')} - {template.template_name}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('btnCancel')}
        </Button>,
      ]}
      destroyOnClose
    >
      {getTestContent()}
    </Modal>
  );
};

export default TestNotification;
