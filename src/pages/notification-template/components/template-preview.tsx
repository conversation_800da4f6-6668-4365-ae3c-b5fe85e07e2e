import { EyeOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Modal, Space, Spin, Tabs } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { COMMON_VARIABLES, MODULE } from '../config';
import { NotificationTemplate } from '../type';

interface TemplatePreviewProps {
  template: NotificationTemplate;
  visible: boolean;
  onClose: () => void;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  template,
  visible,
  onClose,
}) => {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const [previewSubject, setPreviewSubject] = useState('');

  const generatePreview = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // Simple variable replacement for preview
      let processedContent = template.content;
      let processedSubject = template.subject;

      Object.keys(values).forEach((key) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        processedContent = processedContent.replace(regex, values[key] || '');
        processedSubject = processedSubject.replace(regex, values[key] || '');
      });

      setPreviewContent(processedContent);
      setPreviewSubject(processedSubject);
    } catch (error) {
      message.error('Please fill in all required fields');
    } finally {
      setLoading(false);
    }
  };

  const renderVariableInput = (variable: any) => {
    return (
      <Form.Item
        key={variable.name}
        label={variable.description}
        name={variable.name}
        rules={
          variable.required
            ? [
                {
                  required: true,
                  message: `${variable.description} is required`,
                },
              ]
            : []
        }
        tooltip={`Example: ${variable.example}`}
      >
        <Input placeholder={variable.example} />
      </Form.Item>
    );
  };

  const renderPreview = () => {
    if (!previewContent && !previewSubject) {
      return (
        <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
          {t('noPreviewAvailable')}
          <br />
          <Button
            type="primary"
            onClick={generatePreview}
            style={{ marginTop: '16px' }}
          >
            {t('previewTemplate')}
          </Button>
        </div>
      );
    }

    return (
      <div>
        {previewSubject && (
          <div style={{ marginBottom: '16px' }}>
            <h4>Subject:</h4>
            <div
              style={{
                padding: '8px 12px',
                background: '#f0f0f0',
                borderRadius: '4px',
                fontWeight: 'bold',
              }}
            >
              {previewSubject}
            </div>
          </div>
        )}

        <h4>Content:</h4>
        <div
          style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            minHeight: '200px',
            overflow: 'auto',
          }}
        >
          {template.channel_code === 'email' ? (
            <iframe
              srcDoc={previewContent}
              style={{ width: '100%', height: '300px', border: 'none' }}
              title="Email Preview"
            />
          ) : (
            <div style={{ padding: '16px', whiteSpace: 'pre-wrap' }}>
              {previewContent}
            </div>
          )}
        </div>
      </div>
    );
  };

  const tabItems = [
    {
      key: 'variables',
      label: t('variables'),
      children: (
        <Form form={form} layout="vertical">
          {COMMON_VARIABLES.map(renderVariableInput)}
          <Form.Item>
            <Button type="primary" onClick={generatePreview} loading={loading}>
              {t('previewTemplate')}
            </Button>
          </Form.Item>
        </Form>
      ),
    },
    {
      key: 'preview',
      label: t('templatePreview'),
      children: loading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
        </div>
      ) : (
        renderPreview()
      ),
    },
  ];

  return (
    <Modal
      title={
        <Space>
          <EyeOutlined />
          {t('previewTemplate')} - {template.template_name}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('btnCancel')}
        </Button>,
      ]}
      destroyOnClose
    >
      <Tabs items={tabItems} />
    </Modal>
  );
};

export default TemplatePreview;
