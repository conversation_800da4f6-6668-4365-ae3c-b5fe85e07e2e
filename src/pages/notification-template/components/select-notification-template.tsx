import { Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getOptions } from '../api';
import { MODULE } from '../config';

interface SelectNotificationTemplateProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  channelFilter?: string;
}

const SelectNotificationTemplate: React.FC<SelectNotificationTemplateProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder,
  channelFilter,
}) => {
  const { t } = useTranslation(MODULE);
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchOptions();
  }, [channelFilter]);

  const fetchOptions = async () => {
    try {
      setLoading(true);
      const response = await getOptions();
      if (response.status.success) {
        let filteredOptions = response.data;

        // Filter by channel if specified
        if (channelFilter) {
          filteredOptions = response.data.filter(
            (option: any) => option.channel_code === channelFilter,
          );
        }

        setOptions(filteredOptions);
      }
    } catch (error) {
      console.error('Error fetching template options:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Select
      value={value}
      onChange={onChange}
      disabled={disabled}
      placeholder={placeholder || 'Select notification template'}
      style={{ width: '100%' }}
      loading={loading}
      showSearch
      filterOption={(input, option) =>
        (option?.children as string)
          ?.toLowerCase()
          .includes(input.toLowerCase())
      }
      allowClear
    >
      {options.map((option) => (
        <Select.Option key={option.value} value={option.value}>
          {option.label}
          {option.channel_code && (
            <span style={{ color: '#999', marginLeft: 8 }}>
              ({option.channel_code})
            </span>
          )}
        </Select.Option>
      ))}
    </Select>
  );
};

export default SelectNotificationTemplate;
