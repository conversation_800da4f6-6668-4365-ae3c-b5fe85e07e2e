import { FolderOutlined, PictureOutlined } from '@ant-design/icons';
import { Badge, Menu } from 'antd';
import { useTranslation } from 'react-i18next';
import { BackButton } from '../../../components/button';
import '../assets/styles.scss';
import List from './list';

export default function Index() {
  const { t } = useTranslation('editor-media');
  const handleMenuClick = () => {};

  return (
    <div>
      <div className="p-4">
        <BackButton destination="dashboard" />
      </div>
      <div className="flex">
        <div
          style={{
            width: '250px',
            position: 'sticky',
            top: '0',
            height: '100vh',
            overflowY: 'auto',
          }}
        >
          <Menu
            theme="light"
            mode="inline"
            defaultSelectedKeys={['1']}
            items={[
              {
                key: '1',
                label: (
                  <div className="flex justify-between items-center">
                    <span className="flex items-center">
                      <PictureOutlined className="mr-2" />
                      {t('media.allMediaFiles')}
                    </span>
                    <Badge count={0} offset={[10, 0]} />
                  </div>
                ),
              },
              {
                key: '2',
                label: (
                  <div className="flex justify-between items-center">
                    <span className="flex items-center">
                      <FolderOutlined className="mr-2" />
                      {t('media.folder')}
                    </span>
                    <Badge count={0} offset={[10, 0]} />
                  </div>
                ),
              },
            ]}
            onClick={handleMenuClick}
            style={{ height: '100%' }}
          />
        </div>

        <div style={{ flex: 1, paddingLeft: '14px' }}>
          <List />
        </div>
      </div>
    </div>
  );
}
