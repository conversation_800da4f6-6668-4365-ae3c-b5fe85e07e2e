import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { deleteItem, getItems, uploadMedia } from '../api';
import '../assets/styles.scss';
import EditorMedia from '../components/editor-media';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useMediaStore from '../store';
import { MEDIA_TYPE_LABELS, MediaType } from '../type';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useMediaStore();
  const paginationDefault = {
    before: null,
    after: null,
    limit: 10,
  };
  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<MediaType[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [showEditorMedia, setShowEditorMedia] = useState(false);

  async function fetchData(payload?: any) {
    const params = {
      ...query,
      ...filters,
      ...payload,
    };
    delete params.total;
    // //changeBrowserLocation(navigate, pathname, params);
    const response = await getItems(params);
    if (response.status.success) {
      setItems(response.data);
      setNextCursor(response.meta.after);
    } else {
      message.error(response.status.message);
    }
  }

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    // //changeBrowserLocation(navigate, pathname, values);

    setFilters(values);
    fetchData(values);
  };

  useEffectOnce(() => {
    fetchData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('media.deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handleUpload = () => {
    // Xử lý upload file
    const input = document.createElement('input');
    input.type = 'file';
    input.onchange = async (e: any) => {
      const file = e.target.files[0];
      if (file) {
        try {
          const response = await uploadMedia(file);
          if (response.status.success) {
            message.success(t('media.uploadSuccess'));
            fetchData();
          } else {
            message.error(response.status.message);
          }
        } catch (error) {
          message.error(t('media.uploadError'));
        }
      }
    };
    input.click();
  };

  const handleEditorMediaClose = () => {
    setShowEditorMedia(false);
    fetchData();
  };

  const handleEditorMediaChange = (selectedItems: MediaType[]) => {
    console.log('Selected media items:', selectedItems);
    // Xử lý các media được chọn
  };

  const columns = [
    {
      title: t('media.fileName'),
      dataIndex: 'file_name',
      key: 'file_name',
      render: (dom: any, record: any) => (
        <div
          className="flex justify-start max-w-[400px]"
          onClick={() => navigate(`/${MODULE}/${record.id}`)}
        >
          <div className="max-w-[100px] mr-2">
            {record.media_type === 'image' ? (
              <img src={record.url} alt={record.alt_text || record.file_name} />
            ) : (
              <div className="file-icon">{record.extension}</div>
            )}
          </div>
          <div className="max-w-[300px]">
            <div className="text-blue-600 cursor-pointer">
              {record.original_name || record.file_name}
            </div>
            <div>
              <Tag color="blue">{MEDIA_TYPE_LABELS[record.media_type]}</Tag>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: t('media.fileInfo'),
      dataIndex: 'info',
      key: 'info',
      render: (dom: any, record: any) => (
        <div>
          <div>
            {t('media.size')}: {(record.size / 1024).toFixed(2)} KB
          </div>
          <div>
            {t('media.fileType')}: {record.mime_type}
          </div>
          {record.metadata &&
            record.metadata.width &&
            record.metadata.height && (
              <div>
                {t('media.dimensions', {
                  width: record.metadata.width,
                  height: record.metadata.height,
                })}
              </div>
            )}
        </div>
      ),
    },
    {
      title: t('media.createdAt'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (dom: any, record: any) => {
        const formatDate = (dateStr: string | undefined) => {
          if (!dateStr) return t('media.noData');
          const date = new Date(dateStr);
          return date.toLocaleString('vi-VN');
        };

        return (
          <div className="flex flex-col">
            <div className="mb-2 flex flex-row">
              <div className="mr-2">{t('media.createdAt')}:</div>
              <div className="">{formatDate(record.created_at)}</div>
            </div>
            <div className="mb-2 flex flex-row">
              <div className="mr-2">{t('media.updatedAt')}:</div>
              <div className="">{formatDate(record.updated_at)}</div>
            </div>
          </div>
        );
      },
    },
    {
      title: t('media.actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('media.deleteConfirm')}
                    onConfirm={() => handleDelete(record.id.toString())}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('media.btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('media.btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">{t('media.module')}</div>
        <div className="gap-4">
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={() => setShowEditorMedia(true)}
            style={{ marginRight: '10px' }}
          >
            {t('media.btnMediaManager')}
          </Button>
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={handleUpload}
            style={{ marginRight: '10px' }}
          >
            {t('media.btnUpload')}
          </Button>
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('media.btnAdd')}
          </Button>
        </div>
      </div>

      {/* EditorMedia component */}
      <EditorMedia
        onClose={handleEditorMediaClose}
        onChange={handleEditorMediaChange}
        showMedia={showEditorMedia}
        initialMediaType="image"
      />

      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        />

        <div className="p-4">
          <CursorPagination
            total={0}
            afterKey={afterKey}
            isNext={isNext}
            isBack={isBack}
            goNext={goNext}
            goBack={goBack}
          />
        </div>
      </Space>

      {showModal && (
        <ModalForm
          visible={showModal}
          onCancel={handleModal}
          id={idCurrent}
        ></ModalForm>
      )}
    </div>
  );
}
