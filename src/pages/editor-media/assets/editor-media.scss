// Media Manager Modal Styles
.media-manager-modal {
  .ant-modal-content {
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .ant-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f1f1f;
  }

  .ant-modal-close {
    color: #999;
    transition: color 0.2s;

    &:hover {
      color: #333;
    }
  }

  .media-sider {
    border-right: 1px solid #f0f0f0;
    background-color: #fafafa;
    border-radius: 4px 0 0 4px;
    padding: 0;
    overflow: hidden;

    .media-type-tabs {
      .ant-tabs-nav {
        width: 100%;
        margin: 0;
      }

      .ant-tabs-tab {
        margin: 0;
        padding: 10px 12px;
        justify-content: flex-start;
        transition: all 0.2s ease;
        border-radius: 0;
        font-weight: 500;
        width: 100%;

        &:hover {
          color: #1890ff;
          background-color: rgba(24, 144, 255, 0.05);
        }

        .anticon {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      .ant-tabs-tab-active {
        background-color: #e6f7ff;
        font-weight: 600;
        color: #1890ff;
      }

      .ant-tabs-ink-bar {
        left: 0;
        width: 3px;
        background-color: #1890ff;
      }
    }
  }

  .media-content {
    background-color: #fff;
    border-radius: 0 4px 4px 0;

    .media-content-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fafafa;

      .ant-breadcrumb {
        margin-bottom: 0;

        .ant-breadcrumb-link {
          a {
            color: #1890ff;
            transition: color 0.2s;

            &:hover {
              color: #40a9ff;
              text-decoration: underline;
            }
          }
        }

        .ant-breadcrumb-separator {
          color: #bfbfbf;
        }
      }

      .ant-input-search {
        border-radius: 4px;
        overflow: hidden;

        .ant-input {
          border-color: #d9d9d9;

          &:hover,
          &:focus {
            border-color: #40a9ff;
          }
        }

        .ant-input-search-button {
          background-color: #1890ff;
          border-color: #1890ff;

          &:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
          }
        }
      }
    }
  }

  // Media content container styling
  .media-content-container {
    padding: 16px 0;

    // Folder styling
    .folder-card {
      transition: all 0.3s ease;
      border-radius: 8px;
      border: 1px solid #f0f0f0;
      overflow: hidden;
      height: 100%;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        border-color: #d9d9d9;
      }

      .ant-card-body {
        padding: 16px;
      }

      .anticon {
        font-size: 28px;
        color: #faad14;
        margin-right: 12px;
        transition: transform 0.3s ease;
      }

      &:hover .anticon {
        transform: scale(1.1);
      }

      .ant-typography {
        color: #262626;
        font-weight: 500;
      }
    }
    // Media card styling
    .media-card {
      transition: all 0.3s ease;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #f0f0f0;
      height: 100%;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        border-color: #d9d9d9;
      }

      &.selected {
        border: 2px solid #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

        .ant-card-cover {
          border-bottom: 1px solid #1890ff;
        }
      }

      .ant-card-cover {
        height: 140px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;
        overflow: hidden;
        border-bottom: 1px solid #f0f0f0;
      }

      // Image container with edit overlay
      .image-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 10;

          .edit-button {
            background-color: #1890ff;
            border-color: #1890ff;
            border-radius: 6px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
            transition: all 0.3s ease;

            &:hover {
              background-color: #40a9ff;
              border-color: #40a9ff;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
            }

            .anticon {
              margin-right: 4px;
            }
          }
        }

        &:hover {
          .image-overlay {
            opacity: 1;
          }

          img {
            transform: scale(1.05);
          }
        }
      }

      .media-preview {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          height: 100%;
          width: 100%;
          object-fit: cover;
        }

        .media-type-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          color: #bfbfbf;

          .anticon {
            font-size: 36px;
          }
        }

        .selected-indicator {
          position: absolute;
          top: 8px;
          right: 8px;
          background-color: #1890ff;
          color: white;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);

          .anticon {
            font-size: 14px;
          }
        }
      }

      .ant-card-body {
        padding: 12px;
      }

      .ant-card-meta-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ant-card-meta-description {
        font-size: 12px;
        color: #8c8c8c;
      }
    }

    // Load more button
    .text-center {
      margin-top: 20px;

      .ant-btn {
        border-radius: 4px;
        height: 36px;
        padding: 0 16px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // Modal footer styling
  .ant-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    border-radius: 0 0 8px 8px;

    .flex {
      display: flex;
      justify-content: space-between;
      align-items: center;

      > div:first-child {
        color: #595959;
        font-size: 14px;
      }

      .ant-btn {
        height: 36px;
        padding: 0 16px;
        font-weight: 500;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }

        &.ant-btn-primary {
          background-color: #1890ff;
          border-color: #1890ff;

          &:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
          }

          &:disabled {
            background-color: #d9d9d9;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
            box-shadow: none;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }
  }

  // Empty state styling
  .ant-empty {
    padding: 40px 0;

    .ant-empty-image {
      height: 100px;
    }

    .ant-empty-description {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  // Create folder modal styling
  .ant-modal {
    &.folder-modal {
      .ant-modal-content {
        border-radius: 8px;
        overflow: hidden;
      }

      .ant-modal-header {
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-modal-body {
        padding: 24px;
      }

      .ant-form-item-label > label {
        font-weight: 500;
        color: #262626;
      }

      .ant-input {
        height: 40px;
        border-radius: 4px;

        &:focus,
        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }

      .ant-btn {
        height: 36px;
        padding: 0 16px;
        border-radius: 4px;
        font-weight: 500;

        &.ant-btn-primary {
          background-color: #1890ff;
          border-color: #1890ff;

          &:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
          }
        }
      }
    }
  }

  // Fix for tabs styling to remove whitespace
  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
    margin: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-nav-list {
    width: 100%;
  }

  .ant-tabs-content-holder {
    border-left: none !important;
  }

  // Responsive design
  @media (max-width: 768px) {
    .media-sider {
      display: flex;
      flex-direction: column;
      width: 100% !important;
      max-width: 100% !important;

      .media-type-tabs {
        flex-direction: row;

        .ant-tabs-tab {
          padding: 12px !important;

          .anticon {
            margin-right: 6px;
            font-size: 16px;
          }
        }
      }
    }

    .media-content-container {
      .ant-row {
        margin: 0 -8px !important;

        .ant-col {
          padding: 0 8px !important;
        }
      }

      .folder-card,
      .media-card {
        margin-bottom: 16px;
      }
    }

    .flex {
      flex-direction: column;

      > div {
        width: 100%;
        margin-bottom: 16px;
      }
    }
  }

  // Media Content
  .MediaContent {
    flex: 1;
    padding: 10px;
    overflow-y: auto; // Enable scroll for content
    display: flex;
    flex-direction: column;

    .FoldersContainer {
      margin-bottom: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 16px;

      .FolderCard {
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        height: 100%;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .ant-card-body {
          padding: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
        }

        .FolderIcon {
          font-size: 32px;
          margin-bottom: 8px;
          color: #faad14;
        }

        .FolderTitle {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 500;
        }

        .FolderMeta {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }
    }

    .MediaContainer {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 16px;

      .MediaCard {
        position: relative;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .MediaOverlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background-color: rgba(0, 0, 0, 0.5);
          opacity: 0;
          transition: opacity 0.2s;

          &:hover {
            opacity: 1;
          }

          .MediaInfo {
            color: white;
            text-align: center;
            padding: 0 8px;

            .MediaTitle {
              font-weight: 500;
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            .MediaMeta {
              font-size: 12px;
            }
          }
        }

        .MediaThumb {
          width: 100%;
          height: 120px;
          object-fit: cover;
          display: block;
        }

        .MediaTitle {
          padding: 8px;
          font-size: 13px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .SelectedIndicator {
          position: absolute;
          top: 8px;
          right: 8px;
          background-color: #52c41a;
          color: white;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .EmptyContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
      color: #999;

      .EmptyText {
        margin-top: 16px;
        text-align: center;
      }
    }

    .LoadMoreContainer {
      display: flex;
      justify-content: center;
      margin-top: 20px;
      padding-bottom: 20px;
    }
  }

  // Modal footer
  .MediaFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-top: 1px solid #e6e6e7;
    background-color: #fafafb;
    flex-shrink: 0; // Prevent footer from shrinking

    .SelectedCount {
      font-size: 14px;
      color: #666;
    }

    .FooterButtons {
      display: flex;
      gap: 10px;

      button {
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;

        &.CancelButton {
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          color: #333;

          &:hover {
            background-color: #e8e8e8;
          }
        }

        &.SelectButton {
          background-color: #006eff;
          border: none;
          color: white;

          &:hover {
            background-color: #0055cc;
          }
        }
      }
    }
  }

  // Add responsive styles for smaller screens
  @media (max-width: 768px) {
    .MediaContainer {
      width: 95% !important;

      .MenuLeft {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
      }

      .ant-tabs-tab {
        padding: 8px 12px !important;
      }

      .MediaContent {
        .flex {
          flex-direction: column;

          > div {
            width: 100%;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
