/* Special fixes for media manager tabs */

/* Style for the tab labels */
.tab-label {
  display: flex;
  align-items: center;
  width: 100%;

  .anticon {
    margin-right: 8px;
    font-size: 16px;
  }

  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.media-manager-modal {
  /* Tab fixes to remove white space */
  .ant-tabs-left {
    /* Remove any extra padding in the tabs container */
    > .ant-tabs-nav {
      padding: 0;
      margin: 0;
      width: 100%;

      .ant-tabs-nav-list {
        width: 100%;
      }
    }

    /* Fix each tab item */
    .ant-tabs-tab {
      margin: 0 !important;
      padding: 10px 12px !important;
      width: 100%;
      display: flex;
      align-items: center;
      border-radius: 0;

      &.ant-tabs-tab-active {
        background-color: #e6f7ff;
      }

      .ant-tabs-tab-btn {
        width: 100%;

        span {
          display: flex;
          align-items: center;

          .anticon {
            margin-right: 8px;
          }
        }
      }
    }

    /* Remove the default tab padding */
    .ant-tabs-content-holder {
      padding: 0 !important;
      border-left: none !important;
    }
  }

  /* Fix for the sidebar container */
  .media-sider {
    padding: 0 !important;

    .ant-tabs {
      height: 100%;
    }

    /* Additional fixes for tab rendering */
    .ant-tabs-nav-list {
      margin: 0 !important;
      padding: 0 !important;
      width: 100% !important;
    }

    .ant-tabs-tab-btn {
      width: 100%;
      text-align: left;
      padding: 0;
    }

    /* Remove any border that might cause extra spacing */
    .ant-tabs-nav::before {
      border: none !important;
    }

    /* Fix for spacing between tab items */
    .ant-tabs-tab {
      padding: 10px 12px !important;
      margin: 0 !important;
    }
  }

  /* Fix for ant design Tab component - force no padding */
  div.ant-tabs-nav-wrap {
    padding: 0 !important;
  }

  .ant-tabs-content {
    height: 100%;
  }
}
