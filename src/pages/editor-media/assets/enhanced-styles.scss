/* Enhanced styling for the media manager */

.media-manager-modal {
  /* Main container styling */
  .media-content-container {
    .folder-card,
    .media-card {
      transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;
      border-radius: 8px;
      overflow: hidden;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
      }
    }

    /* Folder cards */
    .folder-card {
      .ant-card-body {
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 12px;
          font-size: 28px;
          color: #faad14;
        }

        .ant-typography {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    /* Media cards */
    .media-card {
      border: 1px solid #f0f0f0;

      &.selected {
        border: 2px solid #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      .media-preview {
        height: 140px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .media-type-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;

          .anticon {
            font-size: 36px;
            color: #bfbfbf;
          }
        }

        .selected-indicator {
          position: absolute;
          top: 8px;
          right: 8px;
          background-color: #1890ff;
          color: #fff;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }
      }

      .ant-card-meta-title {
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ant-card-meta-description {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  } /* Tab styles */
  .media-type-tabs {
    .ant-tabs-tab {
      padding: 12px 16px;
      transition: all 0.2s ease;
      margin: 0;

      &:hover {
        color: #1890ff;
        background-color: rgba(24, 144, 255, 0.05);
      }

      &.ant-tabs-tab-active {
        background-color: #e6f7ff;
        font-weight: 500;
      }

      .anticon {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    /* Remove extra space in tabs nav */
    .ant-tabs-nav {
      width: 100%;
      margin: 0;
    }

    .ant-tabs-tab-btn {
      padding: 0;
      display: flex;
      align-items: center;
      width: 100%;
    }

    /* Remove any extra margins from tab content */
    .ant-tabs-content-holder {
      margin: 0;
      padding: 0;
    }
  }

  /* Header area styles */
  .media-content-header {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;

    .ant-breadcrumb {
      a {
        color: #1890ff;
        transition: color 0.2s;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  /* Button styling */
  .text-center {
    .ant-btn {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  /* Modal footer */
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;

    .ant-btn {
      border-radius: 4px;
      transition: all 0.3s ease;

      &.ant-btn-primary {
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
        }
      }
    }
  }
}

/* Folder modal styling */
.folder-modal {
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-form-item-label > label {
    font-weight: 500;
  }

  .ant-input {
    border-radius: 4px;

    &:focus {
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .media-manager-modal {
    .media-content-container {
      .ant-row {
        margin: 0 -8px;

        .ant-col {
          padding: 0 8px;
        }
      }
    }
  }
}
