import { AxiosRequestConfig } from 'axios';
import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { Folder, MediaQueryParams, MediaType, MediaUpdateData } from './type';

export const MODULE = 'media';
export const MODULE_NAME = 'Media Manager';
export const MODULE_POPUP = true;

const url = `/api/admin/v1/media`;
const folderUrl = `/api/admin/v1/media/folders`;

export async function getItems(
  params: MediaQueryParams,
): Promise<ApiResponsePaginationCursor<MediaType[]>> {
  const config: AxiosRequestConfig = { params };
  const response = await apiService.get<
    ApiResponsePaginationCursor<MediaType[]>
  >(url, config);
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<MediaType>> {
  const response = await apiService.get<ApiResponse<MediaType>>(`${url}/${id}`);
  return response.data;
}

export async function uploadMedia(
  file: File,
  alt_text?: string,
  description?: string,
  tags?: string[],
  is_public?: boolean,
  folder_id?: number | null,
): Promise<ApiResponse<MediaType>> {
  const formData = new FormData();
  formData.append('file', file);

  if (alt_text) formData.append('alt_text', alt_text);
  if (description) formData.append('description', description);
  if (tags && tags.length > 0) formData.append('tags', JSON.stringify(tags));
  if (is_public !== undefined) formData.append('is_public', String(is_public));
  if (folder_id !== undefined && folder_id !== null)
    formData.append('folder_id', String(folder_id));

  const response = await apiService.post<{
    status: {
      code: number;
      message: string;
      success: boolean;
      error_code: string;
      path: string;
      timestamp: string;
      details: any;
    };
    data: {
      id: number;
      url: string;
      status: string;
    };
  }>(`${url}/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  // If upload is successful, fetch the complete media details
  if (response.data.status.success && response.data.data.id) {
    try {
      // Try to fetch the complete media item details
      const mediaResponse = await getItem(response.data.data.id.toString());
      return mediaResponse;
    } catch (error) {
      console.error('Error fetching media details after upload:', error);
      // Fall through to create temporary media item
    }
  }

  // If we couldn't fetch the complete media item or the API returned an error,
  // create a temporary placeholder with the required fields
  const mediaType: 'image' | 'video' | 'audio' | 'document' =
    file.type.startsWith('image/')
      ? 'image'
      : file.type.startsWith('video/')
        ? 'video'
        : file.type.startsWith('audio/')
          ? 'audio'
          : 'document';

  // Create a temporary media item with all required fields
  const tempMediaItem: MediaType = {
    id: response.data.data.id,
    tenant_id: 0, // Default value, will be updated when fetched
    file_name: file.name,
    original_name: file.name,
    mime_type: file.type,
    extension: file.name.split('.').pop() || '',
    size: file.size,
    media_type: mediaType,
    path: '',
    url: response.data.data.url,
    public_url: response.data.data.url,
    is_public: is_public || false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    created_by: 0, // Default value, will be updated when fetched
    updated_by: 0, // Default value, will be updated when fetched
  };

  return {
    status: {
      success: response.data.status.success,
      code: response.data.status.code,
      message: response.data.status.message,
    },
    data: tempMediaItem,
  };
}

export async function updateItem(
  id: string,
  data: MediaUpdateData,
): Promise<ApiResponse<MediaType>> {
  const response = await apiService.patch<ApiResponse<MediaType>>(
    `${url}/${id}`,
    data,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(`${url}/${id}`);
  return response.data;
}

export async function getFolders(
  parentId: number | null = null,
): Promise<ApiResponse<Folder[]>> {
  const config: AxiosRequestConfig = {
    params: parentId !== null ? { parent_id: parentId } : {},
  };
  const response = await apiService.get<ApiResponse<Folder[]>>(
    folderUrl,
    config,
  );
  return response.data;
}

export async function createFolder(
  name: string,
  parentId: number | null = null,
): Promise<ApiResponse<Folder>> {
  const payload = { name, parent_id: parentId };
  const response = await apiService.post<ApiResponse<Folder>>(
    folderUrl,
    payload,
  );
  return response.data;
}

export async function updateFolder(
  id: number,
  name: string,
): Promise<ApiResponse<Folder>> {
  const response = await apiService.patch<ApiResponse<Folder>>(
    `${folderUrl}/${id}`,
    { name },
  );
  return response.data;
}

export async function moveFolder(
  id: number,
  parentId: number | null,
): Promise<ApiResponse<Folder>> {
  const response = await apiService.patch<ApiResponse<Folder>>(
    `${folderUrl}/${id}/move`,
    { parent_id: parentId },
  );
  return response.data;
}

export async function deleteFolder(id: number): Promise<ApiResponse<void>> {
  const response = await apiService.delete<ApiResponse<void>>(
    `${folderUrl}/${id}`,
  );
  return response.data;
}
