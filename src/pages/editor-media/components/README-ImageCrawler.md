# Image Crawler Component

Component modal để crawl và tải ảnh từ các trang web.

## Tính năng

- ✅ Crawl ảnh từ URL
- ✅ Hiển thị preview ảnh
- ✅ Chọn nhiều ảnh
- ✅ Hiển thị thông tin ảnh (kích thước, dung lượng)
- ✅ Responsive design
- ✅ Loading states
- ✅ Error handling

## Cách sử dụng

### Import component

```tsx
import { ImageCrawler } from '@/pages/editor-media/components';
```

### Sử dụng cơ bản

```tsx
import React, { useState } from 'react';
import { But<PERSON> } from 'antd';
import { ImageCrawler } from '@/pages/editor-media/components';

const MyComponent = () => {
  const [showCrawler, setShowCrawler] = useState(false);

  const handleSelectImages = (images) => {
    console.log('Selected images:', images);
    // Xử lý ảnh đã chọn
  };

  return (
    <div>
      <Button onClick={() => setShowCrawler(true)}>Mở Image Crawler</Button>

      <ImageCrawler
        open={showCrawler}
        onClose={() => setShowCrawler(false)}
        onSelect={handleSelectImages}
      />
    </div>
  );
};
```

## Props

| Prop       | Type                               | Required | Description             |
| ---------- | ---------------------------------- | -------- | ----------------------- |
| `open`     | `boolean`                          | ✅       | Hiển thị/ẩn modal       |
| `onClose`  | `() => void`                       | ✅       | Callback khi đóng modal |
| `onSelect` | `(images: CrawledImage[]) => void` | ✅       | Callback khi chọn ảnh   |

## Interface CrawledImage

```tsx
interface CrawledImage {
  id: string;
  url: string;
  alt?: string;
  width?: number;
  height?: number;
  size?: string;
}
```

## Demo

Xem file `image-crawler-example.tsx` để tham khảo cách sử dụng đầy đủ.

## Styling

Component sử dụng file CSS riêng `image-crawler.css` với các class:

- `.image-crawler-modal` - Style cho modal
- `.image-card` - Style cho card ảnh
- `.image-card.selected` - Style cho ảnh đã chọn
- `.selected-indicator` - Style cho indicator chọn

## Lưu ý

- Component hiện tại sử dụng mock data để demo
- Trong thực tế cần tích hợp với API crawl ảnh thật
- Hỗ trợ responsive trên mobile
- Có validation URL đầu vào
- Xử lý lỗi khi load ảnh thất bại

## Tùy chỉnh

Bạn có thể tùy chỉnh:

1. **API Endpoint**: Thay thế mock data bằng API thật trong hàm `handleCrawl`
2. **Styling**: Chỉnh sửa file `image-crawler.css`
3. **Validation**: Thêm validation rules trong Form
4. **Image Processing**: Thêm các tính năng xử lý ảnh khác

## Tích hợp API

Để tích hợp với API thật, thay thế phần mock trong `handleCrawl`:

```tsx
const handleCrawl = async () => {
  try {
    const response = await fetch('/api/crawl-images', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: crawlUrl }),
    });

    const data = await response.json();
    setCrawledImages(data.images);
  } catch (error) {
    setError('Crawl failed');
  }
};
```
