import React, { useState } from 'react';
import { <PERSON>ton, Card, Typography, Space, message } from 'antd';
import { PictureOutlined, GlobalOutlined } from '@ant-design/icons';
import ImageCrawler from './image-crawler';

interface CrawledImage {
  id: string;
  url: string;
  alt?: string;
  width?: number;
  height?: number;
  size?: string;
}

const ImageCrawlerExample: React.FC = () => {
  const [showCrawler, setShowCrawler] = useState(false);
  const [selectedImages, setSelectedImages] = useState<CrawledImage[]>([]);

  const handleOpenCrawler = () => {
    setShowCrawler(true);
  };

  const handleCloseCrawler = () => {
    setShowCrawler(false);
  };

  const handleSelectImages = (images: CrawledImage[]) => {
    setSelectedImages(images);
    message.success(`Đã chọn ${images.length} ảnh từ web`);
  };

  const handleClearSelection = () => {
    setSelectedImages([]);
    message.info('Đã xóa tất cả ảnh đã chọn');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <Typography.Title level={3}>
            <GlobalOutlined style={{ marginRight: '8px' }} />
            Image Crawler Demo
          </Typography.Title>
          <Typography.Paragraph type="secondary">
            Crawl và tải ảnh từ các trang web một cách dễ dàng
          </Typography.Paragraph>
        </div>

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              size="large"
              icon={<PictureOutlined />}
              onClick={handleOpenCrawler}
            >
              Mở Image Crawler
            </Button>
          </div>

          {selectedImages.length > 0 && (
            <Card title={`Đã chọn ${selectedImages.length} ảnh`} size="small">
              <div style={{ marginBottom: '16px' }}>
                <Button
                  type="default"
                  size="small"
                  onClick={handleClearSelection}
                >
                  Xóa tất cả
                </Button>
              </div>

              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
                  gap: '12px',
                  maxHeight: '300px',
                  overflowY: 'auto',
                }}
              >
                {selectedImages.map((image) => (
                  <div key={image.id} style={{ textAlign: 'center' }}>
                    <img
                      src={image.url}
                      alt={image.alt || `Image ${image.id}`}
                      style={{
                        width: '100%',
                        height: '80px',
                        objectFit: 'cover',
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9',
                      }}
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src =
                          'https://via.placeholder.com/120x80?text=Error';
                      }}
                    />
                    <Typography.Text
                      style={{
                        fontSize: '10px',
                        display: 'block',
                        marginTop: '4px',
                        color: '#666',
                      }}
                    >
                      {image.width} × {image.height}
                    </Typography.Text>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </Space>
      </Card>

      <ImageCrawler
        open={showCrawler}
        onClose={handleCloseCrawler}
        onSelect={handleSelectImages}
      />
    </div>
  );
};

export default ImageCrawlerExample;
