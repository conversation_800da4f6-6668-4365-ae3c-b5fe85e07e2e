import React, { useRef, useEffect } from 'react';
import { ImageEditSettings } from './image-editor-modal';

export interface ImagePreviewProps {
  imageUrl: string;
  settings: ImageEditSettings;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({ imageUrl, settings }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      // Set canvas size
      canvas.width = img.width;
      canvas.height = img.height;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Fill background
      ctx.fillStyle = settings.backgroundColor || '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Save context state
      ctx.save();

      // Apply rotation
      if (settings.rotation) {
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((settings.rotation * Math.PI) / 180);
        ctx.translate(-canvas.width / 2, -canvas.height / 2);
      }

      // Apply filters
      let filterString = '';
      if (settings.brightness !== undefined) {
        filterString += `brightness(${settings.brightness}%) `;
      }
      if (settings.contrast !== undefined) {
        filterString += `contrast(${settings.contrast}%) `;
      }
      ctx.filter = filterString || 'none';

      // Draw full image
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

      // Restore context state
      ctx.restore();
    };

    img.src = imageUrl;
  }, [imageUrl, settings]);

  return (
    <div
      style={{
        flex: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
        padding: '20px',
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}
      />
    </div>
  );
};

export default ImagePreview;
