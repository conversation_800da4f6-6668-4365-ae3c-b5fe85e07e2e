.image-crop-tool {
  display: flex;
  gap: 16px;
  height: 100%;
}

.crop-controls {
  width: 200px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.crop-input-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.crop-input-group .ant-input-number {
  width: 80px;
}

.crop-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  background: #f0f0f0;
  cursor: crosshair;
}

.crop-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.crop-overlay {
  position: absolute;
  border: 2px solid #1890ff;
  background: rgba(24, 144, 255, 0.1);
  cursor: move;
  user-select: none;
}

.crop-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed #1890ff;
  pointer-events: none;
}

.crop-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #1890ff;
  border: 1px solid #fff;
  border-radius: 50%;
  cursor: pointer;
}

.crop-handle-nw {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.crop-handle-ne {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.crop-handle-sw {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.crop-handle-se {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

.crop-overlay:hover {
  background: rgba(24, 144, 255, 0.2);
}

.crop-overlay:active {
  background: rgba(24, 144, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .image-crop-tool {
    flex-direction: column;
  }

  .crop-controls {
    width: 100%;
    order: 2;
  }

  .crop-container {
    order: 1;
    height: 300px;
  }
}
