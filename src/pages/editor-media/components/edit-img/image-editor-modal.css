.image-editor-modal .ant-modal-content {
  padding: 0;
  overflow: hidden;
}

.image-editor-modal .ant-modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.image-editor-modal .ant-modal-body {
  padding: 0;
  height: 600px;
}

.image-editor-content {
  display: flex;
  height: 100%;
  background: #fafafa;
}

.image-editor-sidebar-container {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.image-editor-preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #fafafa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-editor-modal {
    width: 95% !important;
    max-width: none !important;
  }

  .image-editor-content {
    flex-direction: column;
    height: auto;
  }

  .image-editor-sidebar-container {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
  }

  .image-editor-modal .ant-modal-body {
    height: auto;
    max-height: 80vh;
    overflow-y: auto;
  }
}
