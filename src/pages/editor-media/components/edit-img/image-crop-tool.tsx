import React, { useState, useRef, useEffect } from 'react';
import { Button, Space } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ImageCropToolProps {
  imageUrl: string;
  onCrop: (cropArea: CropArea) => void;
  onCancel: () => void;
}

const ImageCropTool: React.FC<ImageCropToolProps> = ({
  imageUrl,
  onCrop,
  onCancel,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 });
  const [currentCrop, setCurrentCrop] = useState<CropArea | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({
    width: 0,
    height: 0,
  });

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      setImageDimensions({ width: img.width, height: img.height });

      ctx.drawImage(img, 0, 0);
      setImageLoaded(true);
    };

    img.src = imageUrl;
  }, [imageUrl]);

  const getMousePos = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    return {
      x: (e.clientX - rect.left) * scaleX,
      y: (e.clientY - rect.top) * scaleY,
    };
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const pos = getMousePos(e);
    setIsDrawing(true);
    setStartPoint(pos);
    setCurrentCrop(null);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const pos = getMousePos(e);
    const cropArea: CropArea = {
      x: Math.min(startPoint.x, pos.x),
      y: Math.min(startPoint.y, pos.y),
      width: Math.abs(pos.x - startPoint.x),
      height: Math.abs(pos.y - startPoint.y),
    };

    setCurrentCrop(cropArea);
    redrawCanvas(cropArea);
  };

  const handleMouseUp = () => {
    setIsDrawing(false);
  };

  const redrawCanvas = (cropArea?: CropArea) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Redraw image
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      ctx.drawImage(img, 0, 0);

      if (cropArea) {
        // Draw crop overlay
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Clear crop area
        ctx.clearRect(cropArea.x, cropArea.y, cropArea.width, cropArea.height);

        // Redraw image in crop area
        ctx.drawImage(
          img,
          cropArea.x,
          cropArea.y,
          cropArea.width,
          cropArea.height,
          cropArea.x,
          cropArea.y,
          cropArea.width,
          cropArea.height,
        );

        // Draw crop border
        ctx.strokeStyle = '#1890ff';
        ctx.lineWidth = 2;
        ctx.strokeRect(cropArea.x, cropArea.y, cropArea.width, cropArea.height);
      }
    };
    img.src = imageUrl;
  };

  const handleApplyCrop = () => {
    if (currentCrop) {
      onCrop(currentCrop);
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        backgroundColor: '#f5f5f5',
      }}
    >
      <div
        style={{
          padding: '16px',
          borderBottom: '1px solid #d9d9d9',
          backgroundColor: '#fff',
        }}
      >
        <h3 style={{ margin: 0, marginBottom: '8px' }}>Cắt ảnh</h3>
        <p style={{ margin: 0, color: '#666' }}>Kéo để chọn vùng cần cắt</p>
      </div>

      <div
        style={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '20px',
        }}
      >
        {imageLoaded && (
          <canvas
            ref={canvasRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              cursor: 'crosshair',
            }}
          />
        )}
      </div>

      <div
        style={{
          padding: '16px',
          borderTop: '1px solid #d9d9d9',
          backgroundColor: '#fff',
        }}
      >
        <Space>
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={handleApplyCrop}
            disabled={!currentCrop}
          >
            Áp dụng
          </Button>
          <Button icon={<CloseOutlined />} onClick={onCancel}>
            Hủy
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default ImageCropTool;
