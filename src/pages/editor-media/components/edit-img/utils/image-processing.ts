export interface ImageEditSettings {
  brightness: number;
  contrast: number;
  rotation: number;
  backgroundColor: string;
  flipHorizontal?: boolean;
  flipVertical?: boolean;
  saturation?: number;
  hue?: number;
  blur?: number;
  width?: number;
  height?: number;
}

export const processImage = async (
  imageUrl: string,
  settings: ImageEditSettings,
): Promise<string> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = settings.width || img.width;
      canvas.height = settings.height || img.height;

      if (ctx) {
        // Apply background color
        ctx.fillStyle = settings.backgroundColor || '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Apply transformations
        ctx.save();
        ctx.translate(canvas.width / 2, canvas.height / 2);
        ctx.rotate((settings.rotation * Math.PI) / 180);
        ctx.scale(
          settings.flipHorizontal ? -1 : 1,
          settings.flipVertical ? -1 : 1,
        );

        // Apply filters
        const filters = [];
        if (settings.brightness !== 0) {
          filters.push(`brightness(${100 + settings.brightness}%)`);
        }
        if (settings.contrast !== 0) {
          filters.push(`contrast(${100 + settings.contrast}%)`);
        }
        if (settings.saturation !== 0) {
          filters.push(`saturate(${100 + settings.saturation}%)`);
        }
        if (settings.hue !== 0) {
          filters.push(`hue-rotate(${settings.hue}deg)`);
        }
        if (settings.blur && settings.blur > 0) {
          filters.push(`blur(${settings.blur}px)`);
        }

        if (filters.length > 0) {
          ctx.filter = filters.join(' ');
        }

        // Draw image
        ctx.drawImage(
          img,
          -canvas.width / 2,
          -canvas.height / 2,
          canvas.width,
          canvas.height,
        );

        ctx.restore();
      }

      resolve(canvas.toDataURL('image/png'));
    };

    img.crossOrigin = 'anonymous';
    img.src = imageUrl;
  });
};

export const downloadImage = async (
  imageUrl: string,
  filename: string = 'image.png',
): Promise<void> => {
  try {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading image:', error);
  }
};
