import { useCallback, useState } from 'react';
import { ImageEditSettings } from './image-processing';

export interface HistoryState {
  history: ImageEditSettings[];
  currentIndex: number;
  canUndo: boolean;
  canRedo: boolean;
  addToHistory: (settings: ImageEditSettings) => void;
  undo: () => ImageEditSettings | null;
  redo: () => ImageEditSettings | null;
  clearHistory: () => void;
}

export const useImageHistory = (
  initialSettings: ImageEditSettings,
): HistoryState => {
  const [history, setHistory] = useState<ImageEditSettings[]>([
    initialSettings,
  ]);
  const [currentIndex, setCurrentIndex] = useState(0);

  const addToHistory = useCallback(
    (settings: ImageEditSettings) => {
      setHistory((prev) => {
        const newHistory = prev.slice(0, currentIndex + 1);
        newHistory.push(settings);
        return newHistory;
      });
      setCurrentIndex((prev) => prev + 1);
    },
    [currentIndex],
  );

  const undo = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
      return history[currentIndex - 1];
    }
    return null;
  }, [currentIndex, history]);

  const redo = useCallback(() => {
    if (currentIndex < history.length - 1) {
      setCurrentIndex((prev) => prev + 1);
      return history[currentIndex + 1];
    }
    return null;
  }, [currentIndex, history]);

  const clearHistory = useCallback(() => {
    setHistory([initialSettings]);
    setCurrentIndex(0);
  }, [initialSettings]);

  return {
    history,
    currentIndex,
    canUndo: currentIndex > 0,
    canRedo: currentIndex < history.length - 1,
    addToHistory,
    undo,
    redo,
    clearHistory,
  };
};
