import {
  BgColorsOutlined,
  FontSizeOutlined,
  PictureOutlined,
  ScissorOutlined,
  SettingOutlined,
  EditOutlined,
} from '@ant-design/icons';
import React from 'react';

interface MenuItem {
  key: string;
  icon: React.ReactNode;
  label: string;
}

interface VerticalMenuProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const VerticalMenu: React.FC<VerticalMenuProps> = ({
  activeSection,
  onSectionChange,
}) => {
  const menuItems: MenuItem[] = [
    {
      key: 'effects',
      icon: <BgColorsOutlined className="menu-icon" />,
      label: 'Hiệu ứng',
    },
    {
      key: 'text',
      icon: <FontSizeOutlined className="menu-icon" />,
      label: 'Văn bản',
    },
    {
      key: 'image',
      icon: <PictureOutlined className="menu-icon" />,
      label: 'Thêm ảnh',
    },
    {
      key: 'logo',
      icon: <PictureOutlined className="menu-icon" />,
      label: '<PERSON><PERSON><PERSON>',
    },
    {
      key: 'crop',
      icon: <ScissorOutlined className="menu-icon" />,
      label: 'Cắt ảnh',
    },
    {
      key: 'blur-brush',
      icon: <EditOutlined className="menu-icon" />,
      label: 'Làm mờ',
    },
    {
      key: 'settings',
      icon: <SettingOutlined className="menu-icon" />,
      label: 'Cài đặt',
    },
  ];

  return (
    <div
      style={{
        width: '80px',
        minWidth: '80px',
        height: '100%',
        borderRight: '1px solid #e8e8e8',
        backgroundColor: '#fafafa',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '10px 0',
        gap: '16px',
      }}
    >
      {menuItems.map((item) => (
        <button
          key={item.key}
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            background: activeSection === item.key ? '#e6f7ff' : 'none',
            border: 'none',
            padding: '8px',
            width: '60px',
            borderRadius: '4px',
            transition: 'all 0.3s',
          }}
          onClick={() => onSectionChange(item.key)}
          onMouseEnter={(e) => {
            if (activeSection !== item.key) {
              e.currentTarget.style.backgroundColor = '#f0f0f0';
            }
          }}
          onMouseLeave={(e) => {
            if (activeSection !== item.key) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>
            {item.icon}
          </div>
          <span
            style={{
              fontSize: '10px',
              textAlign: 'center',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxWidth: '100%',
            }}
          >
            {item.label}
          </span>
        </button>
      ))}
    </div>
  );
};

export default VerticalMenu;
