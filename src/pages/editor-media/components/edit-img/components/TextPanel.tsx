import {
  Typography,
  Button,
  Input,
  Slider,
  Select,
  Space,
  Divider,
  Card,
} from 'antd';
import {
  PlusOutlined,
  FontSizeOutlined,
  DragOutlined,
} from '@ant-design/icons';
import React, { useState } from 'react';

const { Title, Text } = Typography;
const { Option } = Select;

interface TextItem {
  id: string;
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  x: number;
  y: number;
}

interface TextPanelProps {
  onAddText?: (textItem: TextItem) => void;
}

const TextPanel: React.FC<TextPanelProps> = ({ onAddText }) => {
  const [currentText, setCurrentText] = useState('Văn bản mẫu');
  const [fontSize, setFontSize] = useState(24);
  const [fontFamily, setFontFamily] = useState('Arial');
  const [color, setColor] = useState('#000000');

  const fontOptions = [
    { label: 'Arial', value: 'Arial' },
    { label: 'Times New Roman', value: 'Times New Roman' },
    { label: 'Helvetica', value: 'Helvetica' },
    { label: 'Georgia', value: 'Georgia' },
    { label: 'Verdana', value: 'Verdana' },
    { label: 'Comic Sans MS', value: 'Comic Sans MS' },
    { label: 'Impact', value: 'Impact' },
    { label: 'Trebuchet MS', value: 'Trebuchet MS' },
  ];

  const textTemplates = [
    {
      text: 'Tiêu đề chính',
      fontSize: 32,
      fontFamily: 'Arial',
      color: '#000000',
    },
    {
      text: 'Tiêu đề phụ',
      fontSize: 24,
      fontFamily: 'Georgia',
      color: '#333333',
    },
    {
      text: 'Văn bản thường',
      fontSize: 16,
      fontFamily: 'Helvetica',
      color: '#666666',
    },
    {
      text: 'Chú thích',
      fontSize: 12,
      fontFamily: 'Verdana',
      color: '#999999',
    },
    {
      text: 'Nhấn mạnh',
      fontSize: 28,
      fontFamily: 'Impact',
      color: '#ff0000',
    },
    {
      text: 'Sáng tạo',
      fontSize: 20,
      fontFamily: 'Comic Sans MS',
      color: '#0066cc',
    },
  ];

  const handleAddText = () => {
    const newTextItem: TextItem = {
      id: Date.now().toString(),
      text: currentText,
      fontSize,
      fontFamily,
      color,
      x: 100,
      y: 100,
    };
    onAddText?.(newTextItem);
  };

  const handleTemplateSelect = (template: (typeof textTemplates)[0]) => {
    setCurrentText(template.text);
    setFontSize(template.fontSize);
    setFontFamily(template.fontFamily);
    setColor(template.color);
  };

  return (
    <div
      style={{
        height: 'calc(100vh - 200px)',
        overflow: 'auto',
        padding: '16px',
      }}
    >
      <Title level={4}>Thêm Văn Bản</Title>

      {/* Mẫu văn bản */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Mẫu văn bản</Text>
        <div style={{ marginTop: 8, display: 'grid', gap: 8 }}>
          {textTemplates.map((template, index) => (
            <Card
              key={index}
              size="small"
              hoverable
              onClick={() => handleTemplateSelect(template)}
              style={{
                cursor: 'pointer',
                border: '1px solid #d9d9d9',
              }}
            >
              <div
                style={{
                  fontFamily: template.fontFamily,
                  fontSize: Math.min(template.fontSize, 16),
                  color: template.color,
                  textAlign: 'center',
                }}
              >
                {template.text}
              </div>
              <Text
                type="secondary"
                style={{ fontSize: 10, display: 'block', textAlign: 'center' }}
              >
                {template.fontFamily} - {template.fontSize}px
              </Text>
            </Card>
          ))}
        </div>
      </div>

      <Divider />

      {/* Tùy chỉnh văn bản */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Nội dung</Text>
        <Input
          value={currentText}
          onChange={(e) => setCurrentText(e.target.value)}
          placeholder="Nhập văn bản..."
          style={{ marginTop: 8 }}
        />
      </div>

      {/* Font chữ */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Font chữ</Text>
        <Select
          value={fontFamily}
          onChange={setFontFamily}
          style={{ width: '100%', marginTop: 8 }}
        >
          {fontOptions.map((font) => (
            <Option key={font.value} value={font.value}>
              <span style={{ fontFamily: font.value }}>{font.label}</span>
            </Option>
          ))}
        </Select>
      </div>

      {/* Kích thước chữ */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Kích thước chữ</Text>
        <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
          <Slider
            min={8}
            max={72}
            value={fontSize}
            onChange={setFontSize}
            style={{ flex: 1, marginRight: 12 }}
          />
          <Text>{fontSize}px</Text>
        </div>
      </div>

      {/* Màu chữ */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Màu chữ</Text>
        <div style={{ marginTop: 8 }}>
          <input
            type="color"
            value={color}
            onChange={(e) => setColor(e.target.value)}
            style={{
              width: '100%',
              height: 40,
              border: '1px solid #d9d9d9',
              borderRadius: 6,
              cursor: 'pointer',
            }}
          />
        </div>
      </div>

      {/* Xem trước */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Xem trước</Text>
        <div
          style={{
            marginTop: 8,
            padding: 16,
            border: '1px dashed #d9d9d9',
            borderRadius: 6,
            textAlign: 'center',
            backgroundColor: '#fafafa',
          }}
        >
          <div
            style={{
              fontFamily,
              fontSize: Math.min(fontSize, 24),
              color,
            }}
          >
            {currentText}
          </div>
        </div>
      </div>

      {/* Nút thêm */}
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={handleAddText}
        style={{ width: '100%' }}
        disabled={!currentText.trim()}
      >
        Thêm văn bản
      </Button>

      <div style={{ marginTop: 16 }}>
        <Text type="secondary" style={{ fontSize: 12 }}>
          <DragOutlined /> Văn bản có thể di chuyển và thay đổi kích thước trên
          canvas
        </Text>
      </div>
    </div>
  );
};

export default TextPanel;
