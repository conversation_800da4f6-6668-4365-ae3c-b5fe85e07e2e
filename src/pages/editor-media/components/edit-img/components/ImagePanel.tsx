import { PictureOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Col, Divider, Row, Space, Upload, message } from 'antd';
import React, { useState } from 'react';
import { MediaType } from '../../../type';
import EditorMedia from '../../editor-media';

interface ImagePanelProps {
  onAddImage?: (file: File) => void;
}

const ImagePanel: React.FC<ImagePanelProps> = ({ onAddImage }) => {
  const [imageList, setImageList] = useState<string[]>([]);
  const [showMediaModal, setShowMediaModal] = useState(false);

  const handleUploadImage = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setImageList((prev) => [...prev, imageUrl]);
      message.success('T<PERSON>i ảnh lên thành công!');
    };
    reader.readAsDataURL(file);
    return false; // Prevent default upload
  };

  const handleSelectFromLibrary = () => {
    setShowMediaModal(true);
  };

  const handleMediaSelect = (media: MediaType) => {
    const imageUrl = media.url;
    setImageList((prev) => [...prev, imageUrl]);
    setShowMediaModal(false);
    message.success('Chọn ảnh từ thư viện thành công!');
  };

  const handleImageClick = (imageUrl: string) => {
    // Chuyển đổi URL thành File object để chèn ảnh
    fetch(imageUrl)
      .then((res) => res.blob())
      .then((blob) => {
        const file = new File([blob], 'image.png', { type: blob.type });
        if (onAddImage) {
          onAddImage(file);
          message.success('Đã chèn ảnh vào canvas!');
        }
      })
      .catch(() => {
        message.error('Không thể chèn ảnh!');
      });
  };

  return (
    <div
      style={{
        height: 'calc(100vh - 200px)',
        overflow: 'auto',
        padding: '16px',
      }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Upload
          beforeUpload={handleUploadImage}
          showUploadList={false}
          accept="image/*"
        >
          <Button icon={<UploadOutlined />} style={{ width: '100%' }}>
            Tải ảnh lên
          </Button>
        </Upload>

        <Button
          icon={<PictureOutlined />}
          onClick={handleSelectFromLibrary}
          style={{ width: '100%' }}
        >
          Chọn từ thư viện
        </Button>

        <Divider>Danh sách ảnh</Divider>

        <Row gutter={[8, 8]}>
          {imageList.map((imageUrl, index) => (
            <Col span={12} key={index}>
              <div
                style={{
                  width: '100%',
                  height: '80px',
                  backgroundImage: `url(${imageUrl})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  cursor: 'pointer',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                }}
                onClick={() => handleImageClick(imageUrl)}
              />
            </Col>
          ))}
        </Row>
      </Space>

      <EditorMedia
        visible={showMediaModal}
        onCancel={() => setShowMediaModal(false)}
        onSelect={handleMediaSelect}
        hideEditButton={true}
      />
    </div>
  );
};

export default ImagePanel;
