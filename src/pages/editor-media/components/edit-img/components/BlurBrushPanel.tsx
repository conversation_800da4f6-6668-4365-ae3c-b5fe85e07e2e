import { Button, Slider, Space, Typography } from 'antd';
import React, { useState } from 'react';

const { Title, Text } = Typography;

interface BlurBrushPanelProps {
  onBrushModeChange: (enabled: boolean) => void;
  onBrushSizeChange: (size: number) => void;
  onBrushStrengthChange: (strength: number) => void;
  brushMode?: boolean;
  brushSize?: number;
  brushStrength?: number;
}

const BlurBrushPanel: React.FC<BlurBrushPanelProps> = ({
  onBrushModeChange,
  onBrushSizeChange,
  onBrushStrengthChange,
  brushMode = false,
  brushSize = 20,
  brushStrength = 50,
}) => {
  return (
    <div style={{ padding: '16px 0' }}>
      <Title level={5} style={{ marginBottom: 16 }}>
        Làm mờ bằng bút vẽ
      </Title>

      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Bật/tắt chế độ bút vẽ */}
        <div>
          <Text strong>Chế độ bút vẽ</Text>
          <div style={{ marginTop: 8 }}>
            <Button
              type={brushMode ? 'primary' : 'default'}
              onClick={() => onBrushModeChange(!brushMode)}
              style={{ width: '100%' }}
            >
              {brushMode ? 'Tắt bút vẽ' : 'Bật bút vẽ'}
            </Button>
          </div>
        </div>

        {/* Kích thước bút */}
        <div>
          <Text strong>Kích thước bút: {brushSize}px</Text>
          <Slider
            min={5}
            max={100}
            value={brushSize}
            onChange={onBrushSizeChange}
            tooltip={{
              formatter: (value) => `${value}px`,
            }}
            style={{ marginTop: 8 }}
          />
        </div>

        {/* Độ mạnh làm mờ */}
        <div>
          <Text strong>Độ mạnh: {brushStrength}%</Text>
          <Slider
            min={10}
            max={100}
            value={brushStrength}
            onChange={onBrushStrengthChange}
            tooltip={{
              formatter: (value) => `${value}%`,
            }}
            style={{ marginTop: 8 }}
          />
        </div>

        {/* Hướng dẫn */}
        <div
          style={{
            marginTop: 16,
            padding: 12,
            backgroundColor: '#f6f6f6',
            borderRadius: 4,
          }}
        >
          <Text type="secondary" style={{ fontSize: 12 }}>
            💡 Hướng dẫn:
            <br />• Bật chế độ bút vẽ
            <br />• Vẽ trên vùng ảnh muốn làm mờ
            <br />• Điều chỉnh kích thước và độ mạnh phù hợp
          </Text>
        </div>
      </Space>
    </div>
  );
};

export default BlurBrushPanel;
