import { BlockOutlined } from '@ant-design/icons';
import { Button, Typography, Input, Slider, Radio, Space, Divider } from 'antd';
import React, { useState } from 'react';

const { Title, Text } = Typography;

interface CropPanelProps {
  onCropStart?: () => void;
  onApply?: (settings: CropSettings) => void;
  onCancel?: () => void;
}

interface CropSettings {
  width: number;
  height: number;
  aspectRatio: string;
  rotation: number;
  zoom: number;
}

const CropPanel: React.FC<CropPanelProps> = ({
  onCropStart,
  onApply,
  onCancel,
}) => {
  const [width, setWidth] = useState(2560);
  const [height, setHeight] = useState(1829);
  const [aspectRatio, setAspectRatio] = useState('custom');
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(100);
  const [isCustomSize, setIsCustomSize] = useState(true);

  const handleAspectRatioChange = (value: string) => {
    setAspectRatio(value);
    if (value === 'square') {
      const size = Math.min(width, height);
      setWidth(size);
      setHeight(size);
    } else if (value === 'round') {
      const size = Math.min(width, height);
      setWidth(size);
      setHeight(size);
    } else if (value === 'oval') {
      // Keep current dimensions for oval
    }
  };

  const handleApply = () => {
    const settings: CropSettings = {
      width,
      height,
      aspectRatio,
      rotation,
      zoom,
    };
    onApply?.(settings);
  };

  return (
    <div
      style={{
        height: 'calc(100vh - 200px)',
        overflow: 'auto',
        padding: '16px',
      }}
    >
      <Title level={4}>Cắt Ảnh</Title>

      {/* Kích thước */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Chiều rộng</Text>
        <Input
          type="number"
          value={width}
          onChange={(e) => setWidth(Number(e.target.value))}
          style={{ marginTop: 8 }}
        />
      </div>

      <div style={{ marginBottom: 16 }}>
        <Text strong>Chiều dài</Text>
        <Input
          type="number"
          value={height}
          onChange={(e) => setHeight(Number(e.target.value))}
          style={{ marginTop: 8 }}
        />
      </div>

      {/* Tùy chọn kích thước */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type={isCustomSize ? 'default' : 'primary'}
            size="small"
            onClick={() => setIsCustomSize(false)}
          >
            Tự do
          </Button>
          <Button
            type={isCustomSize ? 'primary' : 'default'}
            size="small"
            onClick={() => setIsCustomSize(true)}
          >
            Góc
          </Button>
        </Space>
      </div>

      <Divider />

      {/* Xoay */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Xoay</Text>
        <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
          <Slider
            min={-180}
            max={180}
            value={rotation}
            onChange={setRotation}
            style={{ flex: 1, marginRight: 12 }}
          />
          <Text>{rotation}°</Text>
        </div>
      </div>

      {/* Thu phóng */}
      <div style={{ marginBottom: 16 }}>
        <Text strong>Thu phóng</Text>
        <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
          <Slider
            min={10}
            max={200}
            value={zoom}
            onChange={setZoom}
            style={{ flex: 1, marginRight: 12 }}
          />
          <Text>{zoom}%</Text>
        </div>
      </div>

      <Divider />

      {/* Chọn kiểu chỗi */}
      <div style={{ marginBottom: 24 }}>
        <Text strong>Chọn kiểu chỗi</Text>
        <Radio.Group
          value={aspectRatio}
          onChange={(e) => handleAspectRatioChange(e.target.value)}
          style={{ marginTop: 8, display: 'block' }}
        >
          <Space direction="vertical">
            <Radio value="square">Vuông</Radio>
            <Radio value="round">Tròn</Radio>
            <Radio value="oval">Oval</Radio>
          </Space>
        </Radio.Group>
      </div>

      {/* Nút thao tác */}
      <Space style={{ width: '100%' }} direction="vertical">
        <Button type="default" onClick={onCancel} style={{ width: '100%' }}>
          Hủy
        </Button>
        <Button type="primary" onClick={handleApply} style={{ width: '100%' }}>
          Áp dụng
        </Button>
      </Space>
    </div>
  );
};

export default CropPanel;
