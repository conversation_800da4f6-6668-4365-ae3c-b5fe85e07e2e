import {
  DownloadOutlined,
  RedoOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  SaveOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import { Button, Divider, Input, Space, Typography } from 'antd';
import React from 'react';
import { ImageEditSettings } from '../utils/image-processing';

const { Title, Text } = Typography;

interface SettingsPanelProps {
  settings: ImageEditSettings;
  onSettingsChange: (settings: Partial<ImageEditSettings>) => void;
  onRotateLeft: () => void;
  onRotateRight: () => void;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  onDownload: () => void;
  isProcessing: boolean;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  settings,
  onSettingsChange,
  onRotateLeft,
  onRotateRight,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onDownload,
  isProcessing,
}) => {
  const handleBackgroundColorChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    onSettingsChange({ backgroundColor: e.target.value });
  };

  return (
    <div
      style={{
        height: 'calc(100vh - 200px)',
        overflow: 'auto',
        padding: '16px',
      }}
    >
      <Title level={4}>Cài Đặt</Title>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ marginBottom: 16 }}>
          <Text>Màu nền</Text>
          <Input
            type="color"
            value={settings.backgroundColor || '#ffffff'}
            onChange={handleBackgroundColorChange}
            style={{ width: '100%', height: 32, marginTop: 8 }}
          />
        </div>

        <Divider />
        <div style={{ display: 'flex', gap: 8, marginBottom: 16 }}>
          <Button
            icon={<RotateLeftOutlined />}
            onClick={onRotateLeft}
            disabled={isProcessing}
            style={{ flex: 1 }}
          >
            Xoay trái
          </Button>
          <Button
            icon={<RotateRightOutlined />}
            onClick={onRotateRight}
            disabled={isProcessing}
            style={{ flex: 1 }}
          >
            Xoay phải
          </Button>
        </div>

        <div style={{ display: 'flex', gap: 8, marginBottom: 16 }}>
          <Button
            icon={<UndoOutlined />}
            onClick={onUndo}
            disabled={!canUndo || isProcessing}
            style={{ flex: 1 }}
          >
            Hoàn tác
          </Button>
          <Button
            icon={<RedoOutlined />}
            onClick={onRedo}
            disabled={!canRedo || isProcessing}
            style={{ flex: 1 }}
          >
            Làm lại
          </Button>
        </div>

        <div style={{ display: 'flex', gap: 8 }}>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={onSave}
            disabled={isProcessing}
            style={{ flex: 1 }}
          >
            Lưu
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={onDownload}
            disabled={isProcessing}
            style={{ flex: 1 }}
          >
            Tải xuống
          </Button>
        </div>
      </Space>
    </div>
  );
};

export default SettingsPanel;
