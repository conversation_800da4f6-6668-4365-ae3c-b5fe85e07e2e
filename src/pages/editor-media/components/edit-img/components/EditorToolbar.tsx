import { PlusOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import React from 'react';

interface EditorToolbarProps {
  selectedId: string | null;
  onAddShape: (type: 'rect' | 'circle' | 'text') => void;
  onImageUpload: (file: File) => boolean;
  onDeleteSelected: () => void;
}

const EditorToolbar: React.FC<EditorToolbarProps> = ({
  selectedId,
  onAddShape,
  onImageUpload,
  onDeleteSelected,
}) => {
  return (
    <div
      style={{
        padding: '10px',
        backgroundColor: '#ffffff',
        borderBottom: '1px solid #e8e8e8',
        display: 'flex',
        alignItems: 'center',
        gap: '10px',
        flexWrap: 'wrap',
      }}
    >
      <Button
        size="small"
        onClick={() => onAddShape('rect')}
        icon={<PlusOutlined />}
      >
        <PERSON><PERSON><PERSON> chữ nhật
      </Button>
      <Button
        size="small"
        onClick={() => onAddShape('circle')}
        icon={<PlusOutlined />}
      >
        Hình tròn
      </Button>
      <Button
        size="small"
        onClick={() => onAddShape('text')}
        icon={<PlusOutlined />}
      >
        Văn bản
      </Button>
      <Upload
        accept="image/*"
        showUploadList={false}
        beforeUpload={onImageUpload}
      >
        <Button size="small" icon={<PlusOutlined />}>
          Thêm ảnh
        </Button>
      </Upload>
      {selectedId && (
        <Button
          size="small"
          danger
          onClick={onDeleteSelected}
          style={{ marginLeft: '10px' }}
        >
          Xóa đối tượng
        </Button>
      )}
    </div>
  );
};

export default EditorToolbar;
