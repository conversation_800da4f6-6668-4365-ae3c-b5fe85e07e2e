import { PictureOutlined, UploadOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Divider,
  Row,
  Space,
  Typography,
  Upload,
  message,
} from 'antd';
import React, { useState } from 'react';
import { MediaType } from '../../../type';
import EditorMedia from '../../editor-media';

const { Title } = Typography;

interface LogoPanelProps {
  onAddLogo?: (file: File, position?: string) => void;
}

const LogoPanel: React.FC<LogoPanelProps> = ({ onAddLogo }) => {
  const [logoList, setLogoList] = useState<string[]>([]);
  const [showMediaModal, setShowMediaModal] = useState(false);
  const [selectedLogoPosition, setSelectedLogoPosition] =
    useState<string>('top-left');

  const handleLogoUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const logoUrl = e.target?.result as string;
      setLogoList((prev) => [...prev, logoUrl]);
      message.success('Tải logo lên thành công!');
      // Tự động chèn logo vào canvas
      if (onAddLogo) {
        onAddLogo(file);
      }
    };
    reader.readAsDataURL(file);
    return false;
  };

  const handleSelectFromLibrary = () => {
    setShowMediaModal(true);
  };

  const handleMediaSelect = (media: MediaType) => {
    const logoUrl = media.url;
    setLogoList((prev) => [...prev, logoUrl]);
    setShowMediaModal(false);
    message.success('Chọn logo từ thư viện thành công!');
  };

  const handleLogoPositionSelect = (position: string) => {
    setSelectedLogoPosition(position);
  };

  const handleLogoClick = (logoUrl: string) => {
    // Chuyển đổi URL thành File object để chèn logo
    fetch(logoUrl)
      .then((res) => res.blob())
      .then((blob) => {
        const file = new File([blob], 'logo.png', { type: blob.type });
        if (onAddLogo) {
          onAddLogo(file, selectedLogoPosition);
          message.success('Đã chèn logo vào canvas!');
        }
      })
      .catch(() => {
        message.error('Không thể chèn logo!');
      });
  };

  const positionButtons = [
    { key: 'top-left', label: '↖', row: 0, col: 0 },
    { key: 'top-center', label: 'Trên', row: 0, col: 1 },
    { key: 'top-right', label: '↗', row: 0, col: 2 },
    { key: 'center-left', label: 'Trái', row: 1, col: 0 },
    { key: 'center', label: 'Trung tâm', row: 1, col: 1 },
    { key: 'center-right', label: 'Phải', row: 1, col: 2 },
    { key: 'bottom-left', label: '↙', row: 2, col: 0 },
    { key: 'bottom-center', label: 'Dưới', row: 2, col: 1 },
    { key: 'bottom-right', label: '↘', row: 2, col: 2 },
  ];

  return (
    <div
      style={{
        height: 'calc(100vh - 200px)',
        overflow: 'auto',
        padding: '16px',
      }}
    >
      <Title level={4}>Chèn Logo</Title>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Upload
          beforeUpload={handleLogoUpload}
          showUploadList={false}
          accept="image/*"
        >
          <Button icon={<UploadOutlined />} style={{ width: '100%' }}>
            Tải logo lên
          </Button>
        </Upload>

        <Button
          icon={<PictureOutlined />}
          onClick={handleSelectFromLibrary}
          style={{ width: '100%' }}
        >
          Chọn logo từ thư viện
        </Button>

        <Divider>Vị trí logo</Divider>

        {/* Logo Position Grid */}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '8px',
            marginTop: '16px',
          }}
        >
          {positionButtons.map((position) => (
            <Button
              key={position.key}
              style={{
                width: '60px',
                height: '60px',
                backgroundColor:
                  selectedLogoPosition === position.key ? '#4CAF50' : '#9E9E9E',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '12px',
              }}
              onClick={() => handleLogoPositionSelect(position.key)}
            >
              {position.label}
            </Button>
          ))}
        </div>

        <Divider>Danh sách logo</Divider>

        <Row gutter={[8, 8]}>
          {logoList.map((logoUrl, index) => (
            <Col span={12} key={index}>
              <div
                style={{
                  width: '100%',
                  height: '80px',
                  backgroundImage: `url(${logoUrl})`,
                  backgroundSize: 'contain',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center',
                  cursor: 'pointer',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  backgroundColor: '#f5f5f5',
                }}
                onClick={() => handleLogoClick(logoUrl)}
              />
            </Col>
          ))}
        </Row>
      </Space>

      <EditorMedia
        visible={showMediaModal}
        onCancel={() => setShowMediaModal(false)}
        onSelect={handleMediaSelect}
        hideEditButton={true}
      />
    </div>
  );
};

export default LogoPanel;
