import { Slider, Space, Typography } from 'antd';
import React from 'react';
import { ImageEditSettings } from '../utils/image-processing';

const { Title, Text } = Typography;

interface EffectsPanelProps {
  settings: ImageEditSettings;
  onSettingsChange: (settings: Partial<ImageEditSettings>) => void;
}

const EffectsPanel: React.FC<EffectsPanelProps> = ({
  settings,
  onSettingsChange,
}) => {
  const renderSlider = (
    label: string,
    value: number,
    onChange: (value: number) => void,
    min = 0,
    max = 200,
    step = 1,
  ) => (
    <div style={{ marginBottom: 16 }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: 8,
        }}
      >
        <Text>{label}</Text>
        <Text style={{ color: '#666' }}>
          {value}
          {min < 0 || max > 100 ? '' : '%'}
        </Text>
      </div>
      <Slider
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={onChange}
        tooltip={{
          formatter: (value) => `${value}${min < 0 || max > 100 ? '' : '%'}`,
        }}
        tipFormatter={(value) => `${value}${min < 0 || max > 100 ? '' : '%'}`}
      />
    </div>
  );

  return (
    <div
      style={{
        height: 'calc(100vh - 200px)',
        overflow: 'auto',
        padding: '16px',
      }}
    >
      <Title level={4}>Hiệu Ứng Ảnh Nền</Title>
      <Space direction="vertical" style={{ width: '100%' }}>
        {renderSlider(
          'Độ sáng',
          settings.brightness || 100,
          (value) => onSettingsChange({ brightness: value }),
          0,
          200,
        )}
        {renderSlider(
          'Tương phản',
          settings.contrast || 100,
          (value) => onSettingsChange({ contrast: value }),
          0,
          200,
        )}
        {renderSlider(
          'Bão hòa',
          settings.saturation || 100,
          (value) => onSettingsChange({ saturation: value }),
          0,
          200,
        )}
        {renderSlider(
          'Màu sắc',
          settings.hue || 0,
          (value) => onSettingsChange({ hue: value }),
          -180,
          180,
        )}
        {renderSlider(
          'Độ mờ',
          Math.round((settings.opacity || 1) * 100),
          (value) => onSettingsChange({ opacity: value / 100 }),
          0,
          100,
        )}
      </Space>
    </div>
  );
};

export default EffectsPanel;
