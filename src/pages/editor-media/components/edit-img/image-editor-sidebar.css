.image-editor-sidebar {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.sidebar-icon {
  font-size: 18px;
  color: #1890ff;
}

.sidebar-section {
  margin-bottom: 20px;
}

.section-label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #262626;
}

.size-controls {
  width: 100%;
}

.size-input-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.size-input-group .ant-typography {
  font-size: 13px;
  color: #595959;
}

.size-input {
  width: 100%;
}

.color-picker {
  width: 100%;
}

.sidebar-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 24px;
}

.action-button {
  height: 40px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.save-button {
  background: #52c41a;
  border-color: #52c41a;
}

.save-button:hover {
  background: #73d13d;
  border-color: #73d13d;
}

.remove-bg-button {
  background: #ffffff;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.remove-bg-button:hover {
  background: #ff4d4f;
  border-color: #ff4d4f;
  color: #ffffff;
}

.remove-bg-button.active {
  background: #ff4d4f;
  border-color: #ff4d4f;
  color: #ffffff;
}

.crop-button {
  background: #ffffff;
  border-color: #1890ff;
  color: #1890ff;
}

.crop-button:hover {
  background: #1890ff;
  border-color: #1890ff;
  color: #ffffff;
}

/* Custom scrollbar */
.image-editor-sidebar::-webkit-scrollbar {
  width: 6px;
}

.image-editor-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.image-editor-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.image-editor-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-editor-sidebar {
    padding: 16px;
  }

  .sidebar-actions {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-button {
    flex: 1;
    min-width: calc(50% - 4px);
  }
}
