# Image Editor Components

Bộ component chỉnh sửa ảnh cho ứng dụng media editor, đ<PERSON><PERSON><PERSON> thiết kế dựa trên giao diện modal chỉnh sửa ảnh.

## Cấu trúc Components

### 1. ImageEditorModal

Component modal chính chứa toàn bộ giao diện chỉnh sửa ảnh.

**Props:**

- `visible: boolean` - Hiển thị/ẩn modal
- `onClose: () => void` - Callback khi đóng modal
- `imageUrl?: string` - URL của ảnh cần chỉnh sửa
- `onSave?: (editedImageData: any) => void` - Callback khi lưu ảnh

### 2. ImageEditorSidebar

Component sidebar chứa các công cụ chỉnh sửa ảnh.

**Tính năng:**

- Chỉnh sửa kích thước (chiều rộng, chiều cao)
- Chọn màu nền
- <PERSON><PERSON>a nền ảnh gốc
- Cắt ảnh
- <PERSON><PERSON><PERSON> ảnh

### 3. ImagePreview

Component hiển thị preview ảnh với các thay đổi được áp dụng.

**Tính năng:**

- Hiển thị ảnh với kích thước đã chỉnh sửa
- Áp dụng màu nền
- Hiển thị thông tin kích thước
- Xử lý loading state

## Cách sử dụng

### Import components

```typescript
import {
  ImageEditorModal,
  ImageEditorSidebar,
  ImagePreview,
  ImageEditorExample,
} from '@/pages/editor-media/components/edit-img';
```

### Sử dụng cơ bản

```typescript
import React, { useState } from 'react';
import { ImageEditorModal } from './edit-img';

const MyComponent = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [imageUrl, setImageUrl] = useState('path/to/image.jpg');

  const handleSave = (editedImageData: any) => {
    console.log('Ảnh đã chỉnh sửa:', editedImageData);
    // Xử lý lưu ảnh
  };

  return (
    <>
      <button onClick={() => setModalVisible(true)}>
        Chỉnh sửa ảnh
      </button>

      <ImageEditorModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        imageUrl={imageUrl}
        onSave={handleSave}
      />
    </>
  );
};
```

### Demo Component

Sử dụng `ImageEditorExample` để xem demo đầy đủ:

```typescript
import { ImageEditorExample } from './edit-img';

const DemoPage = () => {
  return <ImageEditorExample />;
};
```

## Interfaces

### ImageEditSettings

```typescript
interface ImageEditSettings {
  width: number;
  height: number;
  backgroundColor: string;
  removeBackground: boolean;
  cropSettings?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}
```

## Styling

Các component đã được styling với CSS modules:

- `image-editor-modal.css` - Styling cho modal chính
- `image-editor-sidebar.css` - Styling cho sidebar
- `image-preview.css` - Styling cho preview

## Tính năng hiện tại

✅ **Đã hoàn thành:**

- Giao diện modal responsive
- Chỉnh sửa kích thước ảnh
- Chọn màu nền
- Preview ảnh real-time
- Toggle xóa nền
- Hiển thị thông tin kích thước

🚧 **Đang phát triển:**

- Chức năng cắt ảnh (crop)
- Xóa nền ảnh thực tế
- Lưu ảnh đã chỉnh sửa
- Undo/Redo
- Thêm filter và hiệu ứng

## Lưu ý

1. Component sử dụng HTML5 Canvas để xử lý ảnh
2. Hỗ trợ responsive design cho mobile
3. Tương thích với Ant Design theme
4. Sử dụng TypeScript cho type safety

## Tích hợp API

Để tích hợp với API backend:

```typescript
const handleSave = async (editedImageData: any) => {
  try {
    const response = await apiService.post('/media/edit', {
      imageUrl: editedImageData.imageUrl,
      settings: editedImageData.settings,
    });

    console.log('Ảnh đã được lưu:', response.data);
  } catch (error) {
    console.error('Lỗi khi lưu ảnh:', error);
  }
};
```
