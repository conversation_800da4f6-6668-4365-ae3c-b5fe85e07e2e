.image-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #fafafa;
}

.preview-empty {
  color: #8c8c8c;
}

.preview-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.preview-canvas-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100%;
  max-height: calc(100% - 60px);
  padding: 20px;
}

.preview-canvas {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #ffffff;
  transition: all 0.3s ease;
}

.preview-canvas:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.preview-info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.preview-dimensions {
  white-space: nowrap;
}

/* Loading animation */
.preview-loading .ant-spin {
  color: #1890ff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .preview-canvas-wrapper {
    padding: 10px;
    max-height: calc(100% - 40px);
  }

  .preview-info {
    bottom: 10px;
    font-size: 11px;
    padding: 4px 8px;
  }

  .preview-canvas {
    max-width: calc(100vw - 40px);
    max-height: calc(50vh - 40px);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .image-preview-container {
    background: #141414;
  }

  .preview-canvas {
    border-color: #434343;
  }
}
