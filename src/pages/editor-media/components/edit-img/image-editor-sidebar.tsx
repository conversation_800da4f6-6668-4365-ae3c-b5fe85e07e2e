import { Typography } from 'antd';
import React from 'react';
import BlurBrushPanel from './components/BlurBrushPanel';
import CropPanel from './components/CropPanel';
import EffectsPanel from './components/EffectsPanel';
import ImagePanel from './components/ImagePanel';
import LogoPanel from './components/LogoPanel';
import SettingsPanel from './components/SettingsPanel';
import TextPanel from './components/TextPanel';
import { ImageEditSettings } from './utils/image-processing';

const { Title, Text } = Typography;

interface TextItem {
  id: string;
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  x: number;
  y: number;
}

interface ImageEditorSidebarProps {
  activeSection: string;
  settings: ImageEditSettings;
  onSettingsChange: (settings: Partial<ImageEditSettings>) => void;
  onAddImage: (file: File) => void;
  onAddLogo: (file: File) => void;
  onAddText?: (textItem: TextItem) => void;
  onRotateLeft: () => void;
  onRotateRight: () => void;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  onDownload: () => void;
  isProcessing: boolean;
  onCropStart?: () => void;
  onBrushModeChange?: (enabled: boolean) => void;
  onBrushSizeChange?: (size: number) => void;
  onBrushStrengthChange?: (strength: number) => void;
  brushMode?: boolean;
  brushSize?: number;
  brushStrength?: number;
}

const ImageEditorSidebar: React.FC<ImageEditorSidebarProps> = ({
  activeSection,
  settings,
  onSettingsChange,
  onAddImage,
  onAddLogo,
  onAddText,
  onRotateLeft,
  onRotateRight,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onDownload,
  isProcessing,
  onCropStart,
  onBrushModeChange,
  onBrushSizeChange,
  onBrushStrengthChange,
  brushMode,
  brushSize,
  brushStrength,
}) => {
  const renderContent = () => {
    switch (activeSection) {
      case 'effects':
        return (
          <EffectsPanel
            settings={settings}
            onSettingsChange={onSettingsChange}
          />
        );
      case 'text':
        return <TextPanel onAddText={onAddText} />;
      case 'image':
        return <ImagePanel onAddImage={onAddImage} />;
      case 'logo':
        return <LogoPanel onAddLogo={onAddLogo} />;
      case 'crop':
        return <CropPanel onCropStart={onCropStart} />;
      case 'blur-brush':
        return (
          <BlurBrushPanel
            onBrushModeChange={onBrushModeChange!}
            onBrushSizeChange={onBrushSizeChange!}
            onBrushStrengthChange={onBrushStrengthChange!}
            brushMode={brushMode}
            brushSize={brushSize}
            brushStrength={brushStrength}
          />
        );
      case 'settings':
        return (
          <SettingsPanel
            settings={settings}
            onSettingsChange={onSettingsChange}
            onRotateLeft={onRotateLeft}
            onRotateRight={onRotateRight}
            canUndo={canUndo}
            canRedo={canRedo}
            onUndo={onUndo}
            onRedo={onRedo}
            onSave={onSave}
            onDownload={onDownload}
            isProcessing={isProcessing}
          />
        );
      default:
        return (
          <div style={{ padding: 20, textAlign: 'center' }}>
            <Text type="secondary">Chọn một menu để bắt đầu chỉnh sửa</Text>
          </div>
        );
    }
  };

  return (
    <div
      style={{
        width: '100%',
        backgroundColor: 'transparent',
        height: '100%',
        overflowY: 'auto',
      }}
    >
      {renderContent()}
    </div>
  );
};

export default ImageEditorSidebar;
