import { ImageEditSettings } from './image-editor-modal';
import { CropArea } from './image-crop-tool';

/**
 * Process image with given settings and return processed image URL
 */
export const processImage = async (
  imageUrl: string,
  settings: ImageEditSettings,
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('Could not get canvas context'));
      return;
    }

    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        // Set canvas size
        canvas.width = settings.width || img.width;
        canvas.height = settings.height || img.height;

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Fill background
        ctx.fillStyle = settings.backgroundColor || '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Save context state
        ctx.save();

        // Apply rotation
        if (settings.rotation) {
          ctx.translate(canvas.width / 2, canvas.height / 2);
          ctx.rotate((settings.rotation * Math.PI) / 180);
          ctx.translate(-canvas.width / 2, -canvas.height / 2);
        }

        // Apply filters
        let filterString = '';
        if (settings.brightness !== undefined) {
          filterString += `brightness(${100 + settings.brightness}%) `;
        }
        if (settings.contrast !== undefined) {
          filterString += `contrast(${100 + settings.contrast}%) `;
        }
        if (settings.filter) {
          if (settings.filter.grayscale) {
            filterString += `grayscale(${settings.filter.grayscale}%) `;
          }
          if (settings.filter.sepia) {
            filterString += `sepia(${settings.filter.sepia}%) `;
          }
          if (settings.filter.blur) {
            filterString += `blur(${settings.filter.blur}px) `;
          }
        }
        ctx.filter = filterString || 'none';

        // Draw image
        if (settings.cropArea) {
          // Draw cropped image
          const { x, y, width, height } = settings.cropArea;
          ctx.drawImage(
            img,
            x,
            y,
            width,
            height,
            0,
            0,
            canvas.width,
            canvas.height,
          );
        } else {
          // Draw full image
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        }

        // Restore context state
        ctx.restore();

        // Convert to blob and create URL
        canvas.toBlob((blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            resolve(url);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        }, 'image/png');
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = imageUrl;
  });
};

/**
 * Download image with given filename
 */
export const downloadImage = async (
  imageUrl: string,
  filename: string,
): Promise<void> => {
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('Error downloading image:', error);
    throw error;
  }
};

/**
 * Get image dimensions
 */
export const getImageDimensions = (
  imageUrl: string,
): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = imageUrl;
  });
};

/**
 * Convert file to data URL
 */
export const fileToDataURL = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
};
