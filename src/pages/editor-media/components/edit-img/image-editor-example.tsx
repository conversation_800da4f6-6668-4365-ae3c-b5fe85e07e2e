import React, { useState } from 'react';
import { Button, Space, Typography, Card } from 'antd';
import { EditOutlined, PictureOutlined } from '@ant-design/icons';
import { ImageEditorModal } from './image-editor-modal';

const { Title, Text } = Typography;

export const ImageEditorExample: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>(
    'https://picsum.photos/400/300?random=1',
  );

  const sampleImages = [
    {
      id: 1,
      url: 'https://picsum.photos/400/300?random=1',
      name: 'Ảnh mẫu 1',
    },
    {
      id: 2,
      url: 'https://picsum.photos/400/300?random=2',
      name: 'Ảnh mẫu 2',
    },
    {
      id: 3,
      url: 'https://picsum.photos/400/300?random=3',
      name: 'Ảnh mẫu 3',
    },
  ];

  const handleImageSelect = (imageUrl: string) => {
    setSelectedImage(imageUrl);
    setModalVisible(true);
  };

  const handleSave = (editedImageData: any) => {
    console.log('Ảnh đã được chỉnh sửa:', editedImageData);
    // Ở đây bạn có thể xử lý việc lưu ảnh đã chỉnh sửa
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={3}>
        <PictureOutlined /> Demo Modal Chỉnh Sửa Ảnh
      </Title>

      <Text type="secondary" style={{ display: 'block', marginBottom: '24px' }}>
        Chọn một ảnh mẫu để mở modal chỉnh sửa ảnh
      </Text>

      <div
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
          gap: '16px',
          marginBottom: '24px',
        }}
      >
        {sampleImages.map((image) => (
          <Card
            key={image.id}
            hoverable
            cover={
              <img
                alt={image.name}
                src={image.url}
                style={{ height: '150px', objectFit: 'cover' }}
              />
            }
            actions={[
              <Button
                key="edit"
                type="primary"
                icon={<EditOutlined />}
                onClick={() => handleImageSelect(image.url)}
              >
                Chỉnh sửa
              </Button>,
            ]}
          >
            <Card.Meta title={image.name} />
          </Card>
        ))}
      </div>

      <Space>
        <Button
          type="primary"
          icon={<EditOutlined />}
          onClick={() => setModalVisible(true)}
        >
          Mở Modal Chỉnh Sửa
        </Button>
      </Space>

      <ImageEditorModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        imageUrl={selectedImage}
        onSave={handleSave}
      />
    </div>
  );
};

export default ImageEditorExample;
