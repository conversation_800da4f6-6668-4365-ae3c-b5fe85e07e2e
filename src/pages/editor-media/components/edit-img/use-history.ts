import { useState, useCallback } from 'react';

interface UseHistoryReturn<T> {
  state: T;
  setState: (newState: T | ((prevState: T) => T)) => void;
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  clearHistory: () => void;
  historySize: number;
}

export const useHistory = <T>(
  initialState: T,
  maxHistorySize: number = 50,
): UseHistoryReturn<T> => {
  const [history, setHistory] = useState<T[]>([initialState]);
  const [currentIndex, setCurrentIndex] = useState(0);

  const setState = useCallback(
    (newState: T | ((prevState: T) => T)) => {
      setHistory((prevHistory) => {
        const currentState = prevHistory[currentIndex];
        const nextState =
          typeof newState === 'function'
            ? (newState as (prevState: T) => T)(currentState)
            : newState;

        // Don't add to history if state hasn't changed
        if (JSON.stringify(nextState) === JSON.stringify(currentState)) {
          return prevHistory;
        }

        // Remove any future history if we're not at the end
        const newHistory = prevHistory.slice(0, currentIndex + 1);

        // Add new state
        newHistory.push(nextState);

        // Limit history size
        if (newHistory.length > maxHistorySize) {
          newHistory.shift();
          setCurrentIndex(newHistory.length - 1);
        } else {
          setCurrentIndex(newHistory.length - 1);
        }

        return newHistory;
      });
    },
    [currentIndex, maxHistorySize],
  );

  const undo = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  }, [currentIndex]);

  const redo = useCallback(() => {
    if (currentIndex < history.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  }, [currentIndex, history.length]);

  const clearHistory = useCallback(() => {
    setHistory([initialState]);
    setCurrentIndex(0);
  }, [initialState]);

  const canUndo = currentIndex > 0;
  const canRedo = currentIndex < history.length - 1;
  const state = history[currentIndex];

  return {
    state,
    setState,
    undo,
    redo,
    canUndo,
    canRedo,
    clearHistory,
    historySize: history.length,
  };
};
