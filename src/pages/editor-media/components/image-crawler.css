/* Image Crawler Modal Styles */
.image-crawler-modal .ant-modal-content {
  border-radius: 8px;
}

.image-crawler-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.image-crawler-modal .ant-modal-body {
  padding: 0;
}

.image-crawler-modal .ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
}

/* Image Card Styles */
.image-card {
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-card.selected {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.image-card .ant-card-cover {
  position: relative;
  overflow: hidden;
}

.image-card .ant-card-cover img {
  transition: transform 0.3s ease;
}

.image-card:hover .ant-card-cover img {
  transform: scale(1.05);
}

.image-card .ant-card-body {
  padding: 12px;
}

.image-card .ant-card-meta-description {
  color: #666;
  font-size: 12px;
}

/* Search Section */
.ant-input-search .ant-input-group .ant-input-group-addon .ant-btn {
  height: 40px;
  border-radius: 0 6px 6px 0;
}

/* Loading State */
.ant-spin-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Selected Indicator */
.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .image-crawler-modal {
    width: 95% !important;
  }

  .image-crawler-modal .ant-modal-body {
    height: calc(85vh - 110px) !important;
  }
}

/* Custom Scrollbar */
.image-crawler-modal .ant-modal-body::-webkit-scrollbar {
  width: 6px;
}

.image-crawler-modal .ant-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.image-crawler-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.image-crawler-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for image loading */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.image-card {
  animation: fadeIn 0.3s ease-out;
}

/* Error state for images */
.image-error {
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
}
