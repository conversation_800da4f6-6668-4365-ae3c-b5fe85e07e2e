import { InboxOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { Button, message, Modal, Typography, Upload } from 'antd';
import React, { useEffect, useState } from 'react';
import { uploadGallery } from '../../../components/upload/api';
import { MediaFile } from '../../../components/upload/type';

const { Text } = Typography;
const { Dragger } = Upload;

interface MediaUploadProps {
  /** Whether to allow multiple file uploads */
  multiple?: boolean;
  /** Maximum number of files allowed */
  maxCount?: number;
  /** Accept file types */
  accept?: string;
  /** Initial images to display */
  initialImages?: MediaFile[];
  /** Callback when files change */
  onChange?: (files: MediaFile[]) => void;
  /** Upload mode: 'card' | 'dragger' | 'button' */
  mode?: 'card' | 'dragger' | 'button';
  /** Show upload list */
  showUploadList?: boolean;
  /** Custom upload button text */
  uploadText?: string;
  /** Disabled state */
  disabled?: boolean;
}

const MediaUpload: React.FC<MediaUploadProps> = ({
  multiple = true,
  maxCount = 8,
  accept = 'image/*',
  initialImages = [],
  onChange,
  mode = 'card',
  showUploadList = true,
  uploadText = 'Upload',
  disabled = false,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [uploading, setUploading] = useState(false);

  // Initialize file list from initial images
  useEffect(() => {
    if (initialImages.length > 0) {
      const initialFileList: UploadFile[] = initialImages.map((img, index) => ({
        uid: String(img.id || img.id || `initial-${index}`),
        name: img.filename || img.original_filename || `image-${index}`,
        status: 'done',
        url: img.url || img.public_url, // API returns 'url' field
        response: img,
      }));
      setFileList(initialFileList);
    }
  }, [initialImages]);

  // Handle preview
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as File);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(
      file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1),
    );
  };

  // Convert file to base64
  const getBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  // Handle file change
  const handleChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];

    // Limit the number of files
    if (maxCount && newFileList.length > maxCount) {
      newFileList = newFileList.slice(-maxCount);
    }

    setFileList(newFileList);

    // Extract successful uploads and call onChange
    const successfulUploads = newFileList
      .filter((file) => file.status === 'done' && file.response)
      .map((file) => file.response as MediaFile);

    if (onChange) {
      onChange(successfulUploads);
    }
  };

  // Custom upload function
  const customUpload = async (options: any) => {
    const { onSuccess, onError, file, onProgress } = options;
    setUploading(true);

    try {
      const response = await uploadGallery(
        {
          file: file,
          is_public: true,
          // folder_id can be added here if needed
        },
        {
          // Let the browser set Content-Type automatically for FormData
          onUploadProgress: (progressEvent: any) => {
            const percent = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total,
            );
            onProgress({ percent });
          },
        },
      );

      if (response.status.success) {
        // Transform the response data to include legacy fields for compatibility
        const transformedData: MediaFile = {
          ...response.data,
          _id: String(response.data.id),
          filename: file.name, // Use the original file name
          original_filename: file.name,
          public_url: response.data.url, // API returns 'url' field
          mimetype: file.type, // Use the file's MIME type
          content_type: file.type,
          size: file.size,
          media_type: file.type.startsWith('image/') ? 'image' : 'file',
        };

        onSuccess(transformedData);
        message.success(`${file.name} uploaded successfully!`);
      } else {
        onError(new Error(response.status.message));
        message.error(`${file.name} upload failed: ${response.status.message}`);
      }
    } catch (error) {
      onError(error);
      message.error(`${file.name} upload failed!`);
    } finally {
      setUploading(false);
    }
  };

  // Handle remove
  const handleRemove = (file: UploadFile) => {
    const newFileList = fileList.filter((item) => item.uid !== file.uid);
    setFileList(newFileList);

    const successfulUploads = newFileList
      .filter((item) => item.status === 'done' && item.response)
      .map((item) => item.response as MediaFile);

    if (onChange) {
      onChange(successfulUploads);
    }
  };

  // Upload button
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>{uploadText}</div>
    </div>
  );

  // Render based on mode
  const renderUpload = () => {
    const commonProps: UploadProps = {
      multiple,
      accept,
      fileList,
      onChange: handleChange,
      onPreview: handlePreview,
      onRemove: handleRemove,
      customRequest: customUpload,
      disabled: disabled || uploading,
      showUploadList,
    };

    switch (mode) {
      case 'dragger':
        return (
          <Dragger {...commonProps} style={{ padding: '20px' }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              Click or drag files to this area to upload
            </p>
            <p className="ant-upload-hint">
              Support for single or bulk upload.{' '}
              {multiple ? `Maximum ${maxCount} files.` : 'Single file only.'}
            </p>
          </Dragger>
        );

      case 'button':
        return (
          <Upload {...commonProps}>
            <Button
              icon={<UploadOutlined />}
              loading={uploading}
              disabled={disabled}
            >
              {uploadText}
            </Button>
          </Upload>
        );

      case 'card':
      default:
        return (
          <Upload {...commonProps} listType="picture-card">
            {(!multiple && fileList.length >= 1) || fileList.length >= maxCount
              ? null
              : uploadButton}
          </Upload>
        );
    }
  };

  return (
    <div className="media-upload">
      {renderUpload()}

      {/* Preview Modal */}
      <Modal
        open={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewOpen(false)}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>

      {/* Upload Progress */}
      {uploading && (
        <div style={{ marginTop: 16 }}>
          <Text type="secondary">Uploading...</Text>
        </div>
      )}
    </div>
  );
};

export default MediaUpload;
