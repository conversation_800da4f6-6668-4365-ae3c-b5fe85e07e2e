import React, { useState } from 'react';
import {
  Mo<PERSON>,
  Input,
  <PERSON><PERSON>,
  Card,
  Row,
  Col,
  Typo<PERSON>,
  Alert,
  Spin,
  Empty,
  message,
  Form,
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './image-crawler.css';

interface CrawledImage {
  id: string;
  url: string;
  alt?: string;
  width?: number;
  height?: number;
  size?: string;
}

interface ImageCrawlerProps {
  open: boolean;
  onClose: () => void;
  onSelect: (images: CrawledImage[]) => void;
}

const ImageCrawler: React.FC<ImageCrawlerProps> = ({
  open,
  onClose,
  onSelect,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [crawlUrl, setCrawlUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [crawledImages, setCrawledImages] = useState<CrawledImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<CrawledImage[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleCrawl = async () => {
    if (!crawlUrl.trim()) {
      message.error('Vui lòng nhập URL để crawl ảnh');
      return;
    }

    setLoading(true);
    setError(null);
    setCrawledImages([]);
    setSelectedImages([]);

    try {
      // Simulate crawling process - In real implementation, this would call an API
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock crawled images data
      const mockImages: CrawledImage[] = [
        {
          id: '1',
          url: 'https://picsum.photos/300/200?random=1',
          alt: 'Sample Image 1',
          width: 300,
          height: 200,
          size: '45 KB',
        },
        {
          id: '2',
          url: 'https://picsum.photos/400/300?random=2',
          alt: 'Sample Image 2',
          width: 400,
          height: 300,
          size: '67 KB',
        },
        {
          id: '3',
          url: 'https://picsum.photos/350/250?random=3',
          alt: 'Sample Image 3',
          width: 350,
          height: 250,
          size: '52 KB',
        },
        {
          id: '4',
          url: 'https://picsum.photos/500/350?random=4',
          alt: 'Sample Image 4',
          width: 500,
          height: 350,
          size: '89 KB',
        },
        {
          id: '5',
          url: 'https://picsum.photos/320/240?random=5',
          alt: 'Sample Image 5',
          width: 320,
          height: 240,
          size: '48 KB',
        },
        {
          id: '6',
          url: 'https://picsum.photos/450/300?random=6',
          alt: 'Sample Image 6',
          width: 450,
          height: 300,
          size: '72 KB',
        },
      ];

      setCrawledImages(mockImages);
      message.success(`Đã tìm thấy ${mockImages.length} ảnh`);
    } catch (err) {
      setError('Có lỗi xảy ra khi crawl ảnh. Vui lòng thử lại.');
      message.error('Crawl ảnh thất bại');
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = (image: CrawledImage) => {
    const isSelected = selectedImages.some(
      (selected) => selected.id === image.id,
    );

    if (isSelected) {
      setSelectedImages(
        selectedImages.filter((selected) => selected.id !== image.id),
      );
    } else {
      setSelectedImages([...selectedImages, image]);
    }
  };

  const handleSelectAll = () => {
    if (selectedImages.length === crawledImages.length) {
      setSelectedImages([]);
    } else {
      setSelectedImages([...crawledImages]);
    }
  };

  const handleSubmit = () => {
    if (selectedImages.length === 0) {
      message.warning('Vui lòng chọn ít nhất một ảnh');
      return;
    }

    onSelect(selectedImages);
    onClose();
  };

  const handleClose = () => {
    setCrawlUrl('');
    setCrawledImages([]);
    setSelectedImages([]);
    setError(null);
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      open={open}
      onCancel={handleClose}
      width="90%"
      style={{ top: 20 }}
      styles={{
        body: {
          padding: 0,
          height: 'calc(90vh - 110px)',
          overflow: 'hidden',
        },
        footer: {
          padding: '16px 24px',
          borderTop: '1px solid #f0f0f0',
        },
        header: {
          marginBottom: 0,
          paddingBottom: '12px',
        },
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.65)',
        },
        content: {
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
        },
      }}
      title={
        <span style={{ fontSize: '18px', fontWeight: 600 }}>CRAWL ẢNH</span>
      }
      footer={[
        <div key="footer" className="flex justify-between items-center w-full">
          <div>
            Chọn tất cả ({selectedImages.length}/{crawledImages.length})
          </div>
          <div>
            <Button onClick={handleClose} style={{ marginRight: 8 }}>
              HỦY BỎ
            </Button>
            <Button
              type="primary"
              onClick={handleSubmit}
              disabled={selectedImages.length === 0}
              size="middle"
            >
              TẢI ẢNH
            </Button>
          </div>
        </div>,
      ]}
      className="image-crawler-modal"
    >
      <div className="h-full flex flex-col">
        {/* Search Section */}
        <div className="p-4 border-b">
          <Form form={form} layout="vertical">
            <Form.Item
              label="Nhập link cần crawl"
              name="crawlUrl"
              rules={[
                { required: true, message: 'Vui lòng nhập URL' },
                { type: 'url', message: 'URL không hợp lệ' },
              ]}
            >
              <Input.Search
                placeholder="https://example.com"
                value={crawlUrl}
                onChange={(e) => setCrawlUrl(e.target.value)}
                onSearch={handleCrawl}
                enterButton={
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    loading={loading}
                  >
                    CRAWL
                  </Button>
                }
                size="large"
              />
            </Form.Item>
          </Form>
        </div>

        {/* Content Section */}
        <div className="flex-1 overflow-auto p-4">
          {loading && (
            <div className="flex justify-center items-center h-64">
              <Spin size="large" tip="Đang crawl ảnh..." />
            </div>
          )}

          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          {!loading && crawledImages.length === 0 && !error && (
            <Empty
              description="Không có ảnh nào"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              style={{ marginTop: 64 }}
            >
              <Typography.Text type="secondary">
                Hãy nhập URL để crawl ảnh
              </Typography.Text>
            </Empty>
          )}

          {!loading && crawledImages.length > 0 && (
            <>
              <div className="flex justify-between items-center mb-4">
                <Typography.Title level={5} style={{ margin: 0 }}>
                  Tìm thấy {crawledImages.length} ảnh
                </Typography.Title>
                <Button
                  type={
                    selectedImages.length === crawledImages.length
                      ? 'default'
                      : 'primary'
                  }
                  onClick={handleSelectAll}
                  size="small"
                >
                  {selectedImages.length === crawledImages.length
                    ? 'Bỏ chọn tất cả'
                    : 'Chọn tất cả'}
                </Button>
              </div>

              <Row gutter={[16, 16]}>
                {crawledImages.map((image) => {
                  const isSelected = selectedImages.some(
                    (selected) => selected.id === image.id,
                  );

                  return (
                    <Col xs={12} sm={8} md={6} lg={4} key={image.id}>
                      <Card
                        hoverable
                        className={`image-card ${isSelected ? 'selected' : ''}`}
                        cover={
                          <div className="relative">
                            <img
                              src={image.url}
                              alt={image.alt || `Image ${image.id}`}
                              style={{
                                width: '100%',
                                height: '150px',
                                objectFit: 'cover',
                              }}
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src =
                                  'https://via.placeholder.com/300x150?text=Error';
                              }}
                            />
                            {isSelected && (
                              <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                                <CheckOutlined style={{ fontSize: '12px' }} />
                              </div>
                            )}
                          </div>
                        }
                        onClick={() => handleImageSelect(image)}
                        style={{
                          border: isSelected
                            ? '2px solid #1890ff'
                            : '1px solid #d9d9d9',
                        }}
                      >
                        <Card.Meta
                          description={
                            <div className="text-xs">
                              <div>
                                {image.width} × {image.height}
                              </div>
                              <div>{image.size}</div>
                            </div>
                          }
                        />
                      </Card>
                    </Col>
                  );
                })}
              </Row>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ImageCrawler;
