import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, message, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useNavigateTenant } from '../../../hooks';
import { deleteToppingGroup, getToppingGroups } from '../api';
import { ROUTES } from '../config';
import { useNavigateTenant } from '../../../hooks';
import toppingGroupStore from '../store';
import { DineinToppingGroup, transformToppingGroup } from '../type';
import ToppingGroupList from './list';
import ToppingGroupSearch from './search';

const DineinProductToppingGroupList: React.FC = () => {
  const { t } = useTranslation(['dinein-product-topping-group', 'common']);
  const navigate = useNavigateTenant();
  const [loading, setLoading] = useState(false);
  const [toppingGroups, setToppingGroups] = useState<DineinToppingGroup[]>([]);
  const [searchParams, setSearchParams] = useState<any>({
    page: 1,
    per_page: 10,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchToppingGroups = async (params: any = {}) => {
    setLoading(true);
    try {
      const response = await getToppingGroups({
        ...params,
      });

      if (response.data) {
        setToppingGroups(
          response.data.map((item: any) => transformToppingGroup(item)),
        );
        setPagination({
          ...pagination,
          current: params.page || 1,
          total: response.meta?.total || 0,
        });
      }
    } catch (error) {
      console.error('Failed to fetch topping groups:', error);
      message.error(t('common:messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchToppingGroups(searchParams);
  }, [searchParams]);

  const handleTableChange = (pagination: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
    });
  };

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      ...values,
      page: 1,
    });
  };

  const handleEdit = (id: number) => {
    navigate(`${ROUTES.LIST}/edit/${id}`);
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteToppingGroup(String(id));
      message.success(t('common:messages.deleteSuccess'));
      fetchToppingGroups(searchParams);
    } catch (error) {
      console.error('Failed to delete topping group:', error);
      message.error(t('common:messages.deleteFailed'));
    }
  };

  const handleViewToppings = (group: DineinToppingGroup) => {
    toppingGroupStore.setCurrentGroup(group);
    navigate(`${ROUTES.LIST}/${group.group_id}`);
  };

  const handleCreateToppingGroup = () => {
    navigate(`${ROUTES.LIST}/create`);
  };

  return (
    <div>
      <Card
        title={t('dinein-product-topping-group:title')}
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateToppingGroup}
          >
            {t('common:buttons.create')}
          </Button>
        }
      >
        <ToppingGroupSearch onSearch={handleSearch} />
        <Spin spinning={loading}>
          <ToppingGroupList
            toppingGroups={toppingGroups}
            pagination={pagination}
            onChange={handleTableChange}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onViewToppings={handleViewToppings}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default DineinProductToppingGroupList;
