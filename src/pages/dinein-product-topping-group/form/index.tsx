import { But<PERSON>, Card, Form, Input, InputNumber, message, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { useNavigateTenant } from '../../../hooks';
import {
  createToppingGroup,
  getToppingGroup,
  updateToppingGroup,
} from '../api';
import { formItemLayout, ROUTES, tailFormItemLayout } from '../config';
import { useNavigateTenant } from '../../../hooks';
import { DineinToppingGroup } from '../type';

const { TextArea } = Input;

const ToppingGroupForm: React.FC = () => {
  const { t } = useTranslation(['dinein-product-topping-group', 'common']);
  const navigate = useNavigateTenant();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isEdit = !!id;

  useEffect(() => {
    if (isEdit) {
      fetchToppingGroup();
    }
  }, [isEdit]);

  const fetchToppingGroup = async () => {
    setLoading(true);
    try {
      const response = await getToppingGroup(id!);
      if (response.data) {
        const group = response.data;
        form.setFieldsValue({
          name: group.name,
          description: group.description,
          min_selections: group.min_selections,
          max_selections: group.max_selections,
          display_order: group.display_order,
        });
      }
    } catch (error) {
      console.error('Failed to fetch topping group:', error);
      message.error(t('dinein-product-topping-group:messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const toppingGroupData: DineinToppingGroup = {
        ...values,
      };

      if (isEdit) {
        await updateToppingGroup(id!, toppingGroupData);
        message.success(
          t('dinein-product-topping-group:messages.updateSuccess'),
        );
      } else {
        await createToppingGroup(toppingGroupData);
        message.success(
          t('dinein-product-topping-group:messages.createSuccess'),
        );
      }
      navigate(ROUTES.LIST);
    } catch (error) {
      console.error('Failed to save topping group:', error);
      message.error(
        isEdit
          ? t('dinein-product-topping-group:messages.updateFailed')
          : t('dinein-product-topping-group:messages.createFailed'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(ROUTES.LIST);
  };

  return (
    <Card
      title={
        isEdit
          ? t('dinein-product-topping-group:edit.title')
          : t('dinein-product-topping-group:create.title')
      }
      bordered={false}
    >
      <Spin spinning={loading}>
        <Form
          {...formItemLayout}
          form={form}
          onFinish={handleSubmit}
          initialValues={{
            min_selections: 0,
            max_selections: 0,
            display_order: 0,
          }}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('dinein-product-topping-group:form.name')}
            rules={[
              {
                required: true,
                message: t(
                  'dinein-product-topping-group:validation.nameRequired',
                ),
              },
            ]}
          >
            <Input
              placeholder={t(
                'dinein-product-topping-group:form.namePlaceholder',
              )}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={t('dinein-product-topping-group:form.description')}
          >
            <TextArea
              placeholder={t(
                'dinein-product-topping-group:form.descriptionPlaceholder',
              )}
              rows={4}
            />
          </Form.Item>

          <Form.Item
            name="min_selections"
            label={t('dinein-product-topping-group:form.minSelections')}
            rules={[
              {
                required: true,
                message: t(
                  'dinein-product-topping-group:validation.minSelectionsRequired',
                ),
              },
            ]}
          >
            <InputNumber<number>
              min={0}
              style={{ width: '100%' }}
              placeholder={t(
                'dinein-product-topping-group:form.minSelectionsPlaceholder',
              )}
            />
          </Form.Item>

          <Form.Item
            name="max_selections"
            label={t('dinein-product-topping-group:form.maxSelections')}
            rules={[
              {
                required: true,
                message: t(
                  'dinein-product-topping-group:validation.maxSelectionsRequired',
                ),
              },
            ]}
          >
            <InputNumber<number>
              min={0}
              style={{ width: '100%' }}
              placeholder={t(
                'dinein-product-topping-group:form.maxSelectionsPlaceholder',
              )}
            />
          </Form.Item>

          <Form.Item
            name="display_order"
            label={t('dinein-product-topping-group:form.displayOrder')}
          >
            <InputNumber<number>
              min={0}
              style={{ width: '100%' }}
              placeholder={t(
                'dinein-product-topping-group:form.displayOrderPlaceholder',
              )}
            />
          </Form.Item>

          <Form.Item {...tailFormItemLayout}>
            <Button onClick={handleCancel} style={{ marginRight: 8 }}>
              {t('dinein-product-topping-group:buttons.cancel')}
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit
                ? t('dinein-product-topping-group:buttons.update')
                : t('dinein-product-topping-group:buttons.create')}
            </Button>
          </Form.Item>
        </Form>
      </Spin>
    </Card>
  );
};

export default ToppingGroupForm;
