import {
  ArrowLeftOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Form,
  InputNumber,
  message,
  Modal,
  Select,
  Spin,
  Table,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { formatMoney } from '../../../utils/format';
import { DineinTopping } from '../../dinein-product-topping/type';
import {
  addToppingToGroup,
  getAvailableToppings,
  getToppingGroup,
  getToppingGroupItems,
  removeToppingFromGroup,
  updateToppingInGroup,
} from '../api';
import { MODULE, ROUTES } from '../config';
import { useNavigateTenant } from '../../../hooks';
import { DineinToppingGroup, DineinToppingGroupItem } from '../type';
const logger = ConsoleService.register(MODULE);

const ToppingGroupDetail: React.FC = () => {
  const { t } = useTranslation(['dinein-product-topping-group', 'common']);
  const navigate = useNavigateTenant();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [group, setGroup] = useState<DineinToppingGroup | null>(null);
  const [toppings, setToppings] = useState<DineinToppingGroupItem[]>([]);
  const [availableToppings, setAvailableToppings] = useState<any[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentToppingId, setCurrentToppingId] = useState<number | null>(null);

  useEffect(() => {
    if (id) {
      fetchToppingGroup();
      fetchToppings();
      fetchAvailableToppings();
    }
  }, [id]);

  const fetchToppingGroup = async () => {
    setLoading(true);
    try {
      const response = await getToppingGroup(id!);
      if (response.data) {
        setGroup(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch topping group:', error);
      message.error(t('common:messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const fetchToppings = async () => {
    setLoading(true);
    try {
      const response = await getToppingGroupItems(id!);
      logger(response);
      if (response.data) {
        setToppings(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch toppings:', error);
      message.error(t('common:messages.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableToppings = async () => {
    try {
      const response = await getAvailableToppings({
        is_active: true,
        per_page: 100,
      });
      if (response.data) {
        const options = response.data.map((topping: any) => ({
          label: topping.name,
          value: topping.topping_id,
        }));
        setAvailableToppings(options);
      }
    } catch (error) {
      console.error('Failed to fetch available toppings:', error);
    }
  };

  const handleBack = () => {
    navigate(ROUTES.LIST);
  };

  const showAddToppingModal = () => {
    form.resetFields();
    setIsEditing(false);
    setCurrentToppingId(null);
    setIsModalVisible(true);
  };

  const showEditToppingModal = (record: DineinToppingGroupItem) => {
    form.setFieldsValue({
      topping_id: record.topping_id,
      display_order: record.display_order,
      price_override: record.price_override,
    });
    setIsEditing(true);
    setCurrentToppingId(record.topping_id!);
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    form.resetFields();
    setIsModalVisible(false);
  };

  const handleAddTopping = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (isEditing && currentToppingId) {
        await updateToppingInGroup(id!, String(currentToppingId), values);
        message.success(t('common:messages.updateSuccess'));
      } else {
        await addToppingToGroup(id!, values);
        message.success(t('common:messages.createSuccess'));
      }

      setIsModalVisible(false);
      fetchToppings();
    } catch (error) {
      console.error('Failed to save topping:', error);
      message.error(
        isEditing
          ? t('common:messages.updateFailed')
          : t('common:messages.createFailed'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveTopping = async (toppingId: number) => {
    try {
      await removeToppingFromGroup(id!, String(toppingId));
      message.success(t('common:messages.deleteSuccess'));
      fetchToppings();
    } catch (error) {
      console.error('Failed to remove topping:', error);
      message.error(t('common:messages.deleteFailed'));
    }
  };

  const columns = [
    {
      title: t('dinein-product-topping-group:detail.toppingName'),
      dataIndex: ['topping', 'name'],
      key: 'name',
      render: (name: string, record: DineinTopping) => record.name || '-',
    },
    {
      title: t('dinein-product-topping-group:detail.originalPrice'),
      dataIndex: ['topping', 'price'],
      key: 'price',
      render: (price: number, record: DineinTopping) =>
        record.price ? formatMoney(record.price) : '-',
    },
    {
      title: t('dinein-product-topping-group:detail.priceOverride'),
      dataIndex: 'price_override',
      key: 'price_override',
      render: (price: number) => (price ? formatMoney(price) : '-'),
    },
    {
      title: t('dinein-product-topping-group:detail.displayOrder'),
      dataIndex: 'display_order',
      key: 'display_order',
    },
    {
      title: t('common:table.actions'),
      key: 'action',
      width: 150,
      render: (_: any, record: DineinTopping) => (
        <React.Fragment>
          <Button
            type="default"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditToppingModal(record)}
            style={{ marginRight: 8 }}
            title={t('common:buttons.edit')}
          />
          <Button
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => handleRemoveTopping(record.topping_id!)}
            title={t('common:buttons.delete')}
          />
        </React.Fragment>
      ),
    },
  ];

  // Filter out toppings that are already added to the group when in add mode
  const getFilteredToppings = () => {
    if (isEditing) return availableToppings;

    const existingToppingIds = toppings.map((item) => item.topping_id);
    return availableToppings.filter(
      (option) => !existingToppingIds.includes(option.value),
    );
  };

  return (
    <div>
      <Button
        type="default"
        icon={<ArrowLeftOutlined />}
        onClick={handleBack}
        style={{ marginBottom: 16 }}
      >
        {t('common:buttons.back')}
      </Button>

      <Card
        title={
          <>
            {t('dinein-product-topping-group:detail.title')}
            {group && `: ${group.name}`}
          </>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showAddToppingModal}
          >
            {t('dinein-product-topping-group:detail.addTopping')}
          </Button>
        }
      >
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={toppings}
            rowKey="topping_id"
            pagination={false}
          />
        </Spin>
      </Card>

      <Modal
        title={
          isEditing
            ? t('dinein-product-topping-group:detail.editTopping')
            : t('dinein-product-topping-group:detail.addTopping')
        }
        open={isModalVisible}
        onOk={handleAddTopping}
        onCancel={handleModalCancel}
        confirmLoading={loading}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="topping_id"
            label={t('dinein-product-topping-group:detail.topping')}
            rules={[
              {
                required: true,
                message: t(
                  'dinein-product-topping-group:validation.toppingRequired',
                ),
              },
            ]}
          >
            <Select
              showSearch
              placeholder={t(
                'dinein-product-topping-group:detail.selectTopping',
              )}
              options={getFilteredToppings()}
              filterOption={(input, option) =>
                (option?.label as string)
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
              disabled={isEditing}
            />
          </Form.Item>

          <Form.Item
            name="price_override"
            label={t('dinein-product-topping-group:detail.priceOverride')}
          >
            <InputNumber
              min={0}
              step={0.01}
              style={{ width: '100%' }}
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
              parser={(value) => value?.replace(/\$\s?|(,*)/g, '') || ''}
              placeholder={t(
                'dinein-product-topping-group:detail.priceOverridePlaceholder',
              )}
            />
          </Form.Item>

          <Form.Item
            name="display_order"
            label={t('dinein-product-topping-group:detail.displayOrder')}
            initialValue={0}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder={t(
                'dinein-product-topping-group:detail.displayOrderPlaceholder',
              )}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ToppingGroupDetail;
