import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { BlogKind } from './type';

const url = `/api/admin/v1/${MODULE}`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<BlogKind[]>> {
  const response = await apiService.get<ApiResponsePagination<BlogKind[]>>(
    url,
    { params },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<BlogKind>> {
  const response = await apiService.get<ApiResponse<BlogKind>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<BlogKind>> {
  const response = await apiService.post<ApiResponse<BlogKind>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<BlogKind>> {
  const response = await apiService.put<ApiResponse<BlogKind>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<BlogKind>> {
  const response = await apiService.delete<ApiResponse<BlogKind>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(params: any): Promise<ApiResponse<BlogKind[]>> {
  const response = await apiService.get<ApiResponse<BlogKind[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<BlogKind[]>> {
  const response = await apiService.get<ApiResponse<BlogKind[]>>(`${url}/all`);
  return response.data;
}
