import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { BlogBlockPosts } from '../components';
import { BlogBlock } from '../type';
import { Blog } from '../../blog/type';
import { getItems as getBlogBlocks } from '../api';
import { getItems as getBlogPosts } from '../../blog/api';
import { BackButton } from '../../../components/button';
import '../assets/styles.scss';
export default function BlogBlockPostsPage() {
  const { t } = useTranslation('blog-block');
  const [blocks, setBlocks] = useState<BlogBlock[]>([]);
  const [posts, setPosts] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(false);
  const [postsLoading, setPostsLoading] = useState(false);

  useEffect(() => {
    fetchBlocks();
    fetchPosts();
  }, []);

  const fetchBlocks = async () => {
    setLoading(true);
    try {
      const response = await getBlogBlocks({ page: 1, limit: 100 });
      if (response.status.success) {
        setBlocks(response.data);
      } else {
        message.error(response.status.message);
      }
    } catch (error) {
      message.error('Không thể tải danh sách blocks');
    } finally {
      setLoading(false);
    }
  };

  const fetchPosts = async () => {
    setPostsLoading(true);
    try {
      const response = await getBlogPosts({ page: 1, limit: 100 });
      if (response.status.success) {
        setPosts(response.data);
      } else {
        message.error(response.status.message);
      }
    } catch (error) {
      message.error('Không thể tải danh sách bài viết');
    } finally {
      setPostsLoading(false);
    }
  };

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="flex items-center space-x-2">
          <BackButton destination="blog-block" />
          <div className="text-xl font-bold">Quản lý bài viết trong blocks</div>
        </div>
      </div>

      <BlogBlockPosts
        posts={posts}
        blocks={blocks}
        loading={loading || postsLoading}
      />
    </div>
  );
}
