# Blog Block Posts Component

Component quản lý bài viết trong các blog blocks với tính năng drag & drop và tích hợp API.

## Tính năng

### 1. Drag & Drop

- Kéo thả bài viết từ sidebar vào các blocks
- Sắp xếp lại thứ tự bài viết trong block
- Visual feedback khi đang kéo thả
- Highlight drop zones khi hover
- Cập nhật priority tự động

### 2. Quản lý Posts

- Danh sách bài viết có thể tìm kiếm và lọc
- Hiển thị thông tin chi tiết: ti<PERSON><PERSON> đề, mô tả, tr<PERSON><PERSON> thái, ng<PERSON><PERSON> xu<PERSON><PERSON> bản, độ ưu tiên
- Xóa bài viết khỏi block với API call
- Gán bài viết vào block với API call
- Sắp xếp lại bài viết với API call
- Thông báo thành công/lỗi cho các thao tác

### 3. API Integration

- Tự động gọi API khi thực hiện các thao tác
- Loading states cho từng block
- Error handling và rollback khi API thất bại
- Cập nhật state cục bộ ngay lập tức để UX mượt mà

### 4. Giao diện

- Sidebar chứa danh sách posts
- Tabs hiển thị các blocks với tên và số lượng posts
- Loading spinner cho từng block
- Responsive design
- Styling rõ ràng phân biệt posts và drop zones

## Cách sử dụng

### Import Component

```tsx
import { BlogBlockPosts } from '../pages/blog-block/components';
```

### Props

```tsx
interface BlogBlockPostsProps {
  blocks: BlogBlock[]; // Danh sách blocks
  posts: Blog[]; // Danh sách posts
  loading?: boolean; // Trạng thái loading
  onPostAssign?: (postId: string, blockId: string) => void; // Callback khi gán post
  onPostRemove?: (postId: string, blockId: string) => void; // Callback khi xóa post
  onPostsReorder?: (blockId: string, postIds: string[]) => void; // Callback khi sắp xếp lại
}
```

### Sử dụng cơ bản

```tsx
function MyComponent() {
  const [blocks, setBlocks] = useState<BlogBlock[]>([]);
  const [posts, setPosts] = useState<Blog[]>([]);

  const handlePostAssign = async (postId: string, blockId: string) => {
    // Gọi API để gán post vào block
    await assignPostToBlock(postId, blockId);
  };

  const handlePostRemove = async (postId: string, blockId: string) => {
    // Gọi API để xóa post khỏi block
    await removePostFromBlock(postId, blockId);
  };

  const handlePostsReorder = async (blockId: string, postIds: string[]) => {
    // Gọi API để sắp xếp lại posts trong block
    await reorderPostsInBlock(blockId, postIds);
  };

  return (
    <BlogBlockPosts
      blocks={blocks}
      posts={posts}
      onPostAssign={handlePostAssign}
      onPostRemove={handlePostRemove}
      onPostsReorder={handlePostsReorder}
    />
  );
}
```

## Cấu trúc Files

```
src/pages/blog-block/
├── components/
│   ├── blog-block-posts.tsx      # Component chính
│   ├── blog-block-posts.scss     # Styling
│   └── index.tsx                 # Export component
├── posts/
│   └── index.tsx                 # Page demo sử dụng component
├── i18n/
│   ├── vi.json                   # Translation tiếng Việt
│   └── en.json                   # Translation tiếng Anh
└── README.md                     # Tài liệu này
```

## Dependencies

- `react-beautiful-dnd`: Thư viện drag & drop
- `antd`: UI components
- `react-i18next`: Internationalization

## Truy cập

Component có thể được truy cập qua route: `/blog-block-posts`

## Customization

### Styling

File `blog-block-posts.scss` chứa tất cả styling cho component. Có thể tùy chỉnh:

- Màu sắc drag & drop zones
- Animation effects
- Responsive breakpoints
- Scrollbar styling

### Translation

Thêm các key mới vào files `i18n/vi.json` và `i18n/en.json` để hỗ trợ đa ngôn ngữ.

### API Integration

Implement các callback functions để tích hợp với backend API:

- `onPostAssign`: Gán post vào block
- `onPostRemove`: Xóa post khỏi block
- `onPostsReorder`: Sắp xếp lại posts trong block

## Notes

- Component sử dụng mock data trong demo page
- Cần implement API calls thực tế cho production
- Hỗ trợ responsive design cho mobile
- Có thể mở rộng thêm tính năng filter, sort posts
