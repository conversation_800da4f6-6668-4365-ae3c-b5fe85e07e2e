import { Empty, message, Spin, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { DragDropContext, DropResult } from 'react-beautiful-dnd';
import { useTranslation } from 'react-i18next';
import { Blog } from '../../blog/type';
import * as blogBlockApi from '../api';
import { BlockTab, PostsSidebar } from '../components';
import { BlogBlock, BlogBlockPost } from '../type';
import './blog-block-posts.scss';

const { TabPane } = Tabs;

interface BlogBlockPostsProps {
  blocks?: BlogBlock[];
  posts: Blog[];
  loading?: boolean;
}

const BlogBlockPosts: React.FC<BlogBlockPostsProps> = ({
  blocks: providedBlocks = [],
  posts,
  loading = false,
}) => {
  const { t } = useTranslation('blog-block');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredPosts, setFilteredPosts] = useState<Blog[]>(posts);
  const [blocks, setBlocks] = useState<BlogBlock[]>([]);
  const [activeTab, setActiveTab] = useState<string>('');
  const [activeTabPosts, setActiveTabPosts] = useState<BlogBlockPost[]>([]);
  const [loadingActiveTab, setLoadingActiveTab] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Load blocks from API and initialize
  useEffect(() => {
    const loadBlocks = async () => {
      try {
        // If no blocks provided, load from API
        if (providedBlocks.length === 0) {
          const response = await blogBlockApi.getItems({ page: 1, limit: 100 });

          if (response.data && response.data.length > 0) {
            const apiBlocks = response.data;
            setBlocks(apiBlocks);
            setActiveTab(apiBlocks[0].id.toString());
          }
        } else {
          // Use provided blocks
          setBlocks(providedBlocks);
          if (providedBlocks.length > 0) {
            setActiveTab(providedBlocks[0].id.toString());
          }
        }
      } catch (error) {
        console.error('Failed to load blocks:', error);
      }
    };

    loadBlocks();
  }, [providedBlocks]);

  // Load posts for active tab only
  useEffect(() => {
    const loadActiveTabPosts = async () => {
      if (!activeTab) {
        return;
      }

      setLoadingActiveTab(true);
      try {
        const response = await blogBlockApi.getBlockPosts(activeTab);
        setActiveTabPosts(response.data || []);
        console.log('Posts loaded for block:', activeTab, response.data);
      } catch (error) {
        console.error(`Failed to load posts for block ${activeTab}:`, error);
        setActiveTabPosts([]);
      } finally {
        setLoadingActiveTab(false);
      }
    };

    loadActiveTabPosts();
  }, [activeTab]);

  // Filter posts based on search term and exclude posts already in active block
  useEffect(() => {
    let availablePosts = posts;

    // Exclude posts that are already in the active block
    if (activeTabPosts.length > 0) {
      const assignedPostIds = activeTabPosts.map((bp) => bp.post_id.toString());
      availablePosts = posts.filter(
        (post) => !assignedPostIds.includes(post.post_id?.toString() || ''),
      );
    }

    if (!searchTerm) {
      setFilteredPosts(availablePosts);
    } else {
      const filtered = availablePosts.filter(
        (post) =>
          post.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          post.description?.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredPosts(filtered);
    }
  }, [searchTerm, posts, activeTabPosts]);

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = (result: DropResult) => {
    setIsDragging(false);

    const { destination, source, draggableId } = result;

    if (!destination) {
      return;
    }

    // If dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // Handle drag from sidebar to block
    if (
      source.droppableId === 'sidebar' &&
      destination.droppableId.startsWith('block-')
    ) {
      const blockId = destination.droppableId.replace('block-', '');
      // Extract post_id from sidebar draggableId (format: sidebar-{post_id})
      const postId = draggableId.replace('sidebar-', '');
      const post = posts.find((p) => p.post_id?.toString() === postId);

      if (post) {
        handleAssignPost(postId, blockId, destination.index);
      }
      return;
    }

    // Handle reordering within the same block
    if (
      source.droppableId === destination.droppableId &&
      source.droppableId.startsWith('block-')
    ) {
      const blockId = source.droppableId.replace('block-', '');

      // Only handle reordering for active tab
      if (blockId === activeTab) {
        const newPosts = Array.from(activeTabPosts);
        const [reorderedPost] = newPosts.splice(source.index, 1);
        newPosts.splice(destination.index, 0, reorderedPost);

        // Update local state immediately for better UX
        setActiveTabPosts(newPosts);

        // Call API to update order
        handleReorderPosts(blockId, newPosts);
      }
    }
  };

  const handleAssignPost = async (
    postId: string,
    blockId: string,
    priority: number = 1,
  ) => {
    try {
      const post = posts.find((p) => p.post_id?.toString() === postId);
      if (!post) return;

      // Call API to assign post
      await blogBlockApi.addPostToBlock(blockId, {
        post_id: parseInt(postId),
        priority: priority + 1, // API expects 1-based priority
      });

      // Update local state only if it's the active tab
      if (blockId === activeTab) {
        const newBlockPost: BlogBlockPost = {
          id: `${blockId}-${postId}`,
          blog_block_id: blockId,
          post_id: parseInt(postId),
          priority: priority + 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          post: {
            id: post.post_id || 0,
            title: post.title || '',
            slug: post.slug || '',
            excerpt: post.description,
            content: post.content,
            status: post.status || '',
            published_at: post.published_at,
          },
        };

        setActiveTabPosts((prev) => {
          const updated = [...prev, newBlockPost].sort(
            (a, b) => a.priority - b.priority,
          );
          return updated;
        });
      }

      message.success(t('postAssignedSuccess'));
    } catch (error) {
      console.error('Failed to assign post:', error);
      message.error(t('postAssignedError'));
    }
  };

  const handleRemovePost = async (postId: string, blockId: string) => {
    try {
      await blogBlockApi.removePostFromBlock(blockId, postId);
      // Update local state only if it's the active tab
      if (blockId === activeTab) {
        setActiveTabPosts((prev) =>
          prev.filter((post) => post.post_id.toString() !== postId),
        );
      }
      message.success(t('postRemovedSuccess'));
    } catch (error) {
      console.error('Error removing post:', error);
      message.error(t('postRemoveError'));
    }
  };

  const handleReorderPosts = async (
    blockId: string,
    newPosts: BlogBlockPost[],
  ) => {
    const previousState = activeTabPosts;

    try {
      const postOrders = newPosts.map((post, index) => ({
        post_id: post.post_id,
        priority: index + 1,
      }));

      await blogBlockApi.reorderPosts(blockId, { post_orders: postOrders });
      message.success(t('postsReorderedSuccess'));
    } catch (error) {
      console.error('Error reordering posts:', error);
      message.error(t('postsReorderError'));
      // Revert to previous state on error
      setActiveTabPosts(previousState);
    }
  };

  if (loading) {
    return (
      <div className="blog-block-posts-loading">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <DragDropContext
      key={`dnd-context-${activeTab}`}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="blog-block-posts">
        {/* Sidebar with posts list */}
        <PostsSidebar
          posts={filteredPosts}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />

        {/* Main content with blocks */}
        <div className="blocks-content">
          <div className="content-header">
            <h3>{t('blogBlocks')}</h3>
          </div>

          {blocks.length === 0 ? (
            <Empty description={t('noBlocks')} />
          ) : (
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              type="card"
              className="blocks-tabs"
            >
              {blocks.map((block) => {
                return (
                  <TabPane
                    tab={`${block.name || 'Unnamed Block'}`}
                    key={block.id}
                  >
                    <BlockTab
                      block={block}
                      blockPosts={activeTabPosts}
                      loading={loadingActiveTab}
                      onRemovePost={handleRemovePost}
                    />
                  </TabPane>
                );
              })}
            </Tabs>
          )}
        </div>
      </div>
    </DragDropContext>
  );
};

export default BlogBlockPosts;

// CSS styles
const styles = `
.block-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.block-drop-zone {
  min-height: 100px;
  padding: 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
}

.block-drop-zone.drag-over {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.empty-drop-zone {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.post-item {
  margin-bottom: 8px;
  cursor: move;
}

.post-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.post-priority {
  font-weight: bold;
  color: #1890ff;
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  document.head.appendChild(styleElement);
}
