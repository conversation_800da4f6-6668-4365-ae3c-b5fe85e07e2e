.blog-block-posts {
  display: flex;
  height: calc(100vh - 200px);
  gap: 16px;
  padding: 16px;
  background: #f5f5f5;

  .posts-sidebar {
    width: 350px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    max-height: 100%;

    .sidebar-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      h3 {
        margin: 0 0 16px 0;
        color: #262626;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .posts-list {
      flex: 1;
      overflow-y: auto;
      padding: 8px;
      min-height: 200px;
      transition: background-color 0.2s ease;

      &.drag-over {
        background-color: #e6f7ff;
        border: 2px dashed #1890ff;
        border-radius: 4px;
      }

      .blog-post-item {
        margin-bottom: 8px;
        padding: 12px;
        background: #fafafa;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        cursor: grab;
        transition: all 0.2s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
        }

        &.dragging {
          opacity: 0.8;
          transform: rotate(5deg);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .post-content {
          .post-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            .drag-handle {
              color: #8c8c8c;
              font-size: 12px;
            }

            .post-title {
              margin: 0;
              font-size: 14px;
              font-weight: 500;
              color: #262626;
              line-height: 1.4;
              flex: 1;
            }
          }

          .post-description {
            margin: 0 0 8px 0;
            font-size: 12px;
            color: #595959;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .post-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;

            .publish-date {
              font-size: 11px;
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }

  .blocks-content {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    max-height: 100%;

    .content-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      h3 {
        margin: 0;
        color: #262626;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .blocks-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;

      .ant-tabs-content-holder {
        flex: 1;
        overflow: hidden;
      }

      .ant-tabs-tabpane {
        height: 100%;
        overflow: hidden;
      }

      .block-drop-zone {
        height: 100%;
        padding: 16px;
        overflow-y: auto;
        min-height: 300px;
        transition: all 0.2s ease;

        &.drag-over {
          background-color: #f6ffed;
          border: 2px dashed #52c41a;
          border-radius: 4px;
        }

        .empty-drop-zone {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          background: #fafafa;
          transition: all 0.2s ease;

          &:hover {
            border-color: #1890ff;
            background: #f0f8ff;
          }
        }

        .block-post-item {
          margin-bottom: 12px;
          padding: 16px;
          background: #f9f9f9;
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          cursor: grab;
          transition: all 0.2s ease;

          &:hover {
            border-color: #52c41a;
            box-shadow: 0 2px 6px rgba(82, 196, 26, 0.2);
          }

          .post-content {
            .post-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              .drag-handle {
                color: #8c8c8c;
                font-size: 14px;
              }

              .post-title {
                margin: 0;
                font-size: 15px;
                font-weight: 500;
                color: #262626;
                line-height: 1.4;
                flex: 1;
              }

              .remove-btn {
                color: #ff4d4f;
                opacity: 0;
                transition: opacity 0.2s ease;

                &:hover {
                  color: #ff7875;
                }
              }
            }

            &:hover .remove-btn {
              opacity: 1;
            }

            .post-description {
              margin: 0 0 12px 0;
              font-size: 13px;
              color: #595959;
              line-height: 1.5;
            }

            .post-meta {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 8px;

              .publish-date {
                font-size: 12px;
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }
  }

  &-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .blog-block-posts {
    .posts-sidebar {
      width: 300px;
    }
  }
}

@media (max-width: 768px) {
  .blog-block-posts {
    flex-direction: column;
    height: auto;

    .posts-sidebar {
      width: 100%;
      max-height: 300px;
    }

    .blocks-content {
      min-height: 400px;
    }
  }
}

// Animation for drag and drop
@keyframes dragEnter {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.drag-over {
  animation: dragEnter 0.3s ease;
}

// Custom scrollbar
.posts-list::-webkit-scrollbar,
.block-drop-zone::-webkit-scrollbar {
  width: 6px;
}

.posts-list::-webkit-scrollbar-track,
.block-drop-zone::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.posts-list::-webkit-scrollbar-thumb,
.block-drop-zone::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.posts-list::-webkit-scrollbar-thumb:hover,
.block-drop-zone::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
