import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import {
  BlogBlock,
  BlogBlockPost,
  PostOrder,
  BatchPostResult,
  ReorderResult,
} from './type';

const url = `/api/admin/v1/${MODULE}/blog-blocks`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<BlogBlock[]>> {
  const response = await apiService.get<ApiResponsePagination<BlogBlock[]>>(
    url,
    { params },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<BlogBlock>> {
  const response = await apiService.get<ApiResponse<BlogBlock>>(`${url}/${id}`);
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<BlogBlock>> {
  const response = await apiService.post<ApiResponse<BlogBlock>>(url, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<BlogBlock>> {
  const response = await apiService.put<ApiResponse<BlogBlock>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<BlogBlock>> {
  const response = await apiService.delete<ApiResponse<BlogBlock>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function getOptions(): Promise<ApiResponse<SelectOption[]>> {
  const response = await apiService.get<ApiResponse<SelectOption[]>>(
    `${url}/options`,
  );
  return response.data;
}

export async function getSearch(
  params: any,
): Promise<ApiResponse<BlogBlock[]>> {
  const response = await apiService.get<ApiResponse<BlogBlock[]>>(
    `${url}/search`,
    { params },
  );
  return response.data;
}

export async function getAll(): Promise<ApiResponse<BlogBlock[]>> {
  const response = await apiService.get<ApiResponse<BlogBlock[]>>(`${url}/all`);
  return response.data;
}

// Blog Block Posts API
export async function getBlockPosts(
  blockId: string,
  params?: { limit?: number; cursor?: string },
): Promise<ApiResponsePagination<BlogBlockPost[]>> {
  const response = await apiService.get<ApiResponsePagination<BlogBlockPost[]>>(
    `${url}/${blockId}/posts`,
    { params },
  );
  return response.data;
}

export async function addPostToBlock(
  blockId: string,
  payload: { post_id: number; priority: number },
): Promise<ApiResponse<BlogBlockPost>> {
  const response = await apiService.post<ApiResponse<BlogBlockPost>>(
    `${url}/${blockId}/posts`,
    payload,
  );
  return response.data;
}

export async function removePostFromBlock(
  blockId: string,
  postId: string,
): Promise<ApiResponse<{ message: string }>> {
  const response = await apiService.delete<ApiResponse<{ message: string }>>(
    `${url}/${blockId}/posts/${postId}`,
  );
  return response.data;
}

export async function updatePostPriority(
  blockId: string,
  postId: string,
  payload: { priority: number },
): Promise<ApiResponse<BlogBlockPost>> {
  const response = await apiService.put<ApiResponse<BlogBlockPost>>(
    `${url}/${blockId}/posts/${postId}`,
    payload,
  );
  return response.data;
}

export async function batchAddPosts(
  blockId: string,
  payload: { posts: Array<{ post_id: number; priority: number }> },
): Promise<ApiResponse<BatchPostResult>> {
  const response = await apiService.post<ApiResponse<BatchPostResult>>(
    `${url}/${blockId}/posts/batch`,
    payload,
  );
  return response.data;
}

export async function reorderPosts(
  blockId: string,
  payload: { post_orders: PostOrder[] },
): Promise<ApiResponse<ReorderResult>> {
  const response = await apiService.put<ApiResponse<ReorderResult>>(
    `${url}/${blockId}/posts/reorder`,
    payload,
  );
  return response.data;
}
