import { DragOutlined } from '@ant-design/icons';
import { Tag } from 'antd';
import React from 'react';
import { Blog } from '../../blog/type';

interface PostItemProps {
  post: Blog;
  index: number;
  isDragging?: boolean;
}

const PostItem: React.FC<PostItemProps> = ({
  post,
  index,
  isDragging = false,
}) => {
  return (
    <div
      className={`blog-post-item ${isDragging ? 'dragging' : ''}`}
      key={post.post_id}
    >
      <div className="post-content">
        <div className="post-header">
          <DragOutlined className="drag-handle" />
          <h4 className="post-title">{post.title}</h4>
        </div>
        <p className="post-description">{post.description}</p>
        <div className="post-meta">
          <Tag color="blue">{post.status}</Tag>
          {post.published_at && (
            <span className="publish-date">
              {new Date(post.published_at).toLocaleDateString()}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export { PostItem };
export default PostItem;
