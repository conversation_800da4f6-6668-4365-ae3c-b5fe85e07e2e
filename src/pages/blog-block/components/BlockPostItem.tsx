import { DeleteOutlined, DragOutlined } from '@ant-design/icons';
import { Button, Tag } from 'antd';
import React from 'react';
import { BlogBlockPost } from '../type';

interface BlockPostItemProps {
  blockPost: BlogBlockPost;
  index: number;
  blockId: string;
  onRemove: (postId: string, blockId: string) => void;
}

const BlockPostItem: React.FC<BlockPostItemProps> = ({
  blockPost,
  index,
  blockId,
  onRemove,
}) => {
  return (
    <div className="block-post-item" key={blockPost.id}>
      <div className="post-content">
        <div className="post-header">
          <DragOutlined className="drag-handle" />
          <h4 className="post-title">{blockPost.post?.title || 'Untitled'}</h4>
          <div className="post-priority">
            <Tag color="blue">#{blockPost.priority}</Tag>
          </div>
          <Button
            type="text"
            size="small"
            icon={<DeleteOutlined />}
            onClick={() =>
              onRemove(blockPost.post_id?.toString() || '', blockId)
            }
            className="remove-btn"
          />
        </div>

        <div className="post-meta">
          <Tag color="green">{blockPost.post?.status || 'draft'}</Tag>
          {blockPost.post?.published_at && (
            <span className="publish-date">
              {new Date(blockPost.post.published_at).toLocaleDateString()}
            </span>
          )}
          {blockPost.post?.author && (
            <span className="post-author">by {blockPost.post.author.name}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlockPostItem;
