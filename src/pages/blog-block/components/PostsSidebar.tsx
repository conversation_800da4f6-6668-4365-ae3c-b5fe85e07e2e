import { Empty, Input } from 'antd';
import React from 'react';
import { Draggable, Droppable } from 'react-beautiful-dnd';
import { useTranslation } from 'react-i18next';
import { Blog } from '../../blog/type';
import PostItem from './PostItem';

const { Search } = Input;

interface PostsSidebarProps {
  posts: Blog[];
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

const PostsSidebar: React.FC<PostsSidebarProps> = ({
  posts,
  searchTerm,
  onSearchChange,
}) => {
  const { t } = useTranslation('blog-block');

  return (
    <div className="posts-sidebar">
      <div className="sidebar-header">
        <h3>{t('availablePosts')}</h3>
        <Search
          placeholder={t('searchPosts')}
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          style={{ marginBottom: 16 }}
        />
      </div>

      <Droppable droppableId="sidebar">
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`posts-list ${snapshot.isDraggingOver ? 'drag-over' : ''}`}
          >
            {posts.length === 0 ? (
              <Empty description={t('noPosts')} />
            ) : (
              posts.map((post, index) => (
                <Draggable
                  key={`sidebar-${post.post_id}`}
                  draggableId={`sidebar-${post.post_id}`}
                  index={index}
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                    >
                      <PostItem
                        post={post}
                        index={index}
                        isDragging={snapshot.isDragging}
                      />
                    </div>
                  )}
                </Draggable>
              ))
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
};

export default PostsSidebar;
