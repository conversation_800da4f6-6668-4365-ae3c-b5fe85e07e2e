import { Empty, Spin } from 'antd';
import React from 'react';
import { Draggable, Droppable } from 'react-beautiful-dnd';
import { useTranslation } from 'react-i18next';
import { BlogBlock, BlogBlockPost } from '../type';
import BlockPostItem from './BlockPostItem';

interface BlockTabProps {
  block: BlogBlock;
  loading: boolean;
  blockPosts: BlogBlockPost[];
  onRemovePost: (postId: string, blockId: string) => void;
}

const BlockTab: React.FC<BlockTabProps> = ({
  block,
  blockPosts,
  loading,
  onRemovePost,
}) => {
  const { t } = useTranslation('blog-block');

  console.log('BlockTab props:', { block, blockPosts: blockPosts });

  if (loading) {
    return (
      <div className="block-loading">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Droppable droppableId={`block-${block.id}`}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.droppableProps}
          className={`block-drop-zone ${
            snapshot.isDraggingOver ? 'drag-over' : ''
          }`}
        >
          {blockPosts.length === 0 ? (
            <div className="empty-drop-zone">
              <Empty
                description={t('dropPostsHere')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          ) : (
            blockPosts.map((blockPost, index) => (
              <Draggable
                key={`block-${block.id}-${blockPost.post_id}`}
                draggableId={`block-${block.id}-${blockPost.post_id}`}
                index={index}
              >
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                  >
                    <BlockPostItem
                      blockPost={blockPost}
                      index={index}
                      blockId={block.id}
                      onRemove={onRemovePost}
                    />
                  </div>
                )}
              </Draggable>
            ))
          )}
          {provided.placeholder}
        </div>
      )}
    </Droppable>
  );
};

export default BlockTab;
