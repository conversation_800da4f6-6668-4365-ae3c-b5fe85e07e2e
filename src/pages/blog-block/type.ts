export interface BlogBlock {
  _id: string;
  id: string;
  name: string;
  description?: string;
  type: 'featured' | 'recent' | 'popular' | 'custom';
  config: {
    limit: number;
    show_excerpt?: boolean;
    show_author?: boolean;
    show_date?: boolean;
  };
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Legacy fields for backward compatibility
  serverIP?: string;
  startDate?: string;
  lastDate?: string;
  totalBlogBlocks?: number;
  totalSources?: number;
  status?: string;
}

export interface BlogBlockPost {
  id: string;
  blog_block_id: string;
  post_id: number;
  priority: number;
  created_at: string;
  updated_at: string;
  post?: {
    id: number;
    title: string;
    slug: string;
    excerpt?: string;
    content?: string;
    status: string;
    published_at?: string;
    author?: {
      id: number;
      name: string;
      email: string;
    };
  };
}

export interface PostOrder {
  post_id: number;
  priority: number;
}

export interface BatchPostResult {
  created_count: number;
  failed_count: number;
  created_posts: BlogBlockPost[];
  failed_posts: Array<{
    post_id: number;
    error: string;
  }>;
}

export interface ReorderResult {
  updated_count: number;
  failed_count: number;
  updated_posts: BlogBlockPost[];
  failed_posts: Array<{
    post_id: number;
    error: string;
  }>;
}
