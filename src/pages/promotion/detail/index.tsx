import {
  ArrowLeftOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  message,
  Row,
  Space,
  Spin,
  Tabs,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { ButtonLink } from '../../../components/button';
import { deleteItem, getItem } from '../api';
import { Promotion } from '../type';

const { Title } = Typography;
const { TabPane } = Tabs;

const PromotionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigateTenant();
  const [promotion, setPromotion] = useState<Promotion | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    if (id) {
      loadPromotion(parseInt(id));
    }
  }, [id]);

  const loadPromotion = async (promotionId: number) => {
    setLoading(true);
    try {
      const response = await getItem(promotionId);
      setPromotion(response.data);
    } catch (error) {
      console.error('Error loading promotion:', error);
      message.error('Failed to load promotion details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!promotion) return;

    if (window.confirm('Are you sure you want to delete this promotion?')) {
      try {
        await deleteItem(promotion.id);
        message.success('Promotion deleted successfully');
        navigate('/promotion');
      } catch (error) {
        console.error('Error deleting promotion:', error);
        message.error('Failed to delete promotion');
      }
    }
  };

  const getStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return <Tag color="red">Inactive</Tag>;
      case 1:
        return <Tag color="green">Active</Tag>;
      case 2:
        return <Tag color="gray">Expired</Tag>;
      case 3:
        return <Tag color="blue">Scheduled</Tag>;
      default:
        return <Tag>Unknown</Tag>;
    }
  };

  const formatDiscountValue = (promotion: Promotion) => {
    if (promotion.discount_type === 'percentage') {
      return `${promotion.discount_value}%`;
    } else if (promotion.discount_type === 'fixed_amount') {
      return `$${promotion.discount_value.toFixed(2)}`;
    } else if (promotion.discount_type === 'free_shipping') {
      return 'Free Shipping';
    } else {
      return 'Buy X Get Y';
    }
  };

  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (!promotion) {
    return (
      <div>
        <Title level={2}>Promotion not found</Title>
        <ButtonLink to="/promotion" type="primary" absolute={true}>
          Back to Promotions
        </ButtonLink>
      </div>
    );
  }

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Space>
            <ButtonLink
              to="/promotion"
              icon={<ArrowLeftOutlined />}
              absolute={true}
            >
              Back
            </ButtonLink>
            <Title level={2} style={{ margin: 0 }}>
              {promotion.name} {getStatusTag(promotion.status)}
            </Title>
          </Space>
        </Col>
        <Col>
          <Space>
            <ButtonLink
              to={`promotion/${promotion.id}/edit`}
              type="primary"
              icon={<EditOutlined />}
            >
              Edit
            </ButtonLink>
            <Button danger icon={<DeleteOutlined />} onClick={handleDelete}>
              Delete
            </Button>
          </Space>
        </Col>
      </Row>

      <Card>
        <Tabs defaultActiveKey="info">
          <TabPane tab="Basic Information" key="info">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="ID">{promotion.id}</Descriptions.Item>
              <Descriptions.Item label="Code">
                {promotion.code || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Promotion Type">
                {promotion.promotion_type}
              </Descriptions.Item>
              <Descriptions.Item label="Discount">
                {formatDiscountValue(promotion)}
              </Descriptions.Item>
              <Descriptions.Item label="Minimum Order Amount">
                ${promotion.minimum_order_amount.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="Maximum Discount">
                {promotion.maximum_discount
                  ? `$${promotion.maximum_discount.toFixed(2)}`
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Valid Period">
                {dayjs(promotion.start_date).format('YYYY-MM-DD')} to{' '}
                {dayjs(promotion.end_date).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                {getStatusTag(promotion.status)}
              </Descriptions.Item>
              <Descriptions.Item label="Usage Limit Per Promotion">
                {promotion.usage_limit_per_promotion || 'Unlimited'}
              </Descriptions.Item>
              <Descriptions.Item label="Usage Limit Per User">
                {promotion.usage_limit_per_user || 'Unlimited'}
              </Descriptions.Item>
              <Descriptions.Item label="Applies To" span={2}>
                {promotion.applies_to === 'entire_order'
                  ? 'Entire Order'
                  : promotion.applies_to === 'specific_products'
                    ? 'Specific Products'
                    : 'Specific Categories'}
              </Descriptions.Item>
              <Descriptions.Item label="Created At">
                {dayjs(promotion.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="Updated At">
                {dayjs(promotion.updated_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>

            {promotion.description && (
              <>
                <Divider orientation="left">Description</Divider>
                <p>{promotion.description}</p>
              </>
            )}
          </TabPane>

          {promotion.applies_to === 'specific_products' &&
            promotion.products &&
            promotion.products.length > 0 && (
              <TabPane tab="Products" key="products">
                <Descriptions bordered>
                  {promotion.products.map((product) => (
                    <Descriptions.Item
                      label={`Product ID: ${product.product_id}`}
                      key={product.id}
                    >
                      {product.product_name || `Product ${product.product_id}`}
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              </TabPane>
            )}

          {promotion.applies_to === 'specific_categories' &&
            promotion.categories &&
            promotion.categories.length > 0 && (
              <TabPane tab="Categories" key="categories">
                <Descriptions bordered>
                  {promotion.categories.map((category) => (
                    <Descriptions.Item
                      label={`Category ID: ${category.category_id}`}
                      key={category.id}
                    >
                      {category.category_name ||
                        `Category ${category.category_id}`}
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              </TabPane>
            )}

          {promotion.rules && promotion.rules.length > 0 && (
            <TabPane tab="Rules" key="rules">
              <Descriptions bordered>
                {promotion.rules.map((rule) => (
                  <Descriptions.Item
                    label={`Rule: ${rule.rule_type}`}
                    key={rule.id}
                  >
                    {rule.operator}: {JSON.stringify(rule.value)}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </TabPane>
          )}
        </Tabs>
      </Card>
    </div>
  );
};

export default PromotionDetail;
