import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  Tabs,
  Typography,
  message,
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { ButtonLink } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import { createItem, getItem, updateItem } from '../api';
import { PromotionConfig } from '../config';
import i18n from '../i18n';
import { CreateUpdatePromotionRequest, Promotion } from '../type';

const { Title } = Typography;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const PromotionForm: React.FC = () => {
  const navigate = useNavigateTenant();
  const { id } = useParams<{ id: string }>();
  const [form] = useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [promotion, setPromotion] = useState<Promotion | null>(null);
  const [appliesToSpecific, setAppliesToSpecific] = useState(false);

  // Use English translations
  const t = i18n.en;

  useEffect(() => {
    if (id) {
      loadPromotion(parseInt(id));
    }
  }, [id]);

  const loadPromotion = async (promotionId: number) => {
    setLoading(true);
    try {
      const response = await getItem(promotionId);
      const data = response.data;
      setPromotion(data);

      // Format dates for form
      const formData = {
        ...data,
        date_range: [dayjs(data.start_date), dayjs(data.end_date)],
      };

      form.setFieldsValue(formData);

      // Set applies to specific state for conditional rendering
      setAppliesToSpecific(
        data.applies_to === 'specific_products' ||
          data.applies_to === 'specific_categories',
      );
    } catch (error) {
      console.error('Error loading promotion:', error);
      message.error(t.error_load);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setSubmitting(true);

    try {
      const promotionData: CreateUpdatePromotionRequest = {
        name: values.name,
        code: values.code || undefined,
        description: values.description || undefined,
        promotion_type: values.promotion_type,
        discount_type: values.discount_type,
        discount_value: values.discount_value,
        minimum_order_amount: values.minimum_order_amount || 0,
        maximum_discount: values.maximum_discount,
        usage_limit_per_promotion: values.usage_limit_per_promotion,
        usage_limit_per_user: values.usage_limit_per_user,
        start_date: values.date_range[0].format('YYYY-MM-DD HH:mm:ss'),
        end_date: values.date_range[1].format('YYYY-MM-DD HH:mm:ss'),
        status: values.status,
        applies_to: values.applies_to,
        product_ids:
          values.applies_to === 'specific_products' ? values.product_ids : [],
        category_ids:
          values.applies_to === 'specific_categories'
            ? values.category_ids
            : [],
        rules: values.rules || [],
      };

      if (id) {
        const updatedPromo = await updateItem(parseInt(id), promotionData);
        message.success(t.success_update);
        // Navigate to the detail view instead of the list
        navigate(`/promotion/${id}`);
      } else {
        const createdPromo = await createItem(promotionData);
        message.success(t.success_create);
        // Navigate to the detail view of the newly created promotion
        if (createdPromo && createdPromo.data && createdPromo.data.id) {
          navigate(`/promotion/${createdPromo.data.id}`);
        } else {
          navigate('/promotion');
        }
      }
    } catch (error) {
      console.error('Error saving promotion:', error);
      message.error(id ? t.error_update : t.error_create);
    } finally {
      setSubmitting(false);
    }
  };

  const onAppliesToChange = (value: string) => {
    setAppliesToSpecific(
      value === 'specific_products' || value === 'specific_categories',
    );
  };

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>{id ? t.edit : t.create}</Title>
        </Col>
        <Col>
          <Space>
            <ButtonLink to="/promotion" absolute={true}>
              {t.cancel}
            </ButtonLink>
            <Button
              type="primary"
              onClick={() => form.submit()}
              loading={submitting}
            >
              {t.save}
            </Button>
          </Space>
        </Col>
      </Row>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          status: 1,
          promotion_type: 'coupon',
          discount_type: 'percentage',
          applies_to: 'entire_order',
          minimum_order_amount: 0,
          rules: [],
        }}
      >
        <Tabs defaultActiveKey="1">
          <Tabs.TabPane tab={t.tab_info} key="1">
            <Card>
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="name"
                    label={t.name}
                    rules={[
                      {
                        required: true,
                        message: 'Please enter promotion name',
                      },
                    ]}
                  >
                    <Input placeholder="Enter promotion name" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="code"
                    label={t.code}
                    help="Leave empty for automatic promotions"
                  >
                    <Input placeholder="e.g. SUMMER2025" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="description" label={t.description}>
                <TextArea rows={4} placeholder="Enter promotion description" />
              </Form.Item>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="promotion_type"
                    label={t.promotion_type}
                    rules={[
                      {
                        required: true,
                        message: 'Please select promotion type',
                      },
                    ]}
                  >
                    <Select options={PromotionConfig.promotionTypeOptions} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="status"
                    label={t.status}
                    rules={[
                      { required: true, message: 'Please select status' },
                    ]}
                  >
                    <Select options={PromotionConfig.statusOptions} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="date_range"
                label={t.date_range}
                rules={[
                  { required: true, message: 'Please select promotion period' },
                ]}
              >
                <RangePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </Card>
          </Tabs.TabPane>

          <Tabs.TabPane tab="Discount Settings" key="2">
            <Card>
              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="discount_type"
                    label={t.discount_type}
                    rules={[
                      {
                        required: true,
                        message: 'Please select discount type',
                      },
                    ]}
                  >
                    <Select options={PromotionConfig.discountTypeOptions} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="discount_value"
                    label={t.discount_value}
                    rules={[
                      {
                        required: true,
                        message: 'Please enter discount value',
                      },
                    ]}
                  >
                    <InputNumber
                      min={0}
                      placeholder="Enter value"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="minimum_order_amount"
                    label={t.minimum_order_amount}
                  >
                    <InputNumber
                      min={0}
                      placeholder="0.00"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="maximum_discount"
                    label={t.maximum_discount}
                    help="Leave empty for unlimited"
                  >
                    <InputNumber
                      min={0}
                      placeholder="0.00"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="usage_limit_per_promotion"
                    label={t.usage_limit_per_promotion}
                    help="Leave empty for unlimited"
                  >
                    <InputNumber
                      min={0}
                      placeholder="0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    name="usage_limit_per_user"
                    label={t.usage_limit_per_user}
                    help="Leave empty for unlimited"
                  >
                    <InputNumber
                      min={0}
                      placeholder="0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Tabs.TabPane>

          <Tabs.TabPane tab="Application Scope" key="3">
            <Card>
              <Form.Item
                name="applies_to"
                label={t.applies_to}
                rules={[
                  {
                    required: true,
                    message: 'Please select what this promotion applies to',
                  },
                ]}
              >
                <Select
                  onChange={onAppliesToChange}
                  options={PromotionConfig.appliesToOptions}
                />
              </Form.Item>

              {appliesToSpecific && (
                <>
                  {form.getFieldValue('applies_to') === 'specific_products' && (
                    <Form.Item
                      name="product_ids"
                      label="Select Products"
                      rules={[
                        {
                          required: true,
                          message: 'Please select at least one product',
                        },
                      ]}
                    >
                      <Select mode="multiple" placeholder="Select products">
                        {/* This would be populated from your product API */}
                        <Select.Option value={1}>Product 1</Select.Option>
                        <Select.Option value={2}>Product 2</Select.Option>
                        <Select.Option value={3}>Product 3</Select.Option>
                      </Select>
                    </Form.Item>
                  )}

                  {form.getFieldValue('applies_to') ===
                    'specific_categories' && (
                    <Form.Item
                      name="category_ids"
                      label="Select Categories"
                      rules={[
                        {
                          required: true,
                          message: 'Please select at least one category',
                        },
                      ]}
                    >
                      <Select mode="multiple" placeholder="Select categories">
                        {/* This would be populated from your category API */}
                        <Select.Option value={1}>Category 1</Select.Option>
                        <Select.Option value={2}>Category 2</Select.Option>
                        <Select.Option value={3}>Category 3</Select.Option>
                      </Select>
                    </Form.Item>
                  )}
                </>
              )}
            </Card>
          </Tabs.TabPane>

          <Tabs.TabPane tab={t.tab_rules} key="4">
            <Card>
              <Form.List name="rules">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Row
                        gutter={16}
                        key={key}
                        align="middle"
                        style={{ marginBottom: 16 }}
                      >
                        <Col xs={24} sm={8}>
                          <Form.Item
                            {...restField}
                            name={[name, 'rule_type']}
                            rules={[{ required: true, message: 'Required' }]}
                          >
                            <Select
                              placeholder="Rule Type"
                              options={PromotionConfig.ruleTypeOptions}
                            />
                          </Form.Item>
                        </Col>
                        <Col xs={24} sm={7}>
                          <Form.Item
                            {...restField}
                            name={[name, 'operator']}
                            rules={[{ required: true, message: 'Required' }]}
                          >
                            <Select
                              placeholder="Operator"
                              options={PromotionConfig.operatorOptions}
                            />
                          </Form.Item>
                        </Col>
                        <Col xs={24} sm={7}>
                          <Form.Item
                            {...restField}
                            name={[name, 'value']}
                            rules={[{ required: true, message: 'Required' }]}
                          >
                            <Input placeholder="Value" />
                          </Form.Item>
                        </Col>
                        <Col xs={24} sm={2}>
                          <MinusCircleOutlined onClick={() => remove(name)} />
                        </Col>
                      </Row>
                    ))}
                    <Form.Item>
                      <Button
                        type="dashed"
                        onClick={() => add()}
                        block
                        icon={<PlusOutlined />}
                      >
                        Add Rule
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Card>
          </Tabs.TabPane>
        </Tabs>
      </Form>
    </div>
  );
};

export default PromotionForm;
