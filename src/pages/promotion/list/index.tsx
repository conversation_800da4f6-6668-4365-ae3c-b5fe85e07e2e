import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Typography,
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { ButtonLink } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import { deleteItem, getItems, updateStatus } from '../api';
import { PromotionConfig } from '../config';
import i18n from '../i18n';
import { ListPromotionRequest, Promotion } from '../type';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const PromotionList: React.FC = () => {
  const navigate = useNavigateTenant();
  const [loading, setLoading] = useState(false);
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [meta, setMeta] = useState({ next_cursor: '', has_more: false });
  const [filters, setFilters] = useState<ListPromotionRequest>({ limit: 20 });
  const [form] = useForm();

  // Use English translations
  const t = i18n.en;

  const loadPromotions = async (params: ListPromotionRequest = filters) => {
    setLoading(true);
    try {
      const response = await getItems(params);
      setPromotions(response.data || response.items || []);

      // Handle different response structures
      if (response.meta) {
        setMeta(response.meta);
      } else if (
        response.next_cursor !== undefined &&
        response.has_more !== undefined
      ) {
        // Direct properties on response
        setMeta({
          next_cursor: response.next_cursor,
          has_more: response.has_more,
        });
      } else {
        // Default to empty pagination state
        setMeta({ next_cursor: '', has_more: false });
      }
    } catch (error) {
      console.error('Error loading promotions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPromotions();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm(t.confirm_delete)) {
      try {
        await deleteItem(id);
        loadPromotions();
      } catch (error) {
        console.error('Error deleting promotion:', error);
      }
    }
  };

  const handleStatusChange = async (id: number, status: number) => {
    try {
      await updateStatus(id, status);
      loadPromotions();
    } catch (error) {
      console.error('Error updating promotion status:', error);
    }
  };

  const handleSearch = (values: any) => {
    const searchParams: ListPromotionRequest = {
      ...filters,
      search: values.search,
      status: values.status,
      promotion_type: values.promotion_type,
    };

    if (values.date_range && values.date_range.length === 2) {
      searchParams.start_date_from = values.date_range[0].format('YYYY-MM-DD');
      searchParams.end_date_to = values.date_range[1].format('YYYY-MM-DD');
    }

    setFilters(searchParams);
    loadPromotions(searchParams);
  };

  const handleLoadMore = () => {
    if (meta.has_more) {
      const newFilters = { ...filters, cursor: meta.next_cursor };
      setFilters(newFilters);
      loadPromotions(newFilters);
    }
  };

  const handlePromotionClick = (promotion: Promotion) => {
    navigate(`/promotion/${promotion.id}`);
  };

  const getStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return <Tag color="red">{t.status_inactive}</Tag>;
      case 1:
        return <Tag color="green">{t.status_active}</Tag>;
      case 2:
        return <Tag color="gray">{t.status_expired}</Tag>;
      case 3:
        return <Tag color="blue">{t.status_scheduled}</Tag>;
      default:
        return <Tag>Unknown</Tag>;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: t.name,
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Promotion) => (
        <ButtonLink to={`promotion/${record.id}`} type="link">
          {text}
        </ButtonLink>
      ),
    },
    {
      title: t.code,
      dataIndex: 'code',
      key: 'code',
      render: (text: string | null) => text || '-',
    },
    {
      title: t.promotion_type,
      dataIndex: 'promotion_type',
      key: 'promotion_type',
      render: (text: string) => text.charAt(0).toUpperCase() + text.slice(1),
    },
    {
      title: 'Discount',
      key: 'discount',
      render: (_, record: Promotion) => {
        if (record.discount_type === 'percentage') {
          return `${record.discount_value}%`;
        } else if (record.discount_type === 'fixed_amount') {
          return `$${record.discount_value.toFixed(2)}`;
        } else if (record.discount_type === 'free_shipping') {
          return 'Free Shipping';
        } else {
          return 'Buy X Get Y';
        }
      },
    },
    {
      title: 'Period',
      key: 'period',
      render: (_, record: Promotion) => (
        <>
          {dayjs(record.start_date).format('YYYY-MM-DD')} to{' '}
          {dayjs(record.end_date).format('YYYY-MM-DD')}
        </>
      ),
    },
    {
      title: t.status,
      dataIndex: 'status',
      key: 'status',
      render: (status: number) => getStatusTag(status),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: Promotion) => (
        <Space size="small">
          <ButtonLink to={`promotion/${record.id}`} type="link">
            {t.detail}
          </ButtonLink>
          <ButtonLink to={`promotion/${record.id}/edit`} type="link">
            {t.edit}
          </ButtonLink>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>
            {t.delete}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <Title level={2}>{t.title}</Title>
        </Col>
        <Col>
          <ButtonLink
            to="promotion/create"
            type="primary"
            icon={<PlusOutlined />}
          >
            {t.create}
          </ButtonLink>
        </Col>
      </Row>

      <Card style={{ marginBottom: 16 }}>
        <Form form={form} layout="vertical" onFinish={handleSearch}>
          <Row gutter={16}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="search" label={t.search}>
                <Input
                  placeholder="Search by name or code"
                  prefix={<SearchOutlined />}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="status" label={t.status}>
                <Select
                  allowClear
                  placeholder="Select status"
                  options={PromotionConfig.statusOptions}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="promotion_type" label={t.promotion_type}>
                <Select
                  allowClear
                  placeholder="Select type"
                  options={PromotionConfig.promotionTypeOptions}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="date_range" label={t.date_range}>
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col>
              <Button type="primary" htmlType="submit">
                {t.search}
              </Button>
              <Button
                style={{ marginLeft: 8 }}
                onClick={() => {
                  form.resetFields();
                  setFilters({ limit: 20 });
                  loadPromotions({ limit: 20 });
                }}
              >
                {t.reset}
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>

      <Table
        columns={columns}
        dataSource={promotions}
        rowKey="id"
        loading={loading}
        pagination={false}
      />

      {meta.has_more && (
        <Row justify="center" style={{ marginTop: 16 }}>
          <Button onClick={handleLoadMore}>{t.loadMore}</Button>
        </Row>
      )}
    </div>
  );
};

export default PromotionList;
