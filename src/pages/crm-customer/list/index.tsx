import {
  CheckCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  MoreOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Dropdown,
  Modal,
  Row,
  Table,
  Tag,
  Typography,
  message,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { deleteItem, getItems, updateStatus, verifyCustomer } from '../api';
import { PATH } from '../config';
import { useCustomerStore } from '../store';
import { Customer, CustomerStatus } from '../type';
import Search from './search';

const { confirm } = Modal;

const CustomerList: React.FC = () => {
  const { t } = useTranslation();
  const [data, setData] = useState<Customer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [params, setParams] = useState<any>({
    page: 1,
    limit: 10,
  });

  const {
    setCustomer,
    setIsEdit,
    setIsModalOpen,
    refreshList,
    setRefreshList,
  } = useCustomerStore();

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await getItems(params);
      setData(response.data);
      setPagination({
        current: params.page,
        pageSize: params.limit,
        total: response.meta.total,
      });
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [params, refreshList]);

  const handleSearch = (values: any) => {
    setParams({
      ...params,
      page: 1,
      ...values,
    });
  };

  const handleDelete = (id: number) => {
    confirm({
      title: t('customer.messages.confirm_delete'),
      onOk: async () => {
        try {
          await deleteItem(String(id));
          message.success(t('customer.messages.delete_success'));
          fetchData();
        } catch (error) {
          console.error('Error deleting customer:', error);
        }
      },
    });
  };

  const handleVerify = async (id: number) => {
    try {
      await verifyCustomer(String(id));
      message.success(t('customer.messages.verify_success'));
      fetchData();
    } catch (error) {
      console.error('Error verifying customer:', error);
    }
  };

  const handleChangeStatus = async (id: number, status: CustomerStatus) => {
    try {
      await updateStatus(String(id), status);
      message.success(t('customer.messages.status_update_success'));
      fetchData();
    } catch (error) {
      console.error('Error updating customer status:', error);
    }
  };

  const handleTableChange = (pagination: any) => {
    setParams({
      ...params,
      page: pagination.current,
      limit: pagination.pageSize,
    });
  };

  const handleAdd = () => {
    setCustomer(null);
    setIsEdit(false);
    setIsModalOpen(true);
  };

  const handleEdit = (record: Customer) => {
    setCustomer(record);
    setIsEdit(true);
    setIsModalOpen(true);
  };

  const columns: ColumnsType<Customer> = [
    {
      title: t('customer.fields.customer_id'),
      dataIndex: 'customer_id',
      key: 'customer_id',
      width: 80,
    },
    {
      title: t('customer.fields.full_name'),
      dataIndex: 'full_name',
      key: 'full_name',
      render: (text, record) => (
        <Link to={`${PATH}/${record.customer_id}`}>{text}</Link>
      ),
    },
    {
      title: t('customer.fields.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('customer.fields.phone'),
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: t('customer.fields.group_id'),
      dataIndex: 'group_name',
      key: 'group_name',
    },
    {
      title: t('customer.fields.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: CustomerStatus) => {
        let color = 'default';
        switch (status) {
          case 'active':
            color = 'green';
            break;
          case 'inactive':
            color = 'orange';
            break;
          case 'blocked':
            color = 'red';
            break;
          case 'pending':
            color = 'blue';
            break;
        }
        return <Tag color={color}>{t(`customer.status.${status}`)}</Tag>;
      },
    },
    {
      title: t('customer.fields.is_verified'),
      dataIndex: 'is_verified',
      key: 'is_verified',
      render: (verified: boolean) =>
        verified ? (
          <Tag color="green">
            <CheckCircleOutlined /> {t('common.yes')}
          </Tag>
        ) : (
          <Tag color="orange">{t('common.no')}</Tag>
        ),
    },
    {
      title: t('customer.fields.created_at'),
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => dayjs(date).format('DD/MM/YYYY HH:mm'),
    },
    {
      title: t('common.actions'),
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                label: (
                  <Link to={`${PATH}/${record.customer_id}`}>
                    <EyeOutlined /> {t('customer.actions.view_details')}
                  </Link>
                ),
              },
              {
                key: 'edit',
                label: (
                  <a onClick={() => handleEdit(record)}>
                    <EditOutlined /> {t('common.edit')}
                  </a>
                ),
              },
              {
                key: 'delete',
                danger: true,
                label: (
                  <a onClick={() => handleDelete(record.customer_id)}>
                    <DeleteOutlined /> {t('common.delete')}
                  </a>
                ),
              },
              {
                key: 'divider',
                type: 'divider',
              },
              !record.is_verified && {
                key: 'verify',
                label: (
                  <a onClick={() => handleVerify(record.customer_id)}>
                    <CheckCircleOutlined /> {t('customer.actions.verify')}
                  </a>
                ),
              },
              {
                key: 'statusActive',
                label: (
                  <a
                    onClick={() =>
                      handleChangeStatus(record.customer_id, 'active')
                    }
                  >
                    {t('customer.status.active')}
                  </a>
                ),
                disabled: record.status === 'active',
              },
              {
                key: 'statusInactive',
                label: (
                  <a
                    onClick={() =>
                      handleChangeStatus(record.customer_id, 'inactive')
                    }
                  >
                    {t('customer.status.inactive')}
                  </a>
                ),
                disabled: record.status === 'inactive',
              },
              {
                key: 'statusBlocked',
                label: (
                  <a
                    onClick={() =>
                      handleChangeStatus(record.customer_id, 'blocked')
                    }
                  >
                    {t('customer.status.blocked')}
                  </a>
                ),
                disabled: record.status === 'blocked',
              },
            ].filter(Boolean),
          }}
          trigger={['click']}
        >
          <Button icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  return (
    <>
      <Card>
        <Row justify="space-between" align="middle" gutter={[16, 16]}>
          <Col>
            <Typography.Title level={4}>{t('customer.title')}</Typography.Title>
          </Col>
          <Col>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              {t('customer.actions.create')}
            </Button>
          </Col>
        </Row>
        <Search onSearch={handleSearch} />
        <Table
          rowKey="customer_id"
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
        />
      </Card>
    </>
  );
};

export default CustomerList;
