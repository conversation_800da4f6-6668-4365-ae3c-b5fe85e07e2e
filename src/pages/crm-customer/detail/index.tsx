import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  message,
  Modal,
  Row,
  Skeleton,
  Space,
  Tabs,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

import { useNavigateTenant } from '../../../hooks';
import { deleteItem, getItem, updateStatus, verifyCustomer } from '../api';
import { PATH } from '../config';
import { useCustomerStore } from '../store';
import { Customer, CustomerStatus } from '../type';

const { confirm } = Modal;
const { TabPane } = Tabs;

const CustomerDetail: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigateTenant();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const {
    setCustomer: setStoreCustomer,
    setIsEdit,
    setIsModalOpen,
  } = useCustomerStore();

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!id) return;
      setLoading(true);
      try {
        const response = await getItem(id);
        if (response.success) {
          setCustomer(response.data);
        }
      } catch (error) {
        console.error('Error fetching customer:', error);
        message.error('Failed to load customer details');
      } finally {
        setLoading(false);
      }
    };
    fetchCustomer();
  }, [id]);

  const handleEdit = () => {
    if (customer) {
      setStoreCustomer(customer);
      setIsEdit(true);
      setIsModalOpen(true);
    }
  };

  const handleDelete = () => {
    if (!customer) return;

    confirm({
      title: t('customer.messages.confirm_delete'),
      onOk: async () => {
        try {
          await deleteItem(String(customer.customer_id));
          message.success(t('customer.messages.delete_success'));
          navigate(PATH);
        } catch (error) {
          console.error('Error deleting customer:', error);
        }
      },
    });
  };

  const handleVerify = async () => {
    if (!customer) return;

    try {
      await verifyCustomer(String(customer.customer_id));
      message.success(t('customer.messages.verify_success'));
      // Reload customer data to show updated verification status
      const response = await getItem(String(customer.customer_id));
      if (response.success) {
        setCustomer(response.data);
      }
    } catch (error) {
      console.error('Error verifying customer:', error);
    }
  };

  const handleChangeStatus = async (status: CustomerStatus) => {
    if (!customer) return;

    try {
      await updateStatus(String(customer.customer_id), status);
      message.success(t('customer.messages.status_update_success'));
      // Reload customer data to show updated status
      const response = await getItem(String(customer.customer_id));
      if (response.success) {
        setCustomer(response.data);
      }
    } catch (error) {
      console.error('Error updating customer status:', error);
    }
  };

  const getStatusColor = (status: CustomerStatus) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'orange';
      case 'blocked':
        return 'red';
      case 'pending':
        return 'blue';
      default:
        return 'default';
    }
  };

  return (
    <Card>
      {loading ? (
        <Skeleton active avatar paragraph={{ rows: 6 }} />
      ) : customer ? (
        <>
          <Row
            justify="space-between"
            align="middle"
            style={{ marginBottom: 16 }}
          >
            <Col>
              <Space>
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={() => navigate(PATH)}
                >
                  {t('common.back')}
                </Button>
                <Typography.Title level={4} style={{ margin: 0 }}>
                  {t('customer.title')} #{customer.customer_id}
                </Typography.Title>
              </Space>
            </Col>
            <Col>
              <Space>
                {!customer.is_verified && (
                  <Button
                    type="primary"
                    ghost
                    icon={<CheckCircleOutlined />}
                    onClick={handleVerify}
                  >
                    {t('customer.actions.verify')}
                  </Button>
                )}
                <Button icon={<EditOutlined />} onClick={handleEdit}>
                  {t('common.edit')}
                </Button>
                <Button danger icon={<DeleteOutlined />} onClick={handleDelete}>
                  {t('common.delete')}
                </Button>
              </Space>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} sm={24} md={8} lg={6}>
              <Card bordered={false}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    size={120}
                    src={customer.avatar_url}
                    icon={<UserOutlined />}
                    style={{ marginBottom: 16 }}
                  />
                  <Typography.Title level={4} style={{ margin: '8px 0' }}>
                    {customer.full_name}
                  </Typography.Title>
                  <Typography.Text type="secondary">
                    {customer.email}
                  </Typography.Text>
                  <div style={{ margin: '16px 0' }}>
                    <Tag color={getStatusColor(customer.status)}>
                      {t(`customer.status.${customer.status}`)}
                    </Tag>
                    {customer.is_verified && (
                      <Tag color="green">
                        <CheckCircleOutlined /> {t('common.verified')}
                      </Tag>
                    )}
                  </div>
                  <Divider />
                  <Typography.Title level={5}>
                    {t('customer.actions.change_status')}
                  </Typography.Title>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Button
                      type={
                        customer.status === 'active' ? 'primary' : 'default'
                      }
                      block
                      disabled={customer.status === 'active'}
                      onClick={() => handleChangeStatus('active')}
                    >
                      {t('customer.status.active')}
                    </Button>
                    <Button
                      type={
                        customer.status === 'inactive' ? 'primary' : 'default'
                      }
                      block
                      disabled={customer.status === 'inactive'}
                      onClick={() => handleChangeStatus('inactive')}
                    >
                      {t('customer.status.inactive')}
                    </Button>
                    <Button
                      type={
                        customer.status === 'blocked' ? 'primary' : 'default'
                      }
                      danger={customer.status !== 'blocked'}
                      block
                      disabled={customer.status === 'blocked'}
                      onClick={() => handleChangeStatus('blocked')}
                    >
                      {t('customer.status.blocked')}
                    </Button>
                    <Button
                      type={
                        customer.status === 'pending' ? 'primary' : 'default'
                      }
                      block
                      disabled={customer.status === 'pending'}
                      onClick={() => handleChangeStatus('pending')}
                    >
                      {t('customer.status.pending')}
                    </Button>
                  </Space>
                </div>
              </Card>
            </Col>

            <Col xs={24} sm={24} md={16} lg={18}>
              <Card bordered={false}>
                <Tabs defaultActiveKey="details">
                  <TabPane tab={t('common.details')} key="details">
                    <Descriptions
                      bordered
                      column={{ xs: 1, sm: 2 }}
                      layout="vertical"
                    >
                      <Descriptions.Item
                        label={t('customer.fields.customer_id')}
                      >
                        {customer.customer_id}
                      </Descriptions.Item>
                      <Descriptions.Item label={t('customer.fields.full_name')}>
                        {customer.full_name}
                      </Descriptions.Item>
                      <Descriptions.Item label={t('customer.fields.email')}>
                        {customer.email}
                      </Descriptions.Item>
                      <Descriptions.Item label={t('customer.fields.phone')}>
                        {customer.phone || '-'}
                      </Descriptions.Item>
                      <Descriptions.Item label={t('customer.fields.group_id')}>
                        {customer.group_name || '-'}
                      </Descriptions.Item>
                      <Descriptions.Item label={t('customer.fields.status')}>
                        <Tag color={getStatusColor(customer.status)}>
                          {t(`customer.status.${customer.status}`)}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item
                        label={t('customer.fields.is_verified')}
                      >
                        {customer.is_verified ? (
                          <Tag color="green">
                            <CheckCircleOutlined /> {t('common.yes')}
                          </Tag>
                        ) : (
                          <Tag color="orange">{t('common.no')}</Tag>
                        )}
                      </Descriptions.Item>
                      <Descriptions.Item
                        label={t('customer.fields.created_at')}
                      >
                        {customer.created_at
                          ? dayjs(customer.created_at).format(
                              'DD/MM/YYYY HH:mm',
                            )
                          : '-'}
                      </Descriptions.Item>
                      <Descriptions.Item
                        label={t('customer.fields.updated_at')}
                      >
                        {customer.updated_at
                          ? dayjs(customer.updated_at).format(
                              'DD/MM/YYYY HH:mm',
                            )
                          : '-'}
                      </Descriptions.Item>
                      <Descriptions.Item
                        label={t('customer.fields.last_login_at')}
                      >
                        {customer.last_login_at
                          ? dayjs(customer.last_login_at).format(
                              'DD/MM/YYYY HH:mm',
                            )
                          : '-'}
                      </Descriptions.Item>
                    </Descriptions>
                  </TabPane>
                  <TabPane tab={t('common.activities')} key="activities">
                    <div style={{ padding: '20px 0', textAlign: 'center' }}>
                      <Typography.Text type="secondary">
                        {t('common.no_data')}
                      </Typography.Text>
                    </div>
                  </TabPane>
                  <TabPane tab={t('common.orders')} key="orders">
                    <div style={{ padding: '20px 0', textAlign: 'center' }}>
                      <Typography.Text type="secondary">
                        {t('common.no_data')}
                      </Typography.Text>
                    </div>
                  </TabPane>
                </Tabs>
              </Card>
            </Col>
          </Row>
        </>
      ) : (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Typography.Text type="danger">
            {t('common.not_found', {
              resource: t('customer.title').toLowerCase(),
            })}
          </Typography.Text>
        </div>
      )}
    </Card>
  );
};

export default CustomerDetail;
