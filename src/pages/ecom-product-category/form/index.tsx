import React from 'react';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import IndexForm from './form';

const Detail: React.FC = (props) => {
  const { id } = useParams();
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();

  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    navigate(`/${MODULE}/`);
  };

  return (
    <div className="p-4 bg-white rounded-sm">
      <IndexForm onChange={handleChangeForm} id={id}></IndexForm>
    </div>
  );
};

export default Detail;
