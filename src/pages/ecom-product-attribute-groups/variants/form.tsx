import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Switch,
} from 'antd';
import _ from 'lodash';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  createVariant,
  getAttributeValuesByProductId,
  getVariant,
  updateVariant,
} from '../api';
import { MODULE } from '../config';
import { ProductAttributeValue, ProductVariant } from '../type';

const FormItem = Form.Item;

export const ProductVariantForm: React.FC = () => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();
  const { productId, variantId } = useParams<{
    productId: string;
    variantId: string;
  }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [isNew, setIsNew] = useState<boolean>(true);
  const [attributeValues, setAttributeValues] = useState<
    ProductAttributeValue[]
  >([]);

  const initForm = {
    product_id: productId ? parseInt(productId) : undefined,
    is_default: false,
    is_active: true,
    quantity: 0,
  };

  const fetchAttributeValues = useCallback(async () => {
    if (!productId) return;

    try {
      const response = await getAttributeValuesByProductId(productId);
      if (response.status.success) {
        setAttributeValues(response.data);
      }
    } catch (error) {
      logger('Error fetching attribute values', error);
    }
  }, [productId, logger]);

  const fetchVariant = useCallback(
    async (id: string) => {
      setLoading(true);
      try {
        const response = await getVariant(id);
        if (response.status.success) {
          form.setFieldsValue(response.data);
        } else {
          message.error(response.status.message);
        }
      } catch (error) {
        logger('Error fetching variant', error);
        message.error(t('submitError'));
      } finally {
        setLoading(false);
      }
    },
    [form, logger, t],
  );

  useEffect(() => {
    fetchAttributeValues();
  }, [productId, fetchAttributeValues]);

  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(initForm);

    if (variantId && variantId !== 'create') {
      setIsNew(false);
      fetchVariant(variantId);
    } else {
      setIsNew(true);
    }
  }, [variantId, productId, form, initForm, fetchVariant]);

  const onFinish = async (values: ProductVariant) => {
    setLoading(true);
    try {
      let response;
      if (isNew) {
        response = await createVariant(values);
        if (response.status.success) {
          message.success(t('variant.addSuccess'));
          navigate(`/${MODULE}/${productId}/variants`);
        }
      } else if (variantId) {
        response = await updateVariant(variantId, values);
        if (response.status.success) {
          message.success(t('variant.updateSuccess'));
          navigate(`/${MODULE}/${productId}/variants`);
        }
      }

      if (response && !response.status.success) {
        message.error(response.status.message);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('variant.submitError'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(`/${MODULE}/${productId}/variants`);
  };

  return (
    <div className="p-4">
      <Card
        title={
          <div className="flex items-center">
            <Button
              icon={<ArrowLeftOutlined />}
              type="text"
              onClick={handleCancel}
              style={{ marginRight: '10px' }}
            />
            <span>{isNew ? t('variant.add') : t('variant.edit')}</span>
          </div>
        }
        extra={
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => form.submit()}
            loading={loading}
          >
            {t('btnSave')}
          </Button>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={initForm}
        >
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('variant.sku')}
                name="sku"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem label={t('variant.name')} name="name">
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('variant.price')}
                name="price"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  prefix="$"
                />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('variant.quantity')}
                name="quantity"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <InputNumber style={{ width: '100%' }} min={0} precision={0} />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('variant.isDefault')}
                name="is_default"
                valuePropName="checked"
              >
                <Switch />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('variant.isActive')}
                name="is_active"
                valuePropName="checked"
              >
                <Switch />
              </FormItem>
            </Col>

            {attributeValues.length > 0 && (
              <Col xs={24}>
                <Card title={t('variant.attributes')} size="small">
                  <Row gutter={16}>
                    {attributeValues.map((attr) => (
                      <Col xs={24} lg={12} key={attr.value_id}>
                        <FormItem
                          label={attr.attribute_id.toString()}
                          name={[
                            'attribute_values',
                            attr.attribute_id.toString(),
                          ]}
                        >
                          <Input disabled value={attr.value} />
                        </FormItem>
                      </Col>
                    ))}
                  </Row>
                </Card>
              </Col>
            )}
          </Row>
        </Form>
      </Card>
    </div>
  );
};
