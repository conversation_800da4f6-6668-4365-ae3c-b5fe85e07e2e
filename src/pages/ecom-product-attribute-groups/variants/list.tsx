import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table, Tag } from 'antd';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { deleteVariant, getVariantsByProductId } from '../api';
import '../assets/styles.scss';
import { MODULE } from '../config';
import { ProductVariant } from '../type';

export const ProductVariantList: React.FC = () => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { productId } = useParams<{ productId: string }>();

  const [loading, setLoading] = useState<boolean>(false);
  const [variants, setVariants] = useState<ProductVariant[]>([]);

  const fetchVariants = useCallback(async () => {
    if (!productId) return;

    setLoading(true);
    try {
      const response = await getVariantsByProductId(productId);
      if (response.status.success) {
        setVariants(response.data);
      } else {
        message.error(response.status.message);
      }
    } catch (error) {
      logger('Error fetching variants', error);
      message.error(t('submitError'));
    } finally {
      setLoading(false);
    }
  }, [productId, t, logger]);

  useEffectOnce(() => {
    fetchVariants();
  });

  const handleDelete = async (id: string) => {
    try {
      const res = await deleteVariant(id);
      if (res.status.success) {
        message.success(t('variant.deleteSuccess'));
        fetchVariants();
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      logger('Error deleting variant', error);
      message.error(t('submitError'));
    }
  };

  const handleActions = (action: string, record?: ProductVariant) => {
    if (action === 'edit' && record) {
      navigate(`/${MODULE}/${productId}/variants/${record.variant_id}`);
    } else if (action === 'add') {
      navigate(`/${MODULE}/${productId}/variants/create`);
    } else if (action === 'back') {
      navigate(`/${MODULE}`);
    }
  };

  const columns = [
    {
      title: t('variant.sku'),
      dataIndex: 'sku',
      key: 'sku',
    },
    {
      title: t('variant.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('variant.price'),
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `$${price.toFixed(2)}`,
    },
    {
      title: t('variant.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: t('variant.isDefault'),
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault: boolean) =>
        isDefault ? <Tag color="green">{t('statusActive')}</Tag> : null,
    },
    {
      title: t('variant.isActive'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) =>
        isActive ? (
          <Tag color="green">{t('statusActive')}</Tag>
        ) : (
          <Tag color="red">{t('statusInactive')}</Tag>
        ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: ProductVariant) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.variant_id.toString())}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">
          {t('variant.title')}
        </div>
        <div className="flex gap-4">
          <Button onClick={() => handleActions('back')}>
            {t('btnCancel')}
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add')}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="variant_id"
          loading={loading}
          columns={columns}
          dataSource={variants}
        />
      </Space>
    </div>
  );
};
