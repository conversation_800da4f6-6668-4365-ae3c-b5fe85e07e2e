import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table } from 'antd';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { deleteAttributeValue, getAttributeValuesByProductId } from '../api';
import '../assets/styles.scss';
import { MODULE } from '../config';
import { ProductAttributeValue } from '../type';

export const ProductAttributeValueList: React.FC = () => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { productId } = useParams<{ productId: string }>();

  const [loading, setLoading] = useState<boolean>(false);
  const [attributeValues, setAttributeValues] = useState<
    ProductAttributeValue[]
  >([]);

  const fetchAttributeValues = useCallback(async () => {
    if (!productId) return;

    setLoading(true);
    try {
      const response = await getAttributeValuesByProductId(productId);
      if (response.status.success) {
        setAttributeValues(response.data);
      } else {
        message.error(response.status.message);
      }
    } catch (error) {
      logger('Error fetching attribute values', error);
      message.error(t('submitError'));
    } finally {
      setLoading(false);
    }
  }, [productId, t]);

  useEffectOnce(() => {
    fetchAttributeValues();
  });

  const handleDelete = async (id: string) => {
    try {
      const res = await deleteAttributeValue(id);
      if (res.status.success) {
        message.success(t('attribute.deleteSuccess'));
        fetchAttributeValues();
      } else {
        message.error(res.status.message);
      }
    } catch (error) {
      logger('Error deleting attribute value', error);
      message.error(t('submitError'));
    }
  };

  const handleActions = (action: string, record?: ProductAttributeValue) => {
    if (action === 'edit' && record) {
      navigate(`/${MODULE}/${productId}/attributes/${record.value_id}`);
    } else if (action === 'add') {
      navigate(`/${MODULE}/${productId}/attributes/create`);
    } else if (action === 'back') {
      navigate(`/${MODULE}`);
    }
  };

  const columns = [
    {
      title: t('attribute.name'),
      dataIndex: 'attribute_id',
      key: 'attribute_id',
    },
    {
      title: t('attribute.value'),
      dataIndex: 'value',
      key: 'value',
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: ProductAttributeValue) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.value_id.toString())}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">
          {t('attribute.title')}
        </div>
        <div className="flex gap-4">
          <Button onClick={() => handleActions('back')}>
            {t('btnCancel')}
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add')}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="value_id"
          loading={loading}
          columns={columns}
          dataSource={attributeValues}
        />
      </Space>
    </div>
  );
};
