import { Col, Form, Input, InputNumber, message, Row, Switch } from 'antd';
import _ from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import { ProductAttributeGroup } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const AttributeGroupForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<ProductAttributeGroup>();
    const [formValues, setFormValues] = useState<ProductAttributeGroup>();

    const initForm = {
      name: '',
      code: '',
      description: '',
      position: 0,
      is_active: true,
    };

    const getItemData = async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    };

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
        form.setFieldsValue(initForm);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id]);

    const onFinish = async (values: ProductAttributeGroup) => {
      try {
        let res;
        if (isNew) {
          res = await createItem(values);
          if (res.status.success) {
            message.success(t('form.addSuccess'));
          }
        } else {
          res = await updateItem(id!, values);
          if (res.status.success) {
            message.success(t('form.updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('form.submitError'),
        );
      }
    };

    const handleValuesChange = (changedValues: any, allValues: any) => {
      logger(changedValues);
      logger(allValues);
      setFormValues(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.name')}
                name="name"
                rules={[{ required: true, message: t('form.pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.code')}
                name="code"
                rules={[{ required: true, message: t('form.pleaseEnterData') }]}
                tooltip={t('form.codeTooltip')}
              >
                <Input />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem label={t('form.position')} name="position">
                <InputNumber style={{ width: '100%' }} min={0} />
              </FormItem>
            </Col>

            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.isActive')}
                name="is_active"
                valuePropName="checked"
              >
                <Switch />
              </FormItem>
            </Col>

            <Col xs={24}>
              <FormItem label={t('form.description')} name="description">
                <Input.TextArea rows={4} />
              </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
    );
  },
);
AttributeGroupForm.displayName = 'AttributeGroupForm';

export { AttributeGroupForm };
