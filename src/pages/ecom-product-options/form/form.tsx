import { Col, Form, Input, message, Row, Select } from 'antd';
import _ from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import { DisplayType, EcomProductOption } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const EcomProductOptionForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<EcomProductOption>();
    const [formValues, setFormValues] = useState<EcomProductOption>();

    const initForm = {};

    const getItemData = async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    };

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id]);

    const onFinish = async (values: EcomProductOption) => {
      try {
        let res;
        if (isNew) {
          res = await createItem(values);
          if (res.status.success) {
            message.success(t('form.addSuccess'));
          }
        } else {
          res = await updateItem(id!, values);
          if (res.status.success) {
            message.success(t('form.updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('form.submitError'),
        );
      }
    };

    const handleValuesChange = (newValue: any, allValues: any) => {
      logger(newValue);
      logger(allValues);
      setFormValues(allValues);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    return (
      <Form
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.name')}
                name="name"
                rules={[{ required: true, message: t('form.pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('form.display_type')}
                name="display_type"
                rules={[{ required: true, message: t('form.pleaseEnterData') }]}
              >
                <Select
                  options={Object.values(DisplayType).map((type) => ({
                    label: t(`form.display_type.${type}`),
                    value: type,
                  }))}
                  placeholder={t('form.display_type')}
                />
              </FormItem>
            </Col>
          </Row>
        </div>
      </Form>
    );
  },
);
EcomProductOptionForm.displayName = 'EcomProductOptionForm';

export { EcomProductOptionForm };
