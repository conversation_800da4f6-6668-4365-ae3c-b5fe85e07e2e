import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  cleanParams,
} from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { ProductModal } from '../form/modal';
import useProductStore from '../store';
import { Product } from '../type';
import { ProductSearch } from './search';

function ProductList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useProductStore();

  const [pagination, setPagination] = useState<any>({
    page: 1,
    page_size: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<Product[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...pagination,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.pagination.total);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname, pagination],
  );

  useEffect(() => {
    fetchData();
  }, [fetchData, pagination]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.product_id.toString());
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.product_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handlePagination = (page: number, pageSize: number) => {
    setPagination({ page, page_size: pageSize });
  };

  const columns = [
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('slug'),
      dataIndex: 'slug',
      key: 'slug',
    },
    {
      title: t('basePrice'),
      dataIndex: 'base_price',
      key: 'base_price',
      render: (price: number) => (price ? `$${price.toFixed(2)}` : '-'),
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <span>{t(`status.${status}`)}</span>,
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.product_id.toString())}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <ProductSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></ProductSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="product_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={{
            current: pagination.page,
            pageSize: pagination.page_size,
            total: total,
            onChange: handlePagination,
          }}
        ></Table>
        {MODULE_POPUP && (
          <ProductModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ProductModal>
        )}
      </Space>
    </div>
  );
}
export { ProductList };
