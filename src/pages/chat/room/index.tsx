import {
  FileOutlined,
  PaperClipOutlined,
  SendOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Input,
  List,
  Space,
  Typography,
  Upload,
  message,
} from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { sendMessage, getMessages } from '../api';
import { useChatStore } from '../store';
import { MESSAGE_TYPE, Message, SendMessagePayload } from '../type';
import { CHAT_CONFIG } from '../config';

const { TextArea } = Input;
const { Text } = Typography;

interface ChatRoomProps {
  chatId: number;
}

export default function ChatRoom({ chatId }: ChatRoomProps) {
  const { t } = useTranslation();
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    currentChat,
    onlineUsers,
    typingUsers,
    setMessages,
    addMessage,
    setCurrentChat,
  } = useChatStore();

  // Load messages when chat changes
  useEffect(() => {
    if (chatId) {
      loadMessages();
    }
  }, [chatId]);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load messages
  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await getMessages({
        chat_id: chatId,
        page: 1,
        limit: 50,
      });

      if (response.status.success && response.data) {
        setMessages(chatId, response.data || []);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      message.error('Không thể tải tin nhắn');
    } finally {
      setLoading(false);
    }
  };

  // Scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle send message
  const handleSendMessage = async () => {
    if (!messageText.trim() || !chatId) return;

    if (messageText.length > CHAT_CONFIG.MAX_MESSAGE_LENGTH) {
      message.error(
        `Tin nhắn không được vượt quá ${CHAT_CONFIG.MAX_MESSAGE_LENGTH} ký tự`,
      );
      return;
    }

    try {
      setSending(true);

      const payload: SendMessagePayload = {
        chat_id: chatId,
        content: messageText.trim(),
        message_type: MESSAGE_TYPE.TEXT,
      };

      const response = await sendMessage(payload);

      if (response.status.success && response.data) {
        addMessage(chatId, response.data);
        setMessageText('');
      } else {
        message.error(response.status.message || 'Không thể gửi tin nhắn');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      message.error('Lỗi khi gửi tin nhắn');
    } finally {
      setSending(false);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle file upload
  const handleFileUpload = async (file: File) => {
    if (file.size > CHAT_CONFIG.MAX_FILE_SIZE) {
      message.error(
        `File không được vượt quá ${CHAT_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB`,
      );
      return false;
    }

    if (!CHAT_CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
      message.error('Loại file không được hỗ trợ');
      return false;
    }

    try {
      setSending(true);

      // In real app, you would upload file to server first
      // For now, we'll just create a message with file info
      const payload: SendMessagePayload = {
        chat_id: chatId,
        content: file.name,
        message_type: MESSAGE_TYPE.FILE,
        file_url: URL.createObjectURL(file), // Mock file URL
        file_name: file.name,
        file_size: file.size,
      };

      const response = await sendMessage(payload);

      if (response.status.success && response.data) {
        addMessage(chatId, response.data);
        message.success('Gửi file thành công');
      } else {
        message.error('Không thể gửi file');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      message.error('Lỗi khi gửi file');
    } finally {
      setSending(false);
    }

    return false; // Prevent default upload
  };

  // Format message time
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else {
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    }
  };

  // Render message content based on type
  const renderMessageContent = (message: Message) => {
    switch (message.message_type) {
      case MESSAGE_TYPE.TEXT:
        return <Text>{message.content}</Text>;

      case MESSAGE_TYPE.IMAGE:
        return (
          <div>
            <img
              src={message.file_url}
              alt={message.file_name}
              style={{ maxWidth: '200px', maxHeight: '200px' }}
            />
            {message.content && <Text>{message.content}</Text>}
          </div>
        );

      case MESSAGE_TYPE.FILE:
        return (
          <div className="flex items-center space-x-2">
            <FileOutlined />
            <div>
              <Text strong>{message.file_name}</Text>
              <br />
              <Text type="secondary">
                {message.file_size
                  ? `${(message.file_size / 1024).toFixed(1)} KB`
                  : ''}
              </Text>
            </div>
          </div>
        );

      default:
        return <Text>{message.content}</Text>;
    }
  };

  // Check if user is online
  const isUserOnline = (userId: number) => {
    return onlineUsers.some((user) => user.id === userId);
  };

  // Check if user is typing
  const isUserTyping = (userId: number) => {
    return (typingUsers[chatId] || []).includes(userId);
  };

  if (!currentChat) {
    return (
      <div className="flex items-center justify-center h-full">
        <Text type="secondary">Chọn một cuộc trò chuyện để bắt đầu</Text>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <Card className="border-b rounded-none">
        <div className="flex items-center space-x-3">
          <Avatar icon={<UserOutlined />} size="large" />
          <div>
            <Text strong className="text-lg">
              {currentChat.name || 'Cuộc trò chuyện'}
            </Text>
            <br />
            <Text type="secondary" className="text-sm">
              {currentChat.is_group
                ? 'Nhóm chat'
                : isUserOnline(currentChat.id)
                  ? 'Đang hoạt động'
                  : 'Không hoạt động'}
            </Text>
          </div>
        </div>
      </Card>

      {/* Messages List */}
      <div className="flex-1 overflow-y-auto p-4">
        <List
          loading={loading}
          dataSource={messages[chatId] || []}
          renderItem={(message) => (
            <List.Item className="border-none px-0">
              <div className="w-full">
                <div className="flex items-start space-x-2">
                  <Avatar
                    src={message.sender?.avatar_url}
                    icon={<UserOutlined />}
                    size="small"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <Text strong className="text-sm">
                        {message.sender?.first_name} {message.sender?.last_name}
                      </Text>
                      <Text type="secondary" className="text-xs">
                        {formatMessageTime(message.created_at)}
                      </Text>
                    </div>
                    <div className="bg-gray-100 rounded-lg p-2 max-w-md">
                      {renderMessageContent(message)}
                    </div>
                  </div>
                </div>
              </div>
            </List.Item>
          )}
        />
        <div ref={messagesEndRef} />
      </div>

      {/* Typing Indicator */}
      {(typingUsers[chatId] || []).length > 0 && (
        <div className="px-4 py-2">
          <Text type="secondary" className="text-sm">
            {(typingUsers[chatId] || []).length === 1
              ? 'Đang gõ...'
              : `${(typingUsers[chatId] || []).length} người đang gõ...`}
          </Text>
        </div>
      )}

      {/* Message Input */}
      <Card className="border-t rounded-none">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <TextArea
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Nhập tin nhắn..."
              autoSize={{ minRows: 1, maxRows: 4 }}
              maxLength={CHAT_CONFIG.MAX_MESSAGE_LENGTH}
            />
          </div>
          <Space>
            <Upload
              beforeUpload={handleFileUpload}
              showUploadList={false}
              accept={CHAT_CONFIG.ALLOWED_FILE_TYPES.join(',')}
            >
              <Button icon={<PaperClipOutlined />} />
            </Upload>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              loading={sending || undefined}
              disabled={!messageText.trim()}
            >
              Gửi
            </Button>
          </Space>
        </div>
        <div className="mt-2 text-right">
          <Text type="secondary" className="text-xs">
            {messageText.length}/{CHAT_CONFIG.MAX_MESSAGE_LENGTH}
          </Text>
        </div>
      </Card>
    </div>
  );
}
