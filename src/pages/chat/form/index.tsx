import { PlusOutlined, UserOutlined } from '@ant-design/icons';
import {
  Avatar,
  Button,
  Form,
  Input,
  Modal,
  Select,
  Switch,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { createChat } from '../api';
import { CreateChatPayload, User } from '../type';

interface ChatFormProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

interface UserOption {
  label: string;
  value: number;
  user: User;
}

export default function ChatForm({ open, onCancel, onSuccess }: ChatFormProps) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isGroup, setIsGroup] = useState(false);
  const [users, setUsers] = useState<UserOption[]>([]);
  const [searchingUsers, setSearchingUsers] = useState(false);

  // Mock users data - in real app, this would come from an API
  const mockUsers: User[] = [
    {
      id: 1,
      username: 'john_doe',
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: 'Doe',
      avatar_url: '',
    },
    {
      id: 2,
      username: 'jane_smith',
      email: '<EMAIL>',
      first_name: 'Jane',
      last_name: 'Smith',
      avatar_url: '',
    },
    {
      id: 3,
      username: 'bob_wilson',
      email: '<EMAIL>',
      first_name: 'Bob',
      last_name: 'Wilson',
      avatar_url: '',
    },
  ];

  // Load users
  useEffect(() => {
    if (open) {
      const userOptions: UserOption[] = mockUsers.map((user) => ({
        label: `${user.first_name} ${user.last_name} (${user.email})`,
        value: user.id,
        user,
      }));
      setUsers(userOptions);
    }
  }, [open]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);

      const payload: CreateChatPayload = {
        is_group: isGroup,
        participant_ids: values.participant_ids || [],
        name: isGroup ? values.name : undefined,
      };

      const response = await createChat(payload);

      if (response.status.success) {
        message.success('Tạo cuộc trò chuyện thành công');
        form.resetFields();
        setIsGroup(false);
        onSuccess();
      } else {
        message.error(
          response.status.message || 'Không thể tạo cuộc trò chuyện',
        );
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      message.error('Lỗi khi tạo cuộc trò chuyện');
    } finally {
      setLoading(false);
    }
  };

  // Handle modal close
  const handleCancel = () => {
    form.resetFields();
    setIsGroup(false);
    onCancel();
  };

  // Custom option render for user select
  const renderUserOption = (option: UserOption) => (
    <div className="flex items-center space-x-2">
      <Avatar
        size="small"
        src={option.user.avatar_url}
        icon={<UserOutlined />}
      />
      <span>{option.label}</span>
    </div>
  );

  return (
    <Modal
      title={t('chat.new_chat')}
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          is_group: false,
          participant_ids: [],
        }}
      >
        {/* Group Chat Toggle */}
        <Form.Item label="Loại cuộc trò chuyện">
          <Switch
            checked={isGroup}
            onChange={setIsGroup}
            checkedChildren="Nhóm"
            unCheckedChildren="Cá nhân"
          />
        </Form.Item>

        {/* Group Name (only for group chats) */}
        {isGroup && (
          <Form.Item
            name="name"
            label={t('chat.group_name')}
            rules={[
              {
                required: true,
                message: 'Vui lòng nhập tên nhóm',
              },
            ]}
          >
            <Input placeholder="Nhập tên nhóm" />
          </Form.Item>
        )}

        {/* Participants */}
        <Form.Item
          name="participant_ids"
          label={t('chat.add_participants')}
          rules={[
            {
              required: true,
              message: 'Vui lòng chọn ít nhất một người tham gia',
            },
            {
              validator: (_, value) => {
                if (!isGroup && value && value.length > 1) {
                  return Promise.reject(
                    new Error('Cuộc trò chuyện cá nhân chỉ có thể có 1 người'),
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Select
            mode={isGroup ? 'multiple' : undefined}
            placeholder="Chọn người tham gia"
            showSearch
            loading={searchingUsers}
            notFoundContent="Không tìm thấy người dùng"
          >
            {users.map((user) => (
              <Select.Option key={user.value} value={user.value}>
                <div className="flex items-center space-x-2">
                  <Avatar
                    size="small"
                    src={user.user.avatar_url}
                    icon={<UserOutlined />}
                  />
                  <span>{user.label}</span>
                </div>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Submit Button */}
        <Form.Item className="mb-0">
          <div className="flex justify-end space-x-2">
            <Button onClick={handleCancel}>Hủy</Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading || undefined}
              icon={<PlusOutlined />}
            >
              Tạo cuộc trò chuyện
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
}
