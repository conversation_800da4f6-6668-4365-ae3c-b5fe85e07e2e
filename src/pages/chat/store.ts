import { create } from 'zustand';
import { Chat, Message, User } from './type';

interface ChatStore {
  // Current chat state
  currentChat: Chat | null;
  setCurrentChat: (chat: Chat | null) => void;

  // Chat list
  chats: Chat[];
  setChats: (chats: Chat[]) => void;
  addChat: (chat: Chat) => void;
  updateChat: (chatId: number, updates: Partial<Chat>) => void;
  removeChat: (chatId: number) => void;

  // Messages
  messages: Record<number, Message[]>; // chatId -> messages
  setMessages: (chatId: number, messages: Message[]) => void;
  addMessage: (chatId: number, message: Message) => void;
  updateMessage: (
    chatId: number,
    messageId: number,
    updates: Partial<Message>,
  ) => void;
  removeMessage: (chatId: number, messageId: number) => void;

  // Online users
  onlineUsers: User[];
  setOnlineUsers: (users: User[]) => void;
  updateUserOnlineStatus: (userId: number, isOnline: boolean) => void;

  // Typing indicators
  typingUsers: Record<number, number[]>; // chatId -> userIds
  setTypingUsers: (chatId: number, userIds: number[]) => void;
  addTypingUser: (chatId: number, userId: number) => void;
  removeTypingUser: (chatId: number, userId: number) => void;

  // UI state
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;

  selectedMessages: number[];
  setSelectedMessages: (messageIds: number[]) => void;
  toggleMessageSelection: (messageId: number) => void;
  clearMessageSelection: () => void;

  // Search
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  searchResults: Message[];
  setSearchResults: (results: Message[]) => void;
}

export const useChatStore = create<ChatStore>((set, get) => ({
  // Current chat state
  currentChat: null,
  setCurrentChat: (chat) => set({ currentChat: chat }),

  // Chat list
  chats: [],
  setChats: (chats) => set({ chats }),
  addChat: (chat) => set((state) => ({ chats: [chat, ...state.chats] })),
  updateChat: (chatId, updates) =>
    set((state) => ({
      chats: state.chats.map((chat) =>
        chat.id === chatId ? { ...chat, ...updates } : chat,
      ),
      currentChat:
        state.currentChat?.id === chatId
          ? { ...state.currentChat, ...updates }
          : state.currentChat,
    })),
  removeChat: (chatId) =>
    set((state) => ({
      chats: state.chats.filter((chat) => chat.id !== chatId),
      currentChat: state.currentChat?.id === chatId ? null : state.currentChat,
    })),

  // Messages
  messages: {},
  setMessages: (chatId, messages) =>
    set((state) => ({
      messages: { ...state.messages, [chatId]: messages },
    })),
  addMessage: (chatId, message) =>
    set((state) => ({
      messages: {
        ...state.messages,
        [chatId]: [...(state.messages[chatId] || []), message],
      },
    })),
  updateMessage: (chatId, messageId, updates) =>
    set((state) => ({
      messages: {
        ...state.messages,
        [chatId]: (state.messages[chatId] || []).map((msg) =>
          msg.id === messageId ? { ...msg, ...updates } : msg,
        ),
      },
    })),
  removeMessage: (chatId, messageId) =>
    set((state) => ({
      messages: {
        ...state.messages,
        [chatId]: (state.messages[chatId] || []).filter(
          (msg) => msg.id !== messageId,
        ),
      },
    })),

  // Online users
  onlineUsers: [],
  setOnlineUsers: (users) => set({ onlineUsers: users }),
  updateUserOnlineStatus: (userId, isOnline) =>
    set((state) => ({
      onlineUsers: isOnline
        ? state.onlineUsers.some((user) => user.id === userId)
          ? state.onlineUsers.map((user) =>
              user.id === userId ? { ...user, is_online: true } : user,
            )
          : [...state.onlineUsers, { id: userId, is_online: true } as User]
        : state.onlineUsers.filter((user) => user.id !== userId),
    })),

  // Typing indicators
  typingUsers: {},
  setTypingUsers: (chatId, userIds) =>
    set((state) => ({
      typingUsers: { ...state.typingUsers, [chatId]: userIds },
    })),
  addTypingUser: (chatId, userId) =>
    set((state) => ({
      typingUsers: {
        ...state.typingUsers,
        [chatId]: [...(state.typingUsers[chatId] || []), userId],
      },
    })),
  removeTypingUser: (chatId, userId) =>
    set((state) => ({
      typingUsers: {
        ...state.typingUsers,
        [chatId]: (state.typingUsers[chatId] || []).filter(
          (id) => id !== userId,
        ),
      },
    })),

  // UI state
  isLoading: false,
  setIsLoading: (loading) => set({ isLoading: loading }),

  selectedMessages: [],
  setSelectedMessages: (messageIds) => set({ selectedMessages: messageIds }),
  toggleMessageSelection: (messageId) =>
    set((state) => ({
      selectedMessages: state.selectedMessages.includes(messageId)
        ? state.selectedMessages.filter((id) => id !== messageId)
        : [...state.selectedMessages, messageId],
    })),
  clearMessageSelection: () => set({ selectedMessages: [] }),

  // Search
  searchQuery: '',
  setSearchQuery: (query) => set({ searchQuery: query }),
  searchResults: [],
  setSearchResults: (results) => set({ searchResults: results }),
}));
