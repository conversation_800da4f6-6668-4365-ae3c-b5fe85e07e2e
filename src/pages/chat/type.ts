export enum MESSAGE_TYPE {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  SYSTEM = 'system',
}

export enum CHAT_STATUS {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  is_online?: boolean;
  last_seen?: string;
}

export interface Message {
  id: number;
  chat_id: number;
  sender_id: number;
  message_type: MESSAGE_TYPE;
  content: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  sender?: User;
}

export interface Chat {
  id: number;
  name?: string;
  is_group: boolean;
  status: CHAT_STATUS;
  created_by: number;
  created_at: string;
  updated_at: string;
  last_message?: Message;
  unread_count?: number;
  participants?: User[];
  created_by_user?: User;
}

export interface ChatParticipant {
  id: number;
  chat_id: number;
  user_id: number;
  role: 'admin' | 'member';
  joined_at: string;
  user?: User;
}

export interface CreateChatPayload {
  name?: string;
  is_group: boolean;
  participant_ids: number[];
}

export interface SendMessagePayload {
  chat_id: number;
  message_type: MESSAGE_TYPE;
  content: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
}

export interface ChatListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: CHAT_STATUS;
}

export interface MessageListParams {
  chat_id: number;
  page?: number;
  limit?: number;
  before_message_id?: number;
}
