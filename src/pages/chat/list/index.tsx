import {
  MessageOutlined,
  PlusOutlined,
  SearchOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Badge,
  Button,
  Empty,
  Input,
  List,
  Space,
  Typography,
  message,
} from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import { getChats } from '../api';
import { useChatStore } from '../store';
import { Chat, CHAT_STATUS } from '../type';
import ChatForm from '../form';

const { Text, Title } = Typography;
const { Search } = Input;

export default function ChatList() {
  const { t } = useTranslation();
  const navigate = useNavigateTenant();
  const {
    chats,
    setChats,
    currentChat,
    setCurrentChat,
    isLoading,
    setIsLoading,
    searchQuery,
    setSearchQuery,
  } = useChatStore();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filteredChats, setFilteredChats] = useState<Chat[]>([]);

  // Load chats
  const loadChats = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await getChats({
        status: CHAT_STATUS.ACTIVE,
        limit: 100,
      });

      if (response.status.success) {
        setChats(response.data);
      } else {
        message.error(
          response.status.message || 'Không thể tải danh sách chat',
        );
      }
    } catch (error) {
      console.error('Error loading chats:', error);
      message.error('Lỗi khi tải danh sách chat');
    } finally {
      setIsLoading(false);
    }
  }, [setChats, setIsLoading]);

  // Filter chats based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredChats(chats);
    } else {
      const filtered = chats.filter(
        (chat) =>
          chat.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          chat.participants?.some((participant) =>
            `${participant.first_name} ${participant.last_name}`
              .toLowerCase()
              .includes(searchQuery.toLowerCase()),
          ),
      );
      setFilteredChats(filtered);
    }
  }, [chats, searchQuery]);

  // Load chats on mount
  useEffect(() => {
    loadChats();
  }, [loadChats]);

  // Handle chat selection
  const handleChatSelect = (chat: Chat) => {
    setCurrentChat(chat);
    navigate(`/chat/${chat.id}`);
  };

  // Get chat display name
  const getChatDisplayName = (chat: Chat) => {
    if (chat.name) {
      return chat.name;
    }
    if (chat.participants && chat.participants.length > 0) {
      return chat.participants
        .map((p) => `${p.first_name || ''} ${p.last_name || ''}`.trim())
        .join(', ');
    }
    return 'Cuộc trò chuyện';
  };

  // Get chat avatar
  const getChatAvatar = (chat: Chat) => {
    if (chat.is_group) {
      return <TeamOutlined />;
    }
    if (chat.participants && chat.participants.length > 0) {
      const participant = chat.participants[0];
      return participant.avatar_url ? (
        <Avatar src={participant.avatar_url} />
      ) : (
        <Avatar icon={<UserOutlined />} />
      );
    }
    return <Avatar icon={<MessageOutlined />} />;
  };

  // Format last message time
  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return date.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else {
      return date.toLocaleDateString('vi-VN');
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <Title level={4} className="!mb-0">
            {t('chat.title')}
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowCreateForm(true)}
          >
            {t('chat.new_chat')}
          </Button>
        </div>

        {/* Search */}
        <Search
          placeholder={t('chat.search_placeholder')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          prefix={<SearchOutlined />}
          allowClear
        />
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-auto">
        {filteredChats.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              searchQuery
                ? 'Không tìm thấy cuộc trò chuyện'
                : t('chat.no_chats')
            }
          />
        ) : (
          <List
            loading={isLoading}
            dataSource={filteredChats}
            renderItem={(chat) => (
              <List.Item
                className={`cursor-pointer hover:bg-gray-50 px-4 py-3 ${
                  currentChat?.id === chat.id
                    ? 'bg-blue-50 border-r-2 border-blue-500'
                    : ''
                }`}
                onClick={() => handleChatSelect(chat)}
              >
                <List.Item.Meta
                  avatar={
                    <Badge
                      count={chat.unread_count || 0}
                      size="small"
                      offset={[-5, 5]}
                    >
                      {getChatAvatar(chat)}
                    </Badge>
                  }
                  title={
                    <div className="flex items-center justify-between">
                      <Text strong className="truncate flex-1">
                        {getChatDisplayName(chat)}
                      </Text>
                      {chat.last_message && (
                        <Text type="secondary" className="text-xs ml-2">
                          {formatMessageTime(chat.last_message.created_at)}
                        </Text>
                      )}
                    </div>
                  }
                  description={
                    chat.last_message ? (
                      <Text type="secondary" className="truncate block">
                        {chat.last_message.message_type === 'text'
                          ? chat.last_message.content
                          : `[${chat.last_message.message_type}]`}
                      </Text>
                    ) : (
                      <Text type="secondary" italic>
                        {t('chat.no_messages')}
                      </Text>
                    )
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>

      {/* Create Chat Modal */}
      <ChatForm
        open={showCreateForm}
        onCancel={() => setShowCreateForm(false)}
        onSuccess={() => {
          setShowCreateForm(false);
          loadChats();
        }}
      />
    </div>
  );
}
