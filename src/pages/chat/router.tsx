import React from 'react';
import { Route, Routes, useParams } from 'react-router-dom';
import ChatList from './list';
import ChatRoom from './room';
import ChatForm from './form';
import ChatLayout from './layout';

// Wrapper component for ChatRoom to get params
const ChatRoomWrapper: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const chatId = id ? parseInt(id, 10) : 0;
  return <ChatRoom chatId={chatId} />;
};

// Wrapper component for ChatForm
const ChatFormWrapper: React.FC = () => {
  return (
    <ChatForm
      open={true}
      onCancel={() => window.history.back()}
      onSuccess={() => window.history.back()}
    />
  );
};

const ChatRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<ChatLayout />}>
        <Route index element={<ChatList />} />
        <Route path="room/:id" element={<ChatRoomWrapper />} />
        <Route path="new" element={<ChatFormWrapper />} />
      </Route>
    </Routes>
  );
};

export default ChatRouter;
