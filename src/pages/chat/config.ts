import { MESSAGE_TYPE, CHAT_STATUS } from './type';

export const MESSAGE_TYPE_OPTIONS = [
  {
    label: 'Văn bản',
    value: MESSAGE_TYPE.TEXT,
  },
  {
    label: 'Hình ảnh',
    value: MESSAGE_TYPE.IMAGE,
  },
  {
    label: 'Tệp tin',
    value: MESSAGE_TYPE.FILE,
  },
  {
    label: 'Hệ thống',
    value: MESSAGE_TYPE.SYSTEM,
  },
];

export const CHAT_STATUS_OPTIONS = [
  {
    label: 'Hoạt động',
    value: CHAT_STATUS.ACTIVE,
  },
  {
    label: 'Đã lưu trữ',
    value: CHAT_STATUS.ARCHIVED,
  },
  {
    label: 'Đã xóa',
    value: CHAT_STATUS.DELETED,
  },
];

export const CHAT_CONFIG = {
  MAX_MESSAGE_LENGTH: 1000,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
  ],
  MESSAGE_LOAD_LIMIT: 50,
  TYPING_TIMEOUT: 3000,
  ONLINE_STATUS_TIMEOUT: 300000, // 5 minutes
};
