import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Row, Typography } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ChatForm from '../form';
import ChatList from '../list';
import ChatRoom from '../room';
import { useChatStore } from '../store';

const { Title } = Typography;

export default function ChatLayout() {
  const { t } = useTranslation();
  const [showForm, setShowForm] = useState(false);
  const { currentChat } = useChatStore();

  const handleCreateChat = () => {
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    // Refresh chat list if needed
  };

  const handleFormCancel = () => {
    setShowForm(false);
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <Card className="border-b rounded-none">
        <div className="flex items-center justify-between">
          <Title level={3} className="mb-0">
            Chat
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateChat}
          >
            Tạo cuộc trò chuyện
          </Button>
        </div>
      </Card>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Row className="h-full">
          {/* Chat List */}
          <Col span={8} className="border-r">
            <ChatList />
          </Col>

          {/* Chat Room */}
          <Col span={16}>
            {currentChat ? (
              <ChatRoom chatId={currentChat.id} />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Title level={4} type="secondary">
                    Chọn một cuộc trò chuyện để bắt đầu
                  </Title>
                  <p className="text-gray-500">
                    Chọn một cuộc trò chuyện từ danh sách bên trái hoặc tạo mới
                  </p>
                </div>
              </div>
            )}
          </Col>
        </Row>
      </div>

      {/* Create Chat Form */}
      <ChatForm
        open={showForm}
        onCancel={handleFormCancel}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
}
