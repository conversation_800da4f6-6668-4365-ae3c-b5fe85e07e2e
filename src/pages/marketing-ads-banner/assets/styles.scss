.marketing-ads-banner {
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }
  }

  .banner-image {
    border-radius: 4px;
    object-fit: cover;
  }

  .type-tag {
    &.image {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.video {
      background-color: #f9f0ff;
      border-color: #d3adf7;
      color: #722ed1;
    }

    &.html {
      background-color: #e6fffb;
      border-color: #87e8de;
      color: #13c2c2;
    }
  }

  .status-tag {
    &.active {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.inactive {
      background-color: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }

    &.pending {
      background-color: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
  }

  .platform-tag {
    &.web {
      background-color: #f0f5ff;
      border-color: #adc6ff;
      color: #2f54eb;
    }

    &.mobile {
      background-color: #fff0f6;
      border-color: #ffadd2;
      color: #eb2f96;
    }

    &.all {
      background-color: #fcffe6;
      border-color: #eaff8f;
      color: #7cb305;
    }
  }
}

.form_modal {
  .ant-modal-body {
    padding: 24px;
  }

  .ant-form-item-label > label {
    font-weight: 500;
  }

  .image-upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #1890ff;
    }
  }

  .date-range-picker {
    width: 100%;
  }
}
