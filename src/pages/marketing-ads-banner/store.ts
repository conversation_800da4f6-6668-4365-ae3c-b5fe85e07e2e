import { create } from 'zustand';
import { MarketingAdsBanner } from './type';

interface MarketingAdsBannerStore {
  items: MarketingAdsBanner[];
  currentItem: MarketingAdsBanner | null;
  loading: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  searchKeyword: string;
  filters: {
    status?: string;
    platform?: string;
    position?: string;
    type?: string;
  };

  // Actions
  setItems: (items: MarketingAdsBanner[]) => void;
  setCurrentItem: (item: MarketingAdsBanner | null) => void;
  setLoading: (loading: boolean) => void;
  setTotal: (total: number) => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setSearchKeyword: (keyword: string) => void;
  setFilters: (filters: any) => void;

  // Reset
  reset: () => void;
}

const useMarketingAdsBannerStore = create<MarketingAdsBannerStore>((set) => ({
  items: [],
  currentItem: null,
  loading: false,
  total: 0,
  currentPage: 1,
  pageSize: 10,
  searchKeyword: '',
  filters: {},

  setItems: (items) => set({ items }),
  setCurrentItem: (item) => set({ currentItem: item }),
  setLoading: (loading) => set({ loading }),
  setTotal: (total) => set({ total }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setPageSize: (size) => set({ pageSize: size }),
  setSearchKeyword: (keyword) => set({ searchKeyword: keyword }),
  setFilters: (filters) => set({ filters }),

  reset: () =>
    set({
      items: [],
      currentItem: null,
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 10,
      searchKeyword: '',
      filters: {},
    }),
}));

export default useMarketingAdsBannerStore;
