import {
  ApiResponse,
  ApiResponsePagination,
  apiService,
} from '../../services/api.service';
import { SelectOption } from '../../types.global';
import { MODULE } from './config';
import { MarketingAdsBanner } from './type';

const url = `/api/admin/v1/marketing/ads-banners`;

export async function getItems(
  params: any,
): Promise<ApiResponsePagination<MarketingAdsBanner[]>> {
  const response = await apiService.get<
    ApiResponsePagination<MarketingAdsBanner[]>
  >(url, { params });
  return response.data;
}

export async function getItem(
  id: string,
): Promise<ApiResponse<MarketingAdsBanner>> {
  const response = await apiService.get<ApiResponse<MarketingAdsBanner>>(
    `${url}/${id}`,
  );
  return response.data;
}

export async function createItem(
  payload: any,
): Promise<ApiResponse<MarketingAdsBanner>> {
  const response = await apiService.post<ApiResponse<MarketingAdsBanner>>(
    url,
    payload,
  );
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<MarketingAdsBanner>> {
  const response = await apiService.put<ApiResponse<MarketingAdsBanner>>(
    `${url}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(`${url}/${id}`);
  return response.data;
}

export async function getSelectOptions(): Promise<SelectOption[]> {
  const response = await getItems({ limit: 1000, status: 'active' });
  if (response.status.success) {
    return response.data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  }
  return [];
}
