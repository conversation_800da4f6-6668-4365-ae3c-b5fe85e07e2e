import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  Image,
  message,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Table,
  Tag,
} from 'antd';
import dayjs from 'dayjs';
import queryString from 'query-string';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { BackButton } from '../../../components/button';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { getPageNumber } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useMarketingAdsBannerStore from '../store';
import { MarketingAdsBanner } from '../type';
import Search from './search';

export default function MarketingAdsBannerList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useMarketingAdsBannerStore();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<MarketingAdsBanner[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  async function fetchData(payload?: any) {
    const params = {
      ...pagination,
      ...query,
      ...filters,
      ...payload,
    };
    const response = await getItems(params);
    if (response.status.success) {
      setItems(response.data);
      setTotal(response.meta.total);
    } else {
      message.error(response.status.message);
    }
  }

  const onPagingChange = (page: number, limit: number) => {
    logger('[page]', { page, limit });
    setPagination({ page, limit });
    fetchData({ page, limit });
  };

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData(values);
  };

  useEffectOnce(() => {
    fetchData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteItemSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleTableChange = (page: number, pageSize: number) => {
    onPagingChange(page, pageSize);
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'red';
      case 'pending':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'blue';
      case 'video':
        return 'purple';
      case 'html':
        return 'cyan';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
      render: (dom: any, record: any) => (
        <span
          className="text-blue-600 cursor-pointer"
          onClick={() => navigate(`/${MODULE}/${record.id}`)}
        >
          {dom}
        </span>
      ),
    },
    {
      title: t('type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>
          {type === 'image'
            ? t('typeImage')
            : type === 'video'
              ? t('typeVideo')
              : t('typeHtml')}
        </Tag>
      ),
    },
    {
      title: t('image'),
      dataIndex: 'image',
      key: 'image',
      render: (image: string) =>
        image ? (
          <Image
            width={50}
            height={30}
            src={image}
            style={{ objectFit: 'cover' }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <span className="text-gray-400">-</span>
        ),
    },
    {
      title: t('position'),
      dataIndex: 'position',
      key: 'position',
    },
    {
      title: t('platform'),
      dataIndex: 'platform',
      key: 'platform',
      render: (platform: string) => (
        <Tag color="geekblue">
          {platform === 'web'
            ? t('web')
            : platform === 'mobile'
              ? t('mobile')
              : t('all')}
        </Tag>
      ),
    },
    {
      title: t('fromDate'),
      dataIndex: 'from_date',
      key: 'from_date',
      render: (date: string) =>
        date ? dayjs(date).format('DD/MM/YYYY HH:mm') : '-',
    },
    {
      title: t('toDate'),
      dataIndex: 'to_date',
      key: 'to_date',
      render: (date: string) =>
        date ? dayjs(date).format('DD/MM/YYYY HH:mm') : '-',
    },
    {
      title: t('sortOrder'),
      dataIndex: 'sort_order',
      key: 'sort_order',
      render: (order: number) => (
        <span className="font-mono bg-gray-100 px-2 py-1 rounded">{order}</span>
      ),
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status === 'active'
            ? t('active')
            : status === 'inactive'
              ? t('inactive')
              : t('pending')}
        </Tag>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirmMessage')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <div className="bg-gray flex justify-between p-4">
        <div className="flex items-center space-x-2">
          <BackButton destination="dashboard" />
          <div className="text-xl font-bold">{t('title')}</div>
        </div>
        <div className="gap-4">
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('add')}
          </Button>
        </div>
      </div>
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
          scroll={{ x: 1200 }}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <Pagination
              defaultCurrent={getPageNumber(query, 'page', 1)}
              total={total}
              defaultPageSize={pagination.limit}
              showSizeChanger
              showTitle={false}
              onChange={handleTableChange}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
      </Space>
    </div>
  );
}
