import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';

const { Option } = Select;

function SearchForm(props: any) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    logger('[values]', values);
    props.onChange(values);
  };

  function handlerRefresh() {
    form.resetFields();
    props.onChange({});
  }

  return (
    <Form
      layout="vertical"
      form={form}
      name="frmSearch"
      onFinish={onFinish}
      className="p-4 bg-white rounded-lg mb-4"
    >
      <Row>
        <Col span={24}>
          <Row gutter={16}>
            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="search"
                label={t('search')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Input placeholder={t('searchPlaceholder')} />
              </Form.Item>
            </Col>

            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="status"
                label={t('status')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder={t('status')} allowClear>
                  <Option value="active">{t('active')}</Option>
                  <Option value="inactive">{t('inactive')}</Option>
                  <Option value="pending">{t('pending')}</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="platform"
                label={t('platform')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder={t('platform')} allowClear>
                  <Option value="web">{t('web')}</Option>
                  <Option value="mobile">{t('mobile')}</Option>
                  <Option value="all">{t('all')}</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} md={6} lg={6}>
              <Form.Item
                name="type"
                label={t('type')}
                rules={[{ required: false, message: t('inputData') }]}
              >
                <Select placeholder={t('type')} allowClear>
                  <Option value="image">{t('typeImage')}</Option>
                  <Option value="video">{t('typeVideo')}</Option>
                  <Option value="html">{t('typeHtml')}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col>
          <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
            {t('search')}
          </Button>
        </Col>
        <Col>
          <Button onClick={handlerRefresh} icon={<ReloadOutlined />}>
            {t('reset')}
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export default SearchForm;
