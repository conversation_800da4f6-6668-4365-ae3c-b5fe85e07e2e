import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Switch,
} from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { FormHeader } from '../../../components/form-header';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import { SelectMarketingAdsPosition } from '../../marketing-ads-position';
import MediaUpload from '../../editor-media/components/media-upload';
import { MediaFile } from '../../../components/upload/type';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [uploadedImages, setUploadedImages] = useState<MediaFile[]>([]);

  const initForm = useMemo(
    () => ({
      status: 'pending',
      platform: 'web',
      type: 'image',
      open_in_new_tab: true,
      sort_order: 1,
    }),
    [],
  );

  const getItemData = useCallback(
    async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        const data = {
          ...res.data,
          dateRange:
            res.data.from_date && res.data.to_date
              ? [dayjs(res.data.from_date), dayjs(res.data.to_date)]
              : null,
        };
        form.setFieldsValue(data);

        // Handle existing image
        if (res.data.image) {
          const existingImage: MediaFile = {
            id: Date.now(), // temporary ID
            url: res.data.image,
            public_url: res.data.image,
            filename: 'existing-image',
            original_filename: 'existing-image',
            mimetype: 'image/*',
            content_type: 'image/*',
            size: 0,
            media_type: 'image',
            status: 'done', // Add the missing status property
          };
          setUploadedImages([existingImage]);
        }
      } else {
        message.error(res.status.message);
      }
    },
    [form],
  );

  const handleImageChange = useCallback(
    (files: MediaFile[]) => {
      setUploadedImages(files);
      // Update the form field with the first image URL
      if (files.length > 0) {
        form.setFieldValue('image', files[0].url || files[0].public_url);
      } else {
        form.setFieldValue('image', '');
      }
    },
    [form],
  );

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
      form.setFieldsValue(initForm);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id, form, getItemData, initForm]);

  const onFinish = async (values: any) => {
    logger('[onFinish]', values);

    const payload = {
      ...values,
      from_date: values.dateRange ? values.dateRange[0].toISOString() : null,
      to_date: values.dateRange ? values.dateRange[1].toISOString() : null,
    };
    delete payload.dateRange;

    let response;
    if (isNew) {
      response = await createItem(payload);
    } else {
      response = await updateItem(id as string, payload);
    }

    if (response.status.success) {
      message.success(isNew ? t('createItemSuccess') : t('updateItemSuccess'));
      onChange(true);
    } else {
      message.error(response.status.message);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    logger('[onFinishFailed]', errorInfo);
  };

  const onFormChange = (changedFields: any, allFields: any) => {
    logger('[onFormChange]', { changedFields, allFields });
  };

  return (
    <div>
      <FormHeader title={isNew ? t('add') : t('edit')} />
      <Form
        form={form}
        layout="vertical"
        name="frmDetail"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        onFieldsChange={onFormChange}
        initialValues={initForm}
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <FormItem
              name="name"
              label={t('name')}
              rules={[
                { required: true, message: t('nameRequired') },
                { min: 2, message: t('nameMinLength') },
                { max: 100, message: t('nameMaxLength') },
              ]}
            >
              <Input placeholder={t('namePlaceholder')} />
            </FormItem>
          </Col>

          <Col xs={24} md={12}>
            <FormItem
              name="type"
              label={t('type')}
              rules={[{ required: true, message: t('typeRequired') }]}
            >
              <Select placeholder={t('type')}>
                <Option value="image">{t('typeImage')}</Option>
                <Option value="video">{t('typeVideo')}</Option>
                <Option value="html">{t('typeHtml')}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <FormItem
              name="position"
              label={t('position')}
              rules={[{ required: true, message: t('positionRequired') }]}
            >
              <SelectMarketingAdsPosition placeholder={t('position')} />
            </FormItem>
          </Col>

          <Col xs={24} md={12}>
            <FormItem
              name="platform"
              label={t('platform')}
              rules={[{ required: true, message: t('platformRequired') }]}
            >
              <Select placeholder={t('platform')}>
                <Option value="web">{t('web')}</Option>
                <Option value="mobile">{t('mobile')}</Option>
                <Option value="all">{t('all')}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24}>
            <FormItem name="image" label={t('image')}>
              <MediaUpload
                multiple={false}
                maxCount={1}
                accept="image/*"
                initialImages={uploadedImages}
                onChange={handleImageChange}
                mode="card"
                uploadText={t('uploadImage') || 'Upload Image'}
              />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <FormItem name="link" label={t('link')}>
              <Input placeholder={t('linkPlaceholder')} />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24}>
            <FormItem name="content" label={t('content')}>
              <TextArea rows={4} placeholder={t('contentPlaceholder')} />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <FormItem
              name="dateRange"
              label={`${t('fromDate')} - ${t('toDate')}`}
              rules={[{ required: true, message: t('fromDateRequired') }]}
            >
              <RangePicker
                style={{ width: '100%' }}
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </FormItem>
          </Col>

          <Col xs={24} md={12}>
            <FormItem
              name="sort_order"
              label={t('sortOrder')}
              rules={[{ required: true, message: t('sortOrderRequired') }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                placeholder={t('sortOrderPlaceholder')}
              />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col xs={24} md={12}>
            <FormItem
              name="open_in_new_tab"
              label={t('openInNewTab')}
              valuePropName="checked"
            >
              <Switch />
            </FormItem>
          </Col>

          <Col xs={24} md={12}>
            <FormItem
              name="status"
              label={t('status')}
              rules={[{ required: true, message: t('statusRequired') }]}
            >
              <Select placeholder={t('status')}>
                <Option value="active">{t('active')}</Option>
                <Option value="inactive">{t('inactive')}</Option>
                <Option value="pending">{t('pending')}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>

        <div className="form_footer">
          <FormItem>
            <Button type="primary" htmlType="submit">
              {isNew ? t('btnAdd') : t('btnSave')}
            </Button>
          </FormItem>
        </div>
      </Form>
    </div>
  );
};

export default IndexForm;
