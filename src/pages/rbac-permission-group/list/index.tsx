import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import CursorPaginationV2 from '../../../components/pagination/cursor-pagination-v2';
import { ListHeader } from '../../../components';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import ModalForm from '../form/modal';
import useRbacModuleStore from '../store';
import { RbacPermissionGroup } from '../type';
import Search from './search';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useRbacModuleStore();

  const [cursorAfter, setCursorAfter] = useState<string | null>(null);
  const [cursorHistory, setCursorHistory] = useState<string[]>([]);

  const isFirstPage = cursorHistory.length === 0;

  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<RbacPermissionGroup[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [limit] = useState<number>(10);

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        limit,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      // Uncomment if you want to update browser URL with params
      // //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      console.log('response', response);
      if (response.status.success) {
        setItems(response.data);
        setCursorAfter(response.meta.next_cursor ?? null);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, limit],
  );

  useEffectOnce(() => {
    setCursorHistory([]);
    fetchData();
  });

  const handleNext = () => {
    if (cursorAfter) {
      const currentCursor = cursorAfter;
      setCursorHistory((prev) => [...prev, currentCursor]);
      fetchData({ cursor: cursorAfter });
    }
  };

  const handleBack = () => {
    if (cursorHistory.length > 0) {
      setCursorHistory((prev) => prev.slice(0, -1));

      if (cursorHistory.length <= 1) {
        fetchData();
      } else {
        const previousCursor = cursorHistory[cursorHistory.length - 2];
        fetchData({ cursor: previousCursor });
      }
    }
  };

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    // //changeBrowserLocation(navigate, pathname, values);
    setFilters(values);
    fetchData(values);
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.group_id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.group_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    {
      title: t('id'),
      dataIndex: 'group_id',
      key: 'id',
      render: (dom: any, record: any) => (
        <span
          className="text-blue-600 cursor-pointer"
          onClick={() => navigate(`/${MODULE}/${record.group_id}`)}
        >
          {dom}
        </span>
      ),
    },

    {
      title: t('permissionGroupName'),
      dataIndex: 'permission_group_name',
      key: 'permission_group_name',
    },
    {
      title: t('description'),
      dataIndex: 'permission_group_description',
      key: 'permission_group_description',
    },
    {
      title: t('active'),
      dataIndex: 'active',
      key: 'active',
      render: (dom: any, record: any) => (
        <span>{dom ? t('statusActive') : t('statusDeActive')}</span>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.group_id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <ListHeader
        title={t('module_name')}
        module={MODULE}
        onAddClick={() => handleActions('add', null)}
      />
      <Search query={query} loading={loading} onChange={handleFilters}></Search>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>

        <Row justify="end" className="p-4">
          <Col>
            <CursorPaginationV2
              onBack={handleBack}
              onNext={handleNext}
              disabledBack={isFirstPage}
              disabledNext={!cursorAfter}
              loading={loading}
              isFirstPage={isFirstPage}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
      </Space>
    </div>
  );
}
