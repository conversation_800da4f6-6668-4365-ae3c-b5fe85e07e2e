import { Button, Col, Form, Input, message, Row, Select, Switch } from 'antd';
import _ from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useEffectOnce } from 'react-use';
import { FormHeader } from '../../../components';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import useBlogStore from '../store';
import { RbacPermissionGroup } from '../type';

const FormItem = Form.Item;
const { Option } = Select;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const IndexForm: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading } = useBlogStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<RbacPermissionGroup>();
  const [formValues, setFormValues] = useState<RbacPermissionGroup>();
  const [resources, setResources] = useState<any[]>([]);
  const [selectedResources, setSelectedResources] = useState<number[]>([]);

  const initForm = {};

  const getItemData = useCallback(
    async (_id: string) => {
      const res = await getItem(_id);
      if (res.status.success) {
        setItem(res.data);
        form.setFieldsValue(res.data);
      } else {
        message.error(res.status.message);
      }
    },
    [form],
  );

  // Load resources once on component mount
  useEffectOnce(() => {
    const loadResources = async () => {
      try {
        // If needed, add code to load resources here
        // For example: const res = await apiResource.getItems();
        // setResources(res.data);
      } catch (error) {
        logger('Error loading resources', error);
      }
    };

    loadResources();

    // If we have an ID and it's not 'create', load the item data
    if (id && !['create', undefined].includes(id)) {
      getItemData(id);
    }
  });

  // Handle ID changes
  useEffect(() => {
    logger(id);
    if (['create', undefined].includes(id)) {
      setIsNew(true);
    } else if (id) {
      setIsNew(false);
    }
  }, [id, logger]);

  const onFinish = async (values: RbacPermissionGroup) => {
    try {
      let res;
      if (isNew) {
        res = await createItem(values);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        res = await updateItem(id!, values);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    }
  };

  const handleValuesChange = (newValue: any, allValues: any) => {
    logger(newValue);
    logger(allValues);
    setFormValues(allValues);

    if (newValue.resource_ids) {
      setSelectedResources(newValue.resource_ids);
    }
  };

  return (
    <div>
      <FormHeader
        title={isNew ? t('addNewPermissionGroup') : t('editPermissionGroup')}
        backDestination="/rbac-permission-group"
      />
      <Form
        style={{ marginTop: 8 }}
        form={form}
        name="form"
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        //initialValues={initForm}
        onValuesChange={handleValuesChange}
      >
        <div className="form_content">
          <Row gutter={16}>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('permissionGroupName')}
                name="permission_group_name"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
            <Col xs={24} lg={24}>
              <FormItem
                label={t('description')}
                name="permission_group_description"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>

            <Col xs={24} lg={24}>
              <FormItem
                label={t('active')}
                name="active"
                rules={[{ required: false, message: t('pleaseEnterData') }]}
              >
                <Switch
                  checkedChildren={t('statusActive')}
                  unCheckedChildren={t('statusDeActive')}
                />
              </FormItem>
            </Col>
          </Row>
        </div>
        <div className="form_footer">
          <FormItem>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isNew ? t('btnAdd') : t('btnUpdate')}
            </Button>
          </FormItem>
        </div>
      </Form>
    </div>
  );
};

export default IndexForm;
