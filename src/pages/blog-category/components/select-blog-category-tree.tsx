import type { TreeSelectProps } from 'antd';
import { TreeSelect } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { getTree } from '../api';
import { MODULE } from '../config';
import { BlogCategory } from '../type';
interface TreeNode {
  title: string;
  value: string | number;
  children?: TreeNode[];
}

interface Props extends TreeSelectProps {
  isOptionAll?: boolean;
  isParentAll?: boolean;
}

export const SelectBlogCategoryTree: React.FC<Props> = (props) => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const { isOptionAll = false, isParentAll = false } = props;
  const [loading, setLoading] = useState(false);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);

  // Function to transform BlogCategory data into TreeNode structure
  const transformDataToTreeNode = useCallback(
    (data: BlogCategory[]): TreeNode[] =>
      data.map((item) => ({
        title: item.name,
        value: item.id,
        children: item.children ? transformDataToTreeNode(item.children) : [],
      })),
    [],
  );

  const getDataCallback = useCallback(async () => {
    try {
      setLoading(true);
      const res = await getTree();
      if (res.status.success) {
        const _treeData = transformDataToTreeNode(res.data);
        if (isOptionAll) {
          _treeData.unshift({ title: 'Tất cả', value: '', children: [] });
        }

        if (isParentAll) {
          _treeData.unshift({
            title: t('parent', 'Danh mục cha'),
            value: '',
            children: [],
          });
        }

        logger('[_treeData]', _treeData);
        setTreeData(_treeData);
      }
    } catch (error) {
      logger('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  }, [isOptionAll, transformDataToTreeNode, logger]);

  useEffect(() => {
    getDataCallback();
  }, [getDataCallback]);

  return (
    <TreeSelect
      treeData={treeData}
      loading={loading}
      {...props}
      style={{ width: '100%' }}
      treeDefaultExpandAll
      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
    />
  );
};

export default SelectBlogCategoryTree;
