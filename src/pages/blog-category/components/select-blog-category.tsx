import { Select, SelectProps } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import ConsoleService from '../../../services/console.service';
import { getOptions } from '../api';
import { MODULE } from '../config';

interface Props extends SelectProps {
  isOptionAll?: boolean;
}

export const SelectBlogCategory: React.FC<Props> = (props) => {
  const logger = ConsoleService.register(MODULE);
  const { isOptionAll = false } = props;
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<{ label: string; value: string }[]>(
    [],
  );

  const getDataCallback = useCallback(async () => {
    setLoading(true);
    const list = await getOptions();
    logger(list);

    const _options = list.data;

    if (isOptionAll) {
      _options.unshift({ label: 'Tất cả', value: '' });
    }

    logger('[_options]', _options);
    setOptions(_options);
    setLoading(false);
  }, []);

  useEffect(() => {
    getDataCallback();
  }, []);

  return <Select options={options} loading={loading} {...props} />;
};
