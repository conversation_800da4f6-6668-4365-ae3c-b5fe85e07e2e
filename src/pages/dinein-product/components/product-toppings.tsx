import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Form,
  InputNumber,
  message,
  Modal,
  Select,
  Space,
  Switch,
  Table,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  batchAssignToppings,
  createProductTopping,
  deleteProductTopping,
  getProductToppings,
  updateProductTopping,
} from '../api';
import { getAllToppings } from '../api/topping-api';
import { MODULE } from '../config';
import { ProductTopping, ToppingItem } from '../type';

interface ProductToppingsProps {
  productId: string;
}

interface ToppingCategoryItem {
  category_id: number;
  name: string;
  toppings: ToppingItem[];
}

const ProductToppings: React.FC<ProductToppingsProps> = ({ productId }) => {
  const { t } = useTranslation(MODULE);
  const [toppings, setToppings] = useState<ProductTopping[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editItem, setEditItem] = useState<ProductTopping | null>(null);
  const [toppingCategories, setToppingCategories] = useState<
    ToppingCategoryItem[]
  >([]);
  const [allToppings, setAllToppings] = useState<ToppingItem[]>([]);
  const [form] = Form.useForm();

  const fetchToppings = async () => {
    if (!productId) return;

    setLoading(true);
    try {
      const response = await getProductToppings(productId);
      setToppings(response.data || []);
    } catch (error) {
      console.error('Error loading toppings:', error);
      message.error('Không thể tải danh sách topping');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchToppings();
  }, [productId]);

  // Tải danh sách topping
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Lấy tất cả các topping
        const toppingResponse = await getAllToppings({ limit: 1000 });
        if (toppingResponse.data && Array.isArray(toppingResponse.data)) {
          setAllToppings(toppingResponse.data);
        }
      } catch (error) {
        console.error('Error loading topping data:', error);
        message.error('Không thể tải dữ liệu topping');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleCreate = () => {
    setEditItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: ProductTopping) => {
    setEditItem(record);
    form.setFieldsValue({
      topping_id: record.topping_id,
      price_override: record.price_override,
      is_default: record.is_default,
      max_quantity: record.max_quantity,
      display_order: record.display_order,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: 'Bạn có chắc chắn muốn xóa topping này?',
      okText: 'Xóa',
      cancelText: 'Hủy',
      onOk: async () => {
        try {
          await deleteProductTopping(id.toString());
          message.success('Xóa topping thành công');
          fetchToppings();
        } catch (error) {
          console.error('Error deleting topping:', error);
          message.error('Không thể xóa topping');
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const payload = {
        ...values,
        product_id: parseInt(productId),
      };

      if (editItem) {
        await updateProductTopping(
          editItem.product_topping_id?.toString() || '',
          payload,
        );
        message.success('Cập nhật topping thành công');
      } else {
        await createProductTopping(payload);
        message.success('Thêm topping thành công');
      }

      setModalVisible(false);
      fetchToppings();
    } catch (error) {
      console.error('Error saving topping:', error);
      message.error('Không thể lưu topping');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchAssign = async (selectedToppings: ProductTopping[]) => {
    try {
      setLoading(true);

      const payload = {
        product_id: parseInt(productId),
        toppings: selectedToppings.map((item) => ({
          topping_id: item.topping_id,
          price_override: item.price_override,
          is_default: item.is_default || false,
          max_quantity: item.max_quantity || 1,
          display_order: item.display_order || 0,
        })),
      };

      await batchAssignToppings(payload);
      message.success('Cập nhật toppings thành công');
      fetchToppings();
    } catch (error) {
      console.error('Error batch assigning toppings:', error);
      message.error('Không thể cập nhật toppings');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Tên topping',
      dataIndex: ['topping', 'name'],
      key: 'name',
      render: (text: string, record: ProductTopping) =>
        record.topping?.name || '',
    },
    {
      title: 'Giá mặc định',
      dataIndex: ['topping', 'price'],
      key: 'default_price',
      render: (text: number, record: ProductTopping) =>
        record.topping?.price
          ? `${record.topping.price.toLocaleString()}đ`
          : '0đ',
    },
    {
      title: 'Giá tùy chỉnh',
      dataIndex: 'price_override',
      key: 'price_override',
      render: (text: number) => (text ? `${text.toLocaleString()}đ` : '-'),
    },
    {
      title: 'Mặc định',
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault: boolean) => (isDefault ? 'Có' : 'Không'),
    },
    {
      title: 'Số lượng tối đa',
      dataIndex: 'max_quantity',
      key: 'max_quantity',
    },
    {
      title: 'Thứ tự hiển thị',
      dataIndex: 'display_order',
      key: 'display_order',
    },
    {
      title: 'Thao tác',
      key: 'action',
      render: (_: any, record: ProductTopping) => (
        <Space>
          <Button
            type="primary"
            ghost
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.product_topping_id || 0)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div className="product-toppings">
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
          Thêm Topping
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={toppings}
        rowKey={(record) => record.product_topping_id?.toString() || ''}
        loading={loading}
        pagination={false}
      />

      <Modal
        title={editItem ? 'Cập nhật Topping' : 'Thêm Topping Mới'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={handleSubmit}
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="Topping"
            name="topping_id"
            rules={[{ required: true, message: 'Vui lòng chọn topping' }]}
          >
            <Select placeholder="Chọn topping">
              {allToppings.map((topping) => (
                <Select.Option
                  key={topping.topping_id}
                  value={topping.topping_id}
                >
                  {topping.name} - {topping.price?.toLocaleString()}đ
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="Giá tùy chỉnh"
            name="price_override"
            tooltip="Để trống để sử dụng giá mặc định của topping"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="Nhập giá tùy chỉnh"
              formatter={(value) =>
                `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
              parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            label="Mặc định"
            name="is_default"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="Số lượng tối đa"
            name="max_quantity"
            initialValue={1}
            rules={[
              { required: true, message: 'Vui lòng nhập số lượng tối đa' },
            ]}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="Thứ tự hiển thị"
            name="display_order"
            initialValue={0}
            rules={[
              { required: true, message: 'Vui lòng nhập thứ tự hiển thị' },
            ]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductToppings;
