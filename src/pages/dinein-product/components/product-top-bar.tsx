import { PlusOutlined } from '@ant-design/icons';
import { Button, Space } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import { MODULE } from '../config';

interface ProductTopBarProps {
  onAdd?: () => void;
}

const ProductTopBar: React.FC<ProductTopBarProps> = ({ onAdd }) => {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();

  const handleAdd = () => {
    if (onAdd) {
      onAdd();
    } else {
      navigate(`/${MODULE}/create`);
    }
  };

  return (
    <div className="flex justify-between items-center mb-4">
      <h1 className="text-xl font-bold">Quản lý sản phẩm dùng tại chỗ</h1>
      <Space>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          Thêm sản phẩm
        </Button>
      </Space>
    </div>
  );
};

export default ProductTopBar;
