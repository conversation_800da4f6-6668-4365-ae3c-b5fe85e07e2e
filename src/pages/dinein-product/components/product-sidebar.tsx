import { AppstoreOutlined, FileOutlined, TagOutlined } from '@ant-design/icons';
import { Card, Menu } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';

interface ProductSidebarProps {
  onSelect?: (key: string) => void;
  selectedKey?: string;
}

const ProductSidebar: React.FC<ProductSidebarProps> = ({
  onSelect,
  selectedKey = 'all',
}) => {
  const { t } = useTranslation(MODULE);

  const handleMenuClick = ({ key }: { key: string }) => {
    if (onSelect) {
      onSelect(key);
    }
  };

  return (
    <Card bordered={false} className="h-full">
      <Menu
        mode="inline"
        selectedKeys={[selectedKey]}
        onClick={handleMenuClick}
        items={[
          {
            key: 'all',
            icon: <FileOutlined />,
            label: 'Tất cả sản phẩm',
          },
          {
            key: 'published',
            icon: <FileOutlined />,
            label: '<PERSON><PERSON> bán',
          },
          {
            key: 'draft',
            icon: <FileOutlined />,
            label: 'Bản nháp',
          },
          {
            key: 'categories',
            icon: <AppstoreOutlined />,
            label: 'Danh mục',
          },
          {
            key: 'tags',
            icon: <TagOutlined />,
            label: 'Thẻ',
          },
        ]}
      />
    </Card>
  );
};

export default ProductSidebar;
