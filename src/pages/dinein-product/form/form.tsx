import {
  ArrowLeftOutlined,
  DeleteOutlined,
  SaveOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Space,
  Tabs,
} from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SaveBarBottom } from '../../../components/save-bar';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import EditorMedia from '../../editor-media/components/editor-media';
import { MediaType } from '../../editor-media/type';
import { createItem, getItem, updateItem } from '../api';
import '../assets/styles.scss';
import { ProductToppings, SelectDineinCategory } from '../components';
import { MODULE } from '../config';
import useDineinProductStore from '../store';
import {
  DineinProduct,
  PRODUCT_STATUS,
  PRODUCT_STATUS_LABELS,
  transformProduct,
} from '../type';
const FormItem = Form.Item;
const { TabPane } = Tabs;

interface ProductFormProps {
  id?: string;
  onChange?: (reload: boolean) => void;
}

interface FormValues {
  name: string;
  description: string;
  content: string;
  slug: string;
  image_url: string;
  base_price: number;
  cost_price: number;
  product_code: string;
  barcode: string;
  unit: string;
  status: string;
  category_id: number;
}

const ProductForm: React.FC<ProductFormProps> = ({ id, onChange }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();
  const [form] = Form.useForm();
  const { loading, setLoading } = useDineinProductStore();
  const [product, setProduct] = useState<DineinProduct>();
  const [showEditorMedia, setShowEditorMedia] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaType[]>([]);
  const [imageUrl, setImageUrl] = useState<string>('');

  const fetchData = useCallback(async () => {
    if (!id) return;
    setLoading(true);
    try {
      const response = await getItem(id);
      const productData = transformProduct(response.data);
      setProduct(productData);
      form.setFieldsValue({
        name: productData.name,
        description: productData.description,
        content: productData.content,
        slug: productData.slug,
        image_url: productData.image_url,
        base_price: productData.base_price,
        cost_price: productData.cost_price,
        product_code: productData.product_code,
        barcode: productData.barcode,
        unit: productData.unit,
        status: productData.status,
        category_id: productData.category_id,
      });
    } catch (error) {
      logger('Error loading product:', error);
      message.error('Không thể tải thông tin sản phẩm');
    } finally {
      setLoading(false);
    }
  }, [id, form, setLoading, setProduct, logger]);

  useEffect(() => {
    fetchData();
  }, [id, fetchData]);

  useEffect(() => {
    if (product?.image_url) {
      setImageUrl(String(product.image_url));
    }
  }, [product]);

  const handleMediaSelect = (selected: MediaType[]) => {
    if (selected && selected.length > 0) {
      setSelectedMedia(selected);
      const url = selected[0].public_url;
      form.setFieldsValue({
        image_url: url,
      });
      setImageUrl(url || '');
    }
  };

  const handleRemoveImage = () => {
    form.setFieldsValue({
      image_url: '',
    });
    setImageUrl('');
    setSelectedMedia([]);
  };

  const handleSubmit = async (values: FormValues) => {
    setLoading(true);
    try {
      const payload: any = {
        ...values,
        image_url: form.getFieldValue('image_url'),
      };

      // Xử lý cost_price khi là null hoặc undefined
      if (payload.cost_price === null || payload.cost_price === undefined) {
        delete payload.cost_price;
      }

      if (id) {
        await updateItem(id, payload);
        message.success('Cập nhật sản phẩm thành công');
      } else {
        await createItem(payload);
        message.success('Tạo sản phẩm thành công');
      }

      if (onChange) {
        onChange(true);
      } else {
        navigate(`/${MODULE}/`);
      }
    } catch (error) {
      logger('Error saving product:', error);
      message.error('Có lỗi xảy ra khi lưu sản phẩm');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate(`/${MODULE}/`);
  };

  const handleSlugChange = (value: string) => {
    form.setFieldsValue({ slug: value });
  };

  return (
    <div className="dinein-product-form">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          status: PRODUCT_STATUS.DRAFT,
        }}
      >
        <Row gutter={16}>
          <Col span={16}>
            <Card title="Thông tin sản phẩm" bordered={false}>
              <Tabs defaultActiveKey="info">
                <TabPane tab="Thông tin cơ bản" key="info">
                  <FormItem
                    label="Tên sản phẩm"
                    name="name"
                    rules={[
                      { required: true, message: 'Vui lòng nhập tên sản phẩm' },
                    ]}
                  >
                    <Input placeholder="Nhập tên sản phẩm" />
                  </FormItem>

                  <FormItem label="Hình ảnh" name="image_url">
                    <Input placeholder="URL hình ảnh" readOnly hidden />
                    <div className="image-upload-container">
                      {imageUrl ? (
                        <div className="image-preview-container">
                          <div
                            className="image-preview-square"
                            style={{ backgroundImage: `url(${imageUrl})` }}
                          />
                          <Space className="image-actions">
                            <Button
                              icon={<UploadOutlined />}
                              onClick={() => setShowEditorMedia(true)}
                            >
                              Thay đổi
                            </Button>
                            <Button
                              danger
                              icon={<DeleteOutlined />}
                              onClick={handleRemoveImage}
                            >
                              Xóa
                            </Button>
                          </Space>
                        </div>
                      ) : (
                        <Button
                          icon={<UploadOutlined />}
                          onClick={() => setShowEditorMedia(true)}
                        >
                          Chọn ảnh
                        </Button>
                      )}
                    </div>
                  </FormItem>

                  {/* <FormItem
                    label="Slug"
                    name="slug"
                    rules={[{ required: true, message: 'Vui lòng nhập slug' }]}
                  >
                    <InputSlug
                      placeholder="nhap-ten-san-pham"
                      onChange={handleSlugChange}
                    />
                  </FormItem> */}

                  <FormItem label="Mô tả ngắn" name="description">
                    <Input.TextArea rows={3} placeholder="Nhập mô tả ngắn" />
                  </FormItem>

                  <FormItem label="Nội dung" name="content">
                    <Input.TextArea
                      rows={6}
                      placeholder="Nhập nội dung chi tiết"
                    />
                  </FormItem>

                  <Row gutter={16}>
                    <Col span={12}>
                      <FormItem
                        label="Giá cơ bản"
                        name="base_price"
                        rules={[
                          { required: true, message: 'Vui lòng nhập giá' },
                        ]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          formatter={(value) =>
                            `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                          }
                          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                          placeholder="Nhập giá"
                        />
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem label="Giá vốn" name="cost_price">
                        <InputNumber
                          style={{ width: '100%' }}
                          formatter={(value) =>
                            `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                          }
                          parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                          placeholder="Nhập giá vốn"
                        />
                      </FormItem>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={8}>
                      <FormItem label="Mã sản phẩm" name="product_code">
                        <Input placeholder="Nhập mã sản phẩm" />
                      </FormItem>
                    </Col>
                    <Col span={8}>
                      <FormItem label="Mã vạch" name="barcode">
                        <Input placeholder="Nhập mã vạch" />
                      </FormItem>
                    </Col>
                    <Col span={8}>
                      <FormItem label="Đơn vị" name="unit">
                        <Input placeholder="Nhập đơn vị" />
                      </FormItem>
                    </Col>
                  </Row>
                </TabPane>

                <TabPane tab="Toppings" key="toppings">
                  {id && <ProductToppings productId={id} />}
                  {!id && (
                    <div style={{ padding: '20px 0', textAlign: 'center' }}>
                      Vui lòng lưu sản phẩm trước khi thêm topping
                    </div>
                  )}
                </TabPane>
              </Tabs>
            </Card>
          </Col>

          <Col span={8}>
            <Card title="Trạng thái" bordered={false}>
              <FormItem name="status" label="Trạng thái">
                <Select
                  placeholder="Chọn trạng thái"
                  options={Object.entries(PRODUCT_STATUS).map(([_, value]) => ({
                    value,
                    label: PRODUCT_STATUS_LABELS[value],
                  }))}
                />
              </FormItem>

              <FormItem name="category_id" label="Danh mục">
                <SelectDineinCategory
                  placeholder="Chọn danh mục"
                  value={form.getFieldValue('category_id')}
                  onChange={(value) =>
                    form.setFieldsValue({ category_id: value })
                  }
                />
              </FormItem>
            </Card>
          </Col>
        </Row>

        <SaveBarBottom>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
            Quay lại
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            loading={loading}
          >
            {id ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </SaveBarBottom>
      </Form>

      <EditorMedia
        showMedia={showEditorMedia}
        onClose={() => setShowEditorMedia(false)}
        onChange={handleMediaSelect}
        initialMediaType="image"
      />
    </div>
  );
};

export default ProductForm;
