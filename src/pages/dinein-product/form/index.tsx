import React from 'react';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import IndexForm from './form';

export { default as ProductForm } from './form';
export { default as ProductPage } from './page';
export { default as ProductStandalone } from './standalone';

const Detail: React.FC = (props) => {
  const { id } = useParams();
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();

  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    navigate(`/${MODULE}/`);
  };

  return (
    <div className="">
      <IndexForm onChange={handleChangeForm} id={id}></IndexForm>
    </div>
  );
};

export default Detail;
