import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import { Button, Col, message, Popconfirm, Row, Space, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { deleteItem, getItems } from '../api';
import { MODULE } from '../config';
import useDineinProductStore from '../store';
import { DineinProduct, PRODUCT_STATUS, PRODUCT_STATUS_LABELS } from '../type';
import Search from './search';

export default function List() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useDineinProductStore();
  const paginationDefault = {
    before: null,
    after: null,
    limit: 10,
  };
  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [items, setItems] = useState<DineinProduct[]>([]);

  const fetchData = async () => {
    try {
      const params = {
        ...paginationDefault,
        ...filters,
        after: afterKey,
      };
      const response = await getItems(params);
      setItems(response.data);
      setTotal(response.meta?.total || 0);
      setNextCursor(response.meta?.cursor?.after);
    } catch (error) {
      logger.error(error);
      message.error('Không thể tải danh sách sản phẩm');
    }
  };

  useEffectOnce(() => {
    fetchData();
  });

  const handleSearch = (values: any) => {
    setFilters(values);
    fetchData();
  };

  const handleEdit = (id: string) => {
    navigate(`/${MODULE}/${id}`);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteItem(id);
      message.success('Xóa sản phẩm thành công');
      fetchData();
    } catch (error) {
      logger.error(error);
      message.error('Không thể xóa sản phẩm');
    }
  };

  const handleAdd = () => {
    navigate(`/${MODULE}/create`);
  };

  const getStatusTag = (status: string) => {
    const colorMap: any = {
      [PRODUCT_STATUS.PUBLISHED]: 'green',
      [PRODUCT_STATUS.PENDING]: 'orange',
      [PRODUCT_STATUS.PRIVATE]: 'blue',
      [PRODUCT_STATUS.TRASH]: 'red',
      [PRODUCT_STATUS.DRAFT]: 'default',
    };

    return (
      <Tag color={colorMap[status] || 'default'}>
        {PRODUCT_STATUS_LABELS[status as PRODUCT_STATUS] || status}
      </Tag>
    );
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'product_id',
      key: 'product_id',
      width: 80,
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DineinProduct) => (
        <a onClick={() => handleEdit(record.id!)}>{text}</a>
      ),
    },
    {
      title: 'Slug',
      dataIndex: 'slug',
      key: 'slug',
    },
    {
      title: 'Giá cơ bản',
      dataIndex: 'base_price',
      key: 'base_price',
      render: (price: number) =>
        price?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND',
        }) || '0 ₫',
    },
    {
      title: 'Danh mục',
      dataIndex: 'category_id',
      key: 'category_id',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 120,
      render: (_: any, record: DineinProduct) => (
        <Space size="middle">
          <a onClick={() => handleEdit(record.product_id!)}>
            <EditOutlined />
          </a>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa sản phẩm này?"
            onConfirm={() => handleDelete(record.product_id!)}
            okText="Có"
            cancelText="Không"
          >
            <a>
              <DeleteOutlined />
            </a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-4">
      <Row className="mb-4" justify="space-between" align="middle">
        <Col>
          <h1 className="text-xl font-bold">Quản lý sản phẩm dùng tại chỗ</h1>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={handleAdd}
          >
            Thêm sản phẩm
          </Button>
        </Col>
      </Row>

      <Search onSearch={handleSearch} />

      <Table
        rowKey="id"
        columns={columns}
        dataSource={items}
        loading={loading}
        pagination={false}
      />

      <div className="mt-4 flex justify-end">
        <CursorPagination
          total={total}
          isNext={isNext}
          isBack={isBack}
          onNext={goNext}
          onBack={goBack}
          onChange={fetchData}
        />
      </div>
    </div>
  );
}
