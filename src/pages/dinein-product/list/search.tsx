import { <PERSON><PERSON>, Card, Col, Form, Input, Row, Select } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';
import { PRODUCT_STATUS_LABELS } from '../type';

interface SearchProps {
  onSearch: (values: any) => void;
}

const Search: React.FC<SearchProps> = ({ onSearch }) => {
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  const handleSearch = (values: any) => {
    onSearch(values);
  };

  const handleReset = () => {
    form.resetFields();
    onSearch({});
  };

  return (
    <Card className="mb-4">
      <Form form={form} onFinish={handleSearch} layout="vertical">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="keyword" label="Từ khóa">
              <Input placeholder="Tên sản phẩm, slug..." />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="status" label="Trạng thái">
              <Select
                allowClear
                placeholder="Chọn trạng thái"
                options={Object.entries(PRODUCT_STATUS_LABELS).map(
                  ([value, label]) => ({
                    value,
                    label,
                  }),
                )}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="price_range" label="Khoảng giá">
              <Select
                allowClear
                placeholder="Chọn khoảng giá"
                options={[
                  { value: '0-50000', label: 'Dưới 50.000₫' },
                  { value: '50000-100000', label: '50.000₫ - 100.000₫' },
                  { value: '100000-200000', label: '100.000₫ - 200.000₫' },
                  { value: '200000-500000', label: '200.000₫ - 500.000₫' },
                  { value: '500000-', label: 'Trên 500.000₫' },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button type="primary" htmlType="submit">
              Tìm kiếm
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleReset}>
              Đặt lại
            </Button>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default Search;
