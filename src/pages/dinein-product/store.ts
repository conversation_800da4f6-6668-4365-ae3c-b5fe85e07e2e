import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import ConsoleService from '../../services/console.service';
import { deleteItem, getOptions } from './api';
import { MODULE } from './config';
import { DineinProduct } from './type';
const logger = ConsoleService.register(MODULE);

export interface StoreState {
  loading: boolean;
  items: DineinProduct[];
  options: any[];
  drawerData?: DineinProduct;
  setLoading: (loading: boolean) => void;
  delete: (id: string) => void;
  setOptions: (options: any) => void;
  deleteItem: (id: string) => Promise<void>;
  getOptions: () => Promise<void>;
  updateDrawerData: (data: Partial<DineinProduct>) => void;
}

const useDineinProductStore = create<StoreState>()(
  devtools(
    (set) => ({
      items: [],
      item: undefined,
      options: [],
      loading: false,
      drawerData: undefined,
      setLoading: (loading: boolean) => {
        set({ loading });
      },
      delete: (id: string) => {
        set((state) => {
          const itemsNew = state.items.filter((b) => b.id !== id);
          return { items: itemsNew };
        });
      },
      setOptions: (options: any) => {
        set({ options });
      },
      deleteItem: async (id: string) => {
        set({ loading: true });
        await deleteItem(id);
        set((state) => {
          const itemsNew = state.items.filter((b) => b.id !== id);
          return { items: itemsNew, loading: false };
        });
      },
      getOptions: async () => {
        set({ loading: true });
        try {
          const response = await getOptions();
          set({ options: response.data, loading: false });
        } catch (error) {
          logger.error(error);
          set({ loading: false });
        }
      },
      updateDrawerData: (data: Partial<DineinProduct>) => {
        set((state) => ({
          drawerData: { ...state.drawerData, ...data } as DineinProduct,
        }));
      },
    }),
    { name: 'dinein-product-store' },
  ),
);

export default useDineinProductStore;
