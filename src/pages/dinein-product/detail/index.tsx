import { Card, Col, Descriptions, Image, Row, Tag, Typography } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import ConsoleService from '../../../services/console.service';
import { getItem } from '../api';
import { MODULE } from '../config';
import { DineinProduct, PRODUCT_STATUS_LABELS } from '../type';

const { Title, Paragraph } = Typography;

const ProductDetail: React.FC = () => {
  const { id } = useParams();
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const [product, setProduct] = useState<DineinProduct>();
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    if (!id) return;
    setLoading(true);
    try {
      const response = await getItem(id);
      setProduct(response.data);
    } catch (error) {
      logger.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffectOnce(() => {
    fetchData();
  });

  if (loading) {
    return <div>Đang tải...</div>;
  }

  // if (!product) {
  //   return <div>Không tìm thấy thông tin sản phẩm</div>;
  // }

  return (
    <div className="p-4">
      <Card loading={loading} bordered={false}>
        <Row gutter={24}>
          <Col span={8}>
            <Image
              src={product.image || 'https://via.placeholder.com/300'}
              alt={product.title}
              style={{ width: '100%', maxWidth: 300 }}
            />
          </Col>
          <Col span={16}>
            <Title level={2}>{product.title}</Title>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="Giá bán">
                {product.price?.toLocaleString('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                }) || '0 ₫'}
              </Descriptions.Item>
              {product.sale_price && (
                <Descriptions.Item label="Giá khuyến mãi">
                  {product.sale_price?.toLocaleString('vi-VN', {
                    style: 'currency',
                    currency: 'VND',
                  })}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="Mã SKU">
                {product.sku}
              </Descriptions.Item>
              <Descriptions.Item label="Số lượng trong kho">
                {product.stock_quantity}
              </Descriptions.Item>
              <Descriptions.Item label="Trạng thái">
                <Tag color="blue">
                  {PRODUCT_STATUS_LABELS[product.status as string] ||
                    product.status}
                </Tag>
              </Descriptions.Item>
            </Descriptions>

            <div className="mt-4">
              <Title level={4}>Mô tả</Title>
              <Paragraph>{product.description}</Paragraph>
            </div>
          </Col>
        </Row>

        <div className="mt-4">
          <Title level={4}>Chi tiết sản phẩm</Title>
          <div dangerouslySetInnerHTML={{ __html: product.content || '' }} />
        </div>
      </Card>
    </div>
  );
};

export default ProductDetail;
