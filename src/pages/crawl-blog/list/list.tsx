import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_GROUP, MODULE_POPUP } from '../config';
import { CrawlBlogDetailModal } from '../detail';
import { CrawlBlogModal } from '../form/modal';
import useCrawlBlogStore from '../store';
import { CrawlBlog } from '../type';
import { CrawlBlogSearch } from './search';

function CrawlBlogList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useCrawlBlogStore();

  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showModalDetail, setShowModalDetail] = useState(false);
  const [items, setItems] = useState<CrawlBlog[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [blogCurrent, setBlogCurrent] = useState<CrawlBlog>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.meta.total);
        setTotal(response.meta.total);
        setNextCursor(response.meta.cursors.after);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname],
  );

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });

    setFilters(values);
  };

  useEffect(() => {
    fetchData({ after: afterKey });
  }, [fetchData, afterKey]);

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('form.deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: CrawlBlog) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/create`);
      }
    } else if (action === 'view') {
      setBlogCurrent(record);
      setShowModalDetail(true);
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    // {
    //   title: t('form.id'),
    //   dataIndex: 'id',
    //   key: 'id',
    //   render: (dom: any, record: any) => (
    //     <span
    //       className="text-blue-600 cursor-pointer"
    //       onClick={() => handleActions('view', record)}
    //     >
    //       {dom}
    //     </span>
    //   ),
    // },

    {
      title: t('form.source'),
      dataIndex: 'source',
      key: 'source',
    },
    {
      title: t('form.name'),
      dataIndex: 'name',
      key: 'name',
      render: (dom: any, record: any) => (
        <div className="flex justify-start max-w-[400px]">
          <div className="max-w-[100px] mr-3">
            <img
              src={
                record.image ??
                'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSrJgwdOAjqaZGS7kn35IVm_ZN6E4XFuJ7V_g&s'
              }
              alt="img"
            />
          </div>
          <div className="max-w-[300px]">
            <a
              className="text-blue-600 cursor-pointer"
              href={record.url}
              target="_blank"
              rel="noreferrer"
            >
              {record.name}
            </a>
          </div>
        </div>
      ),
    },
    // {
    //   title: t('form.url'),
    //   dataIndex: 'url',
    //   key: 'url',
    // },
    // {
    //   title: t('form.status'),
    //   dataIndex: 'status',
    //   key: 'status',
    //   render: (dom: string) => <span>{t(`status.${dom}`)}</span>,
    // },
    {
      title: t('list.actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('list.deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('list.btnDelete')}
                  </Popconfirm>
                ),
              },
              {
                key: 'detail',
                label: (
                  <div onClick={() => handleActions('view', record)}>
                    <EyeOutlined /> {t('list.btnView')}
                  </div>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('list.btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('list.btnAdd')}
          </Button>

          {/* <Button
            type="dashed"
            icon={<ImportOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('form.btnImport')}
          </Button>

          <Dropdown menu={{
            items: [
              {
                key: '1',
                label: <a href="#">Chức năng 1</a>,
              },
              {
                key: '2',
                label: <a href="#">Chức năng 2</a>,
              },
              {
                key: '3',
                label: <a href="#">Chức năng 3</a>,
              },
            ]
          }} trigger={['click']}>
            <Button icon={<EllipsisOutlined />} />
          </Dropdown> */}
        </div>
      </div>
      <CrawlBlogSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></CrawlBlogSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <CrawlBlogModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></CrawlBlogModal>
        )}
        {showModalDetail && (
          <CrawlBlogDetailModal
            showModal={showModalDetail}
            onChange={() => {
              setShowModalDetail(false);
            }}
            blog={blogCurrent}
          ></CrawlBlogDetailModal>
        )}
      </Space>
    </div>
  );
}
export { CrawlBlogList };
