import { Modal } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { CrawlBlog } from '../type';

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  showModal: boolean;
  blog: CrawlBlog;
}

const CrawlBlogDetailModal: React.FC<IndexFormProps> = (props) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { showModal, onChange, blog } = props;

  const handleModal = () => {
    onChange(false);
  };

  return (
    <Modal
      title={t('module')}
      open={showModal}
      onCancel={handleModal}
      footer={false}
      className="form_modal"
      width={'900px'}
    >
      <div className="p-[20px]">
        {blog?.name}

        <div dangerouslySetInnerHTML={{ __html: blog?.content || '' }}></div>
      </div>
    </Modal>
  );
};

export { CrawlBlogDetailModal };
