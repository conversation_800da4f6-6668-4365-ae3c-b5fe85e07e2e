import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, Card, Col, Form, Input, message, Row, Select } from 'antd';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  createAttributeValue,
  getAllAttributes,
  getAttributeOptionsByAttributeId,
  getAttributeValue,
  updateAttributeValue,
} from '../api';
import { MODULE } from '../config';
import {
  ProductAttribute,
  ProductAttributeOption,
  ProductAttributeValue,
} from '../type';

const FormItem = Form.Item;

export const ProductAttributeValueForm: React.FC = () => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();
  const { productId, valueId } = useParams<{
    productId: string;
    valueId: string;
  }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [isNew, setIsNew] = useState<boolean>(true);
  const [attributes, setAttributes] = useState<ProductAttribute[]>([]);
  const [attributeOptions, setAttributeOptions] = useState<
    ProductAttributeOption[]
  >([]);
  const [selectedAttributeId, setSelectedAttributeId] = useState<number | null>(
    null,
  );

  const initForm = {
    product_id: productId ? parseInt(productId) : undefined,
  };

  const fetchAttributes = async () => {
    try {
      const response = await getAllAttributes();
      if (response.status.success) {
        setAttributes(response.data);
      }
    } catch (error) {
      logger('Error fetching attributes', error);
    }
  };

  const fetchAttributeOptions = async (attributeId: number) => {
    try {
      const response = await getAttributeOptionsByAttributeId(
        attributeId.toString(),
      );
      if (response.status.success) {
        setAttributeOptions(response.data);
      }
    } catch (error) {
      logger('Error fetching attribute options', error);
    }
  };

  const fetchAttributeValue = async (id: string) => {
    setLoading(true);
    try {
      const response = await getAttributeValue(id);
      if (response.status.success) {
        form.setFieldsValue(response.data);
        if (response.data.attribute_id) {
          setSelectedAttributeId(response.data.attribute_id);
          fetchAttributeOptions(response.data.attribute_id);
        }
      } else {
        message.error(response.status.message);
      }
    } catch (error) {
      logger('Error fetching attribute value', error);
      message.error(t('submitError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAttributes();
  }, []);

  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(initForm);

    if (valueId && valueId !== 'create') {
      setIsNew(false);
      fetchAttributeValue(valueId);
    } else {
      setIsNew(true);
    }
  }, [valueId, productId]);

  const onFinish = async (values: ProductAttributeValue) => {
    setLoading(true);
    try {
      let response;
      if (isNew) {
        response = await createAttributeValue(values);
        if (response.status.success) {
          message.success(t('attribute.addSuccess'));
          navigate(`/${MODULE}/${productId}/attributes`);
        }
      } else if (valueId) {
        response = await updateAttributeValue(valueId, values);
        if (response.status.success) {
          message.success(t('attribute.updateSuccess'));
          navigate(`/${MODULE}/${productId}/attributes`);
        }
      }

      if (response && !response.status.success) {
        message.error(response.status.message);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('attribute.submitError'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(`/${MODULE}/${productId}/attributes`);
  };

  const handleAttributeChange = (attributeId: number) => {
    setSelectedAttributeId(attributeId);
    fetchAttributeOptions(attributeId);
    form.setFieldValue('option_id', undefined);
    form.setFieldValue('value', '');
  };

  return (
    <div className="p-4">
      <Card
        title={
          <div className="flex items-center">
            <Button
              icon={<ArrowLeftOutlined />}
              type="text"
              onClick={handleCancel}
              style={{ marginRight: '10px' }}
            />
            <span>{isNew ? t('attribute.add') : t('attribute.edit')}</span>
          </div>
        }
        extra={
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => form.submit()}
            loading={loading}
          >
            {t('btnSave')}
          </Button>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={initForm}
        >
          <Row gutter={16}>
            <Col xs={24} lg={12}>
              <FormItem
                label={t('attribute.name')}
                name="attribute_id"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Select
                  placeholder={t('attribute.group')}
                  onChange={handleAttributeChange}
                  options={attributes.map((attr) => ({
                    value: attr.attribute_id,
                    label: attr.name,
                  }))}
                />
              </FormItem>
            </Col>

            {selectedAttributeId && attributeOptions.length > 0 && (
              <Col xs={24} lg={12}>
                <FormItem label={t('attribute.options')} name="option_id">
                  <Select
                    placeholder={t('attribute.options')}
                    options={attributeOptions.map((option) => ({
                      value: option.option_id,
                      label: option.value,
                    }))}
                  />
                </FormItem>
              </Col>
            )}

            <Col xs={24}>
              <FormItem
                label={t('attribute.value')}
                name="value"
                rules={[{ required: true, message: t('pleaseEnterData') }]}
              >
                <Input />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
};
