import {
  AppstoreOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  TagsOutlined,
} from '@ant-design/icons';
import { Button, Dropdown, message, Popconfirm, Space, Table, Tag } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  cleanParams,
} from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_POPUP } from '../config';
import { ProductModal } from '../form/modal';
import useProductStore from '../store';
import { Product, ProductType } from '../type';
import { ProductSearch } from './search';

interface ProductListProps {
  showVariants?: boolean;
  showAttributes?: boolean;
}

function ProductList({
  showVariants = false,
  showAttributes = false,
}: ProductListProps) {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const { productId } = useParams<{ productId: string }>();
  const query = queryString.parse(search);
  const { loading } = useProductStore();

  const [pagination, setPagination] = useState<any>({
    page: 1,
    page_size: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<Product[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...pagination,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.pagination.total);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname, pagination],
  );

  useEffectOnce(() => {
    if (!showVariants && !showAttributes) {
      fetchData();
    }
  });

  useEffect(() => {
    if (!showVariants && !showAttributes && pagination.page > 1) {
      fetchData();
    }
  }, [pagination, showVariants, showAttributes, fetchData]);

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
    fetchData({ ...values });
  };

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.product_id.toString());
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/${record.product_id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE}/create`);
      }
    } else if (action === 'variants') {
      navigate(`/${MODULE}/${record.product_id}/variants`);
    } else if (action === 'attributes') {
      navigate(`/${MODULE}/${record.product_id}/attributes`);
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const handlePagination = (page: number, pageSize: number) => {
    setPagination({ page, page_size: pageSize });
  };

  const getProductTypeTag = (type: string) => {
    let color = 'blue';
    switch (type) {
      case ProductType.CONFIGURABLE:
        color = 'purple';
        break;
      case ProductType.DIGITAL:
        color = 'green';
        break;
      case ProductType.BUNDLE:
        color = 'cyan';
        break;
      default:
        color = 'blue';
    }
    return <Tag color={color}>{t(`productType.${type}`)}</Tag>;
  };

  const columns = [
    {
      title: t('name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('productType'),
      dataIndex: 'product_type',
      key: 'product_type',
      render: (type: string) =>
        type ? getProductTypeTag(type) : getProductTypeTag(ProductType.SIMPLE),
    },
    {
      title: t('basePrice'),
      dataIndex: 'base_price',
      key: 'base_price',
      render: (price: number) => (price ? `$${price.toFixed(2)}` : '-'),
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span>{status ? t(`status.${status}`) : '-'}</span>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (_: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'variants',
                label: (
                  <div onClick={() => handleActions('variants', record)}>
                    <AppstoreOutlined /> {t('btnVariants')}
                  </div>
                ),
              },
              {
                key: 'attributes',
                label: (
                  <div onClick={() => handleActions('attributes', record)}>
                    <TagsOutlined /> {t('btnAttributes')}
                  </div>
                ),
              },
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.product_id.toString())}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  // Nếu đang ở trang variants hoặc attributes, hiển thị component tương ứng
  if (showVariants && productId) {
    return <div>Product Variants List Component (To be implemented)</div>;
  }

  if (showAttributes && productId) {
    return <div>Product Attributes List Component (To be implemented)</div>;
  }

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnAdd')}
          </Button>
        </div>
      </div>
      <ProductSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></ProductSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="product_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={{
            current: pagination.page,
            pageSize: pagination.page_size,
            total: total,
            onChange: handlePagination,
          }}
        />
      </Space>

      {showModal && (
        <ProductModal
          id={idCurrent}
          showModal={showModal}
          onChange={handleModal}
        ></ProductModal>
      )}
    </div>
  );
}

export { ProductList };
