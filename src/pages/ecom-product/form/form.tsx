import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Spin,
  Switch,
} from 'antd';
import _ from 'lodash';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, getProductCategories, updateItem } from '../api';
import {
  ProductAttribute,
  ProductOptions,
  ProductVariant,
  ProductVariants,
} from '../components';
import { MODULE } from '../config';
import { Product, ProductStatus, ProductType } from '../type';

const FormItem = Form.Item;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

interface ProductFormValues extends Product {
  attributes?: ProductAttribute[];
  variants?: ProductVariant[];
}

const ProductForm = forwardRef<unknown, IndexFormProps>(
  ({ onChange, id }, ref) => {
    const { t } = useTranslation(MODULE);
    const logger = ConsoleService.register(MODULE);
    const [form] = Form.useForm();
    const [isNew, setIsNew] = useState<boolean>(false);
    const [item, setItem] = useState<ProductFormValues>();
    const [categories, setCategories] = useState<any[]>([]);
    const [productType, setProductType] = useState<string>(ProductType.SIMPLE);
    const [manageVariantPricing, setManageVariantPricing] =
      useState<boolean>(false);
    const [variantsModalVisible, setVariantsModalVisible] =
      useState<boolean>(false);
    const [productAttributes, setProductAttributes] = useState<
      ProductAttribute[]
    >([]);
    const [productVariants, setProductVariants] = useState<ProductVariant[]>(
      [],
    );
    const [loading, setLoading] = useState<boolean>(false);

    // Use useMemo to prevent unnecessary re-renders
    const initForm = useMemo(
      () => ({
        status: ProductStatus.DRAFT,
        product_type: ProductType.SIMPLE,
        is_taxable: false,
        is_virtual: false,
        is_downloadable: false,
        attributes: [],
        variants: [],
      }),
      [],
    );

    // Use useCallback to prevent unnecessary re-renders
    const fetchCategories = useCallback(async () => {
      try {
        setLoading(true);
        const res = await getProductCategories();
        if (res.status.success) {
          setCategories(res.data);
        }
      } catch (error) {
        logger('Error fetching categories', error);
      } finally {
        setLoading(false);
      }
    }, [logger]);

    // Use useCallback to prevent unnecessary re-renders
    const getItemData = useCallback(
      async (_id: string) => {
        try {
          setLoading(true);
          const res = await getItem(_id);
          if (res.status.success) {
            const productData = res.data;
            // Nếu có attributes trong response, set vào state
            if (productData.attributes) {
              setProductAttributes(productData.attributes);
              setManageVariantPricing(!!productData.manage_variant_pricing);
            }
            // Nếu có variants trong response, set vào state
            if (productData.variants) {
              setProductVariants(productData.variants);
            }
            setItem(productData);
            form.setFieldsValue(productData);
            if (productData.product_type) {
              setProductType(productData.product_type);
            }
          } else {
            message.error(res.status.message);
          }
        } catch (error) {
          logger('Error fetching product data', error);
          message.error(t('form.submitError'));
        } finally {
          setLoading(false);
        }
      },
      [form, logger, t],
    );

    useEffect(() => {
      fetchCategories();
    }, [fetchCategories]);

    useEffect(() => {
      logger(id);
      form.resetFields();
      if (['create', undefined].includes(id)) {
        setIsNew(true);
        form.setFieldsValue(initForm);
      } else if (id) {
        setIsNew(false);
        getItemData(id);
      }
    }, [id, form, getItemData, initForm, logger]);

    const onFinish = async (values: ProductFormValues) => {
      try {
        setLoading(true);
        // Thêm manageVariantPricing và variants vào values
        const productData = {
          ...values,
          manage_variant_pricing: manageVariantPricing,
          variants: productVariants,
        };

        let res;
        if (isNew) {
          res = await createItem(productData);
          if (res.status.success) {
            message.success(t('form.addSuccess'));
          }
        } else {
          res = await updateItem(id!, productData);
          if (res.status.success) {
            message.success(t('form.updateSuccess'));
          }
        }
        if (!res.status.success) {
          message.error(res.status.message);
        } else {
          setItem(res.data);
          form.resetFields();
          onChange(true);
        }
      } catch (error) {
        logger('Error submitting form', error);
        message.error(
          _.get(error, 'response.data.message.0') || t('form.submitError'),
        );
      } finally {
        setLoading(false);
      }
    };

    const handleValuesChange = (changedValues: any, allValues: any) => {
      logger(changedValues);
      logger(allValues);

      // Update product type state when it changes
      if (changedValues.product_type) {
        setProductType(changedValues.product_type);
      }

      // Update attributes state when attributes change
      if (changedValues.attributes) {
        setProductAttributes(changedValues.attributes);
      }
    };

    const handleAttributesChange = (attributes: ProductAttribute[]) => {
      setProductAttributes(attributes);
      form.setFieldsValue({ attributes });
    };

    const handleVariantsChange = (variants: ProductVariant[]) => {
      setProductVariants(variants);
    };

    const showVariantsModal = () => {
      setVariantsModalVisible(true);
    };

    const hideVariantsModal = () => {
      setVariantsModalVisible(false);
    };

    const handleVariantsApply = () => {
      // form.setFieldsValue({ variants: productVariants });
      setVariantsModalVisible(false);
    };

    useImperativeHandle(ref, () => ({
      submitForm: () => form.submit(),
    }));

    // Chỉ hiển thị ProductOptions và nút quản lý biến thể nếu sản phẩm là loại CONFIGURABLE
    const showProductOptions = productType === ProductType.CONFIGURABLE;

    // Chỉ hiển thị nút quản lý biến thể khi có ít nhất một thuộc tính với giá trị
    const canManageVariants =
      showProductOptions &&
      productAttributes &&
      productAttributes.length > 0 &&
      productAttributes.every((attr) => attr.values && attr.values.length > 0);

    return (
      <Spin spinning={loading} tip={t('form.loading')}>
        <Form
          form={form}
          name="form"
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
          initialValues={initForm}
          onValuesChange={handleValuesChange}
        >
          <div className="form_content">
            <Row gutter={16}>
              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.name')}
                  name="name"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <Input />
                </FormItem>
              </Col>
              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.slug')}
                  name="slug"
                  tooltip={t('form.slugTooltip')}
                >
                  <Input />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('form.category')} name="category_id">
                  <Select
                    allowClear
                    placeholder={t('form.selectCategory')}
                    options={categories.map((category) => ({
                      value: category.category_id,
                      label: category.name,
                    }))}
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.productType')}
                  name="product_type"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <Select
                    options={Object.entries(ProductType).map(([_, label]) => ({
                      value: label,
                      label: t(`productType.${label}`),
                    }))}
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('form.basePrice')} name="base_price">
                  <InputNumber
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    prefix="$"
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('form.costPrice')} name="cost_price">
                  <InputNumber
                    style={{ width: '100%' }}
                    min={0}
                    precision={2}
                    prefix="$"
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem
                  label={t('form.status')}
                  name="status"
                  rules={[
                    { required: true, message: t('form.pleaseEnterData') },
                  ]}
                >
                  <Select
                    options={Object.entries(ProductStatus).map(
                      ([_, label]) => ({
                        value: label,
                        label: t(`status.${label}`),
                      }),
                    )}
                  />
                </FormItem>
              </Col>

              <Col xs={24} lg={12}>
                <FormItem label={t('form.imageUrl')} name="image_url">
                  <Input />
                </FormItem>
              </Col>

              <Col xs={24} lg={8}>
                <FormItem
                  label={t('form.isTaxable')}
                  name="is_taxable"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>

              <Col xs={24} lg={8}>
                <FormItem
                  label={t('form.isVirtual')}
                  name="is_virtual"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>

              <Col xs={24} lg={8}>
                <FormItem
                  label={t('form.isDownloadable')}
                  name="is_downloadable"
                  valuePropName="checked"
                >
                  <Switch />
                </FormItem>
              </Col>

              <Col xs={24}>
                <FormItem label={t('form.description')} name="description">
                  <Input.TextArea rows={4} />
                </FormItem>
              </Col>

              <Col xs={24}>
                <FormItem label={t('form.content')} name="content">
                  <Input.TextArea rows={8} />
                </FormItem>
              </Col>
            </Row>

            {showProductOptions && (
              <>
                <Divider orientation="left">
                  {t('productOptions.sectionTitle')}
                </Divider>
                <FormItem name="attributes" noStyle>
                  <ProductOptions
                    manageVariantPricing={manageVariantPricing}
                    onManageVariantPricingChange={(checked) =>
                      setManageVariantPricing(checked)
                    }
                    onChange={handleAttributesChange}
                  />
                </FormItem>

                {canManageVariants && (
                  <div style={{ marginTop: 16, textAlign: 'right' }}>
                    <Button type="primary" onClick={showVariantsModal}>
                      {t('productVariants.manageVariants')}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Modal quản lý biến thể sản phẩm */}
          <ProductVariants
            visible={variantsModalVisible}
            onCancel={hideVariantsModal}
            onApply={handleVariantsApply}
            attributes={productAttributes}
            value={productVariants}
            onChange={handleVariantsChange}
            basePrice={form.getFieldValue('base_price') || 0}
          />
        </Form>
      </Spin>
    );
  },
);
ProductForm.displayName = 'ProductForm';

export { ProductForm };
