import { CheckOutlined, ReloadOutlined, SaveOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { ProductForm as BaseProductForm } from './form';

const ProductForm: React.FC = () => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { id } = useParams();
  const formRef = useRef<any>();

  const handleBack = () => {
    navigate(`/${MODULE}`);
  };

  const handleSave = () => {
    formRef.current?.submitForm();
  };

  const handleSaveAndContinue = () => {
    formRef.current?.submitForm();
  };

  const handleChange = (reload: boolean) => {
    if (reload) {
      navigate(`/${MODULE}`);
    }
  };

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold">
          {id === 'create' ? t('form.add') : t('form.edit')}
        </div>
        <div className="flex gap-4">
          <Button type="default" icon={<ReloadOutlined />} onClick={handleBack}>
            {t('form.btnCancel')}
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            {t('form.btnSave')}
          </Button>
          <Button
            type="primary"
            icon={<CheckOutlined />}
            onClick={handleSaveAndContinue}
          >
            {t('form.btnSaveAndContinue')}
          </Button>
        </div>
      </div>
      <div className="bg-white p-4">
        <BaseProductForm
          ref={formRef}
          onChange={handleChange}
          id={id}
        ></BaseProductForm>
      </div>
    </div>
  );
};

export { ProductForm };
