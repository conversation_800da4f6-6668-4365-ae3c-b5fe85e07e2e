import { SaveOutlined } from '@ant-design/icons';
import { Button, Modal } from 'antd';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { ProductForm } from './form';

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  showModal: boolean;
  id?: string;
}

const ProductModal: React.FC<IndexFormProps> = (props) => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const formRef = useRef<any>();

  const handleCancel = () => {
    props.onChange(false);
  };

  const handleOk = () => {
    formRef.current?.submitForm();
  };

  return (
    <Modal
      title={props.id ? t('form.edit') : t('form.add')}
      open={props.showModal}
      onCancel={handleCancel}
      width={1000}
      footer={[
        <Button key="back" onClick={handleCancel}>
          {t('form.btnCancel')}
        </Button>,
        <Button
          key="submit"
          type="primary"
          icon={<SaveOutlined />}
          onClick={handleOk}
        >
          {t('form.btnSave')}
        </Button>,
      ]}
    >
      <ProductForm
        ref={formRef}
        onChange={props.onChange}
        id={props.id}
      ></ProductForm>
    </Modal>
  );
};

export { ProductModal };
