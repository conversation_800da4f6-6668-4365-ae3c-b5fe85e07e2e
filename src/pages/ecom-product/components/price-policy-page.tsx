import { But<PERSON>, Card } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import { MODULE } from '../config';
import { PricePolicyForm } from './price-policy-form';

export function PricePolicyPage() {
  const { t } = useTranslation(MODULE);
  const params = useParams<{ id: string; productId: string }>();
  const navigate = useNavigateTenant();
  const formRef = useRef<{ submitForm: () => void }>();
  const [changed, setChanged] = useState(false);

  useEffect(() => {
    document.title = t('pricePolicy.form.title');
  }, [t]);

  const handleBackClick = () => {
    navigate(`/ecom/products/${params.productId}/price-policies`);
  };

  const handleSubmit = () => {
    formRef.current?.submitForm();
  };

  const handleChange = (reload: boolean) => {
    setChanged(reload);
    if (reload) {
      setTimeout(() => {
        handleBackClick();
      }, 1000);
    }
  };

  const renderHeader = () => {
    return (
      <div className="header_container">
        <div className="header_title">
          {params.id && params.id !== 'create'
            ? t('pricePolicy.form.titleEdit')
            : t('pricePolicy.form.titleCreate')}
        </div>
        <div>
          <Button onClick={handleBackClick} style={{ marginRight: 8 }}>
            {t('common.back')}
          </Button>
          <Button type="primary" onClick={handleSubmit}>
            {t('common.save')}
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="main_container">
      {renderHeader()}
      <div className="content_container">
        <Card>
          <PricePolicyForm
            ref={formRef as any}
            id={params.id}
            productId={params.productId}
            onChange={handleChange}
          />
        </Card>
      </div>
    </div>
  );
}
