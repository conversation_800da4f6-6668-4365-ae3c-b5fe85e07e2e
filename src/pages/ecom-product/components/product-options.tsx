import { PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Divider,
  Input,
  Select,
  Space,
  Switch,
  Tag,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';

const { Text, Title } = Typography;

export interface ProductAttribute {
  name: string;
  values: string[];
  is_configurable?: boolean;
  is_filterable?: boolean;
}

interface ProductOptionsProps {
  value?: ProductAttribute[];
  onChange?: (value: ProductAttribute[]) => void;
  manageVariantPricing: boolean;
  onManageVariantPricingChange: (checked: boolean) => void;
}

const ProductOptions: React.FC<ProductOptionsProps> = ({
  value = [],
  onChange,
  manageVariantPricing,
  onManageVariantPricingChange,
}) => {
  const { t } = useTranslation(MODULE);
  const [attributes, setAttributes] = useState<ProductAttribute[]>(value);
  const [newAttributeName, setNewAttributeName] = useState<string>('');

  useEffect(() => {
    setAttributes(value);
  }, [value]);

  const handleAttributeChange = (
    index: number,
    updatedAttribute: Partial<ProductAttribute>,
  ) => {
    const newAttributes = [...attributes];
    newAttributes[index] = { ...newAttributes[index], ...updatedAttribute };
    setAttributes(newAttributes);
    onChange?.(newAttributes);
  };

  const handleAddValue = (attributeIndex: number, newValue: string) => {
    if (!newValue.trim()) return;

    const newAttributes = [...attributes];
    if (!newAttributes[attributeIndex].values.includes(newValue)) {
      newAttributes[attributeIndex].values = [
        ...newAttributes[attributeIndex].values,
        newValue,
      ];
      setAttributes(newAttributes);
      onChange?.(newAttributes);
    }
  };

  const handleRemoveValue = (attributeIndex: number, valueIndex: number) => {
    const newAttributes = [...attributes];
    newAttributes[attributeIndex].values = newAttributes[
      attributeIndex
    ].values.filter((_, idx) => idx !== valueIndex);
    setAttributes(newAttributes);
    onChange?.(newAttributes);
  };

  const handleAddAttribute = () => {
    if (!newAttributeName.trim()) return;

    if (!attributes.some((attr) => attr.name === newAttributeName)) {
      const newAttributes = [
        ...attributes,
        {
          name: newAttributeName,
          values: [],
          is_configurable: true,
          is_filterable: true,
        },
      ];
      setAttributes(newAttributes);
      onChange?.(newAttributes);
      setNewAttributeName('');
    }
  };

  const styles = {
    productOptionsCard: {
      marginBottom: '16px',
    },
    optionsHeader: {
      marginBottom: '16px',
    },
    attributeContainer: {
      display: 'flex',
      flexDirection: 'column' as const,
      marginBottom: '16px',
      padding: '12px',
      border: '1px solid #f0f0f0',
      borderRadius: '4px',
    },
    attributeName: {
      marginBottom: '8px',
    },
    attributeValues: {
      display: 'flex',
      flexWrap: 'wrap' as const,
      gap: '8px',
      alignItems: 'center',
    },
    addOptionContainer: {
      margin: '16px 0',
      display: 'flex',
      alignItems: 'center',
    },
    managePricingToggle: {
      marginTop: '24px',
      padding: '16px',
      backgroundColor: '#f9f9f9',
      borderRadius: '4px',
    },
    toggleLabel: {
      marginLeft: '8px',
      fontWeight: 500,
    },
    toggleDescription: {
      display: 'block',
      marginTop: '4px',
      marginLeft: '36px',
    },
  };

  return (
    <Card style={styles.productOptionsCard}>
      <div style={styles.optionsHeader}>
        <Title level={4}>{t('productOptions.title')}</Title>
        <Text type="secondary">{t('productOptions.subtitle')}</Text>
      </div>

      {attributes.map((attribute, attributeIndex) => (
        <div key={attributeIndex} style={styles.attributeContainer}>
          <div style={styles.attributeName}>
            <Text strong>{attribute.name}</Text>
          </div>
          <div style={styles.attributeValues}>
            {attribute.values.map((value, valueIndex) => (
              <Tag
                key={valueIndex}
                closable
                onClose={() => handleRemoveValue(attributeIndex, valueIndex)}
              >
                {value}
              </Tag>
            ))}
            <Select
              style={{ width: 'auto', minWidth: '150px' }}
              placeholder={`${t('productOptions.enterValue')}...`}
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <Divider style={{ margin: '8px 0' }} />
                  <Space style={{ padding: '0 8px 4px' }}>
                    <Input
                      placeholder={t('productOptions.addValue')}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleAddValue(attributeIndex, e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <Button
                      type="text"
                      icon={<PlusOutlined />}
                      onClick={(e) => {
                        const input = (e.target as HTMLElement)
                          .closest('.ant-space')
                          ?.querySelector('input');
                        if (input) {
                          handleAddValue(attributeIndex, input.value);
                          input.value = '';
                        }
                      }}
                    >
                      {t('productOptions.add')}
                    </Button>
                  </Space>
                </>
              )}
              options={attribute.values.map((value) => ({
                label: value,
                value,
              }))}
              onChange={(newValue: string) =>
                handleAddValue(attributeIndex, newValue)
              }
            />
          </div>
        </div>
      ))}

      <div style={styles.addOptionContainer}>
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={() => handleAddAttribute()}
          style={{ marginRight: '8px' }}
        >
          {t('productOptions.addOption')}
        </Button>
        <Input
          placeholder={t('productOptions.optionName')}
          value={newAttributeName}
          onChange={(e) => setNewAttributeName(e.target.value)}
          onPressEnter={handleAddAttribute}
          style={{ width: '200px' }}
        />
      </div>

      <div style={styles.managePricingToggle}>
        <Switch
          checked={manageVariantPricing}
          onChange={onManageVariantPricingChange}
        />
        <span style={styles.toggleLabel}>
          {t('productOptions.manageVariantPricing')}
        </span>
        <Text type="secondary" style={styles.toggleDescription}>
          {t('productOptions.pricingDescription')}
        </Text>
      </div>
    </Card>
  );
};

export default ProductOptions;
