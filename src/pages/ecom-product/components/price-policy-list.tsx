import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Popconfirm, Space, Table } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { deletePricePolicy, getPricePoliciesByProduct } from '../api';
import { MODULE } from '../config';
import { PricePolicyType, ProductPricePolicy } from '../type';
import { PricePolicySearch } from './price-policy-search';

interface PricePolicyListProps {
  productId: string;
}

export function PricePolicyList({ productId }: PricePolicyListProps) {
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const logger = ConsoleService.register(MODULE);
  const [items, setItems] = useState<ProductPricePolicy[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [filter, setFilter] = useState<any>({});

  // Tạo đường dẫn cho các action
  const PATH_CREATE = `/ecom/products/${productId}/price-policies/create`;
  const PATH_EDIT = `/ecom/products/${productId}/price-policies`;

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await getPricePoliciesByProduct(productId, filter);
      if (response.status.success) {
        setItems(response.data);
      }
    } catch (error) {
      logger('Fetch data error', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filter, productId]);

  const handleSearch = (values: any) => {
    setFilter(values);
  };

  const handleAddClick = () => {
    navigate(PATH_CREATE);
  };

  const handleEditClick = (id: number) => {
    navigate(`${PATH_EDIT}/${id}`);
  };

  const handleDeleteClick = async (id: number) => {
    try {
      const response = await deletePricePolicy(id.toString());
      if (response.status.success) {
        fetchData();
      }
    } catch (error) {
      logger('Delete error', error);
    }
  };

  const renderHeader = () => {
    return (
      <div className="header_container">
        <div className="header_title">{t('pricePolicy.list.title')}</div>
        <div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddClick}
          >
            {t('common.add')}
          </Button>
        </div>
      </div>
    );
  };

  const columns = [
    {
      title: t('pricePolicy.list.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('pricePolicy.list.type'),
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => t(`pricePolicy.type.${text}`),
    },
    {
      title: t('pricePolicy.list.value'),
      dataIndex: 'value',
      key: 'value',
      render: (text: number, record: ProductPricePolicy) => {
        const suffix = record.type === PricePolicyType.PERCENTAGE ? '%' : '$';
        return `${text}${suffix}`;
      },
    },
    {
      title: t('pricePolicy.list.minQuantity'),
      dataIndex: 'min_quantity',
      key: 'min_quantity',
    },
    {
      title: t('pricePolicy.list.startDate'),
      dataIndex: 'start_date',
      key: 'start_date',
    },
    {
      title: t('pricePolicy.list.endDate'),
      dataIndex: 'end_date',
      key: 'end_date',
    },
    {
      title: t('pricePolicy.list.status'),
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => t(`pricePolicy.status.${text}`),
    },
    {
      title: t('pricePolicy.list.action'),
      key: 'action',
      render: (text: any, record: ProductPricePolicy) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => handleEditClick(record.price_policy_id)}
          />
          <Popconfirm
            title={t('common.confirmDelete')}
            onConfirm={() => handleDeleteClick(record.price_policy_id)}
            okText={t('common.yes')}
            cancelText={t('common.no')}
          >
            <Button type="primary" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="price_policy_container">
      {renderHeader()}
      <div className="content_container">
        <Card className="card_search">
          <PricePolicySearch onSearch={handleSearch} />
        </Card>
        <Card>
          <Table
            rowKey="price_policy_id"
            columns={columns}
            dataSource={items}
            loading={loading}
          />
        </Card>
      </div>
    </div>
  );
}
