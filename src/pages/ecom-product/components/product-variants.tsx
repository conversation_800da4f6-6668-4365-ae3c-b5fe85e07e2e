import { EyeOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Space,
  Switch,
  Table,
  Tooltip,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';
import { ProductAttribute } from './product-options';

const { Text, Title } = Typography;
const { Option } = Select;

export interface ProductVariant {
  id?: string;
  combination: string;
  price_adjustment: number;
  price: number;
  cost_price: number;
  sku: string;
  stock_status: string;
  weight: number;
  weight_unit: string;
  is_visible: boolean;
  attributes: Record<string, string>;
}

interface ProductVariantsProps {
  value?: ProductVariant[];
  attributes?: ProductAttribute[];
  basePrice?: number;
  onChange?: (variants: ProductVariant[]) => void;
  visible: boolean;
  onCancel: () => void;
  onApply: () => void;
}

const generateCombinations = (
  attributes: ProductAttribute[],
): Array<{ combination: string; attributes: Record<string, string> }> => {
  if (!attributes || attributes.length === 0) return [];

  const allOptions: string[][] = attributes.map((attr) => attr.values || []);
  const attributeNames = attributes.map((attr) => attr.name);

  // Helper function for cartesian product
  const cartesian = (arr: string[][]): string[][] => {
    return arr.reduce<string[][]>(
      (acc, val) => {
        return acc
          .map((accItem) => val.map((valItem) => [...accItem, valItem]))
          .flat();
      },
      [[]],
    );
  };

  const combinations = cartesian(allOptions);

  return combinations.map((combo) => {
    const attrs: Record<string, string> = {};
    const parts: string[] = [];

    combo.forEach((value, index) => {
      if (index < attributeNames.length) {
        attrs[attributeNames[index]] = value;
        parts.push(`${value}`);
      }
    });

    return {
      combination: parts.join(' | '),
      attributes: attrs,
    };
  });
};

const ProductVariants: React.FC<ProductVariantsProps> = ({
  value = [],
  attributes = [],
  basePrice = 0,
  onChange,
  visible,
  onCancel,
  onApply,
}) => {
  const { t } = useTranslation(MODULE);
  const [variants, setVariants] = useState<ProductVariant[]>(value);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [trackInventory, setTrackInventory] = useState<boolean>(true);
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && attributes && attributes.length > 0) {
      generateVariants();
    }
  }, [visible, attributes]);

  const generateVariants = () => {
    const combinations = generateCombinations(attributes);

    // Preserve existing variant data if it exists
    const newVariants = combinations.map((combo) => {
      const existingVariant = variants.find(
        (v) => v.combination === combo.combination,
      );

      if (existingVariant) {
        return existingVariant;
      }

      // Create new variant
      return {
        combination: combo.combination,
        price_adjustment: 0,
        price: basePrice,
        cost_price: 0,
        sku: '',
        stock_status: 'Có trong kho',
        weight: 0,
        weight_unit: 'kg',
        is_visible: true,
        attributes: combo.attributes,
      };
    });

    setVariants(newVariants);
    onChange?.(newVariants);
  };

  const handleSelectAll = (e: any) => {
    if (e.target.checked) {
      const allKeys = variants.map((_, index) => index);
      setSelectedRowKeys(allKeys);
    } else {
      setSelectedRowKeys([]);
    }
  };

  const handleTrackInventoryChange = (checked: boolean) => {
    setTrackInventory(checked);
  };

  const handleVariantChange = (
    index: number,
    field: keyof ProductVariant,
    value: any,
  ) => {
    const newVariants = [...variants];
    newVariants[index] = { ...newVariants[index], [field]: value };

    // Update price based on base price and adjustment if field is price_adjustment
    if (field === 'price_adjustment') {
      newVariants[index].price = basePrice + value;
    }

    setVariants(newVariants);
    onChange?.(newVariants);
  };

  const columns = [
    {
      title: t('productVariants.variant'),
      dataIndex: 'combination',
      key: 'combination',
      width: 180,
    },
    {
      title: (
        <span>
          {t('productVariants.priceAdjustment')}
          <Tooltip title={t('productVariants.priceAdjustmentTooltip')}>
            <QuestionCircleOutlined style={{ marginLeft: 8 }} />
          </Tooltip>
        </span>
      ),
      dataIndex: 'price_adjustment',
      key: 'price_adjustment',
      width: 150,
      render: (text: number, record: ProductVariant, index: number) => (
        <InputNumber
          prefix="đ"
          style={{ width: '100%' }}
          value={text}
          onChange={(value) =>
            handleVariantChange(index, 'price_adjustment', value)
          }
        />
      ),
    },
    {
      title: t('productVariants.price'),
      dataIndex: 'price',
      key: 'price',
      width: 150,
      render: (text: number, record: ProductVariant) => (
        <Text>{text.toLocaleString()}₫</Text>
      ),
    },
    {
      title: t('productVariants.costPrice'),
      dataIndex: 'cost_price',
      key: 'cost_price',
      width: 150,
      render: (text: number, record: ProductVariant, index: number) => (
        <InputNumber
          prefix="đ"
          style={{ width: '100%' }}
          value={text}
          onChange={(value) => handleVariantChange(index, 'cost_price', value)}
        />
      ),
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      key: 'sku',
      width: 150,
      render: (text: string, record: ProductVariant, index: number) => (
        <Input
          value={text}
          onChange={(e) => handleVariantChange(index, 'sku', e.target.value)}
        />
      ),
    },
    {
      title: t('productVariants.stockStatus'),
      dataIndex: 'stock_status',
      key: 'stock_status',
      width: 150,
      render: (text: string, record: ProductVariant, index: number) => (
        <Select
          value={text}
          style={{ width: '100%' }}
          onChange={(value) =>
            handleVariantChange(index, 'stock_status', value)
          }
        >
          <Option value="Có trong kho">{t('productVariants.inStock')}</Option>
          <Option value="Hết hàng">{t('productVariants.outOfStock')}</Option>
          <Option value="Đặt trước">{t('productVariants.preOrder')}</Option>
        </Select>
      ),
    },
    {
      title: t('productVariants.weight'),
      dataIndex: 'weight',
      key: 'weight',
      width: 150,
      render: (text: number, record: ProductVariant, index: number) => (
        <Space>
          <InputNumber
            style={{ width: 100 }}
            value={text}
            onChange={(value) => handleVariantChange(index, 'weight', value)}
          />
          <Text>kg</Text>
        </Space>
      ),
    },
    {
      title: t('productVariants.visibility'),
      dataIndex: 'is_visible',
      key: 'is_visible',
      width: 80,
      render: (visible: boolean, record: ProductVariant, index: number) => (
        <Button
          type="text"
          icon={
            <EyeOutlined style={{ color: visible ? '#1890ff' : '#d9d9d9' }} />
          }
          onClick={() => handleVariantChange(index, 'is_visible', !visible)}
        />
      ),
    },
  ];

  return (
    <Modal
      title={t('productVariants.title')}
      open={visible}
      width={1000}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t('productVariants.cancel')}
        </Button>,
        <Button key="apply" type="primary" onClick={onApply}>
          {t('productVariants.apply')}
        </Button>,
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">{t('productVariants.description')}</Text>
      </div>

      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: 16,
        }}
      >
        <Checkbox onChange={handleSelectAll}>
          {t('productVariants.selectAll')} ({variants.length})
        </Checkbox>
        <div>
          <Switch
            checked={trackInventory}
            onChange={handleTrackInventoryChange}
          />
          <Text style={{ marginLeft: 8 }}>
            {t('productVariants.trackInventory')}
          </Text>
          <Tooltip title={t('productVariants.trackInventoryTooltip')}>
            <QuestionCircleOutlined style={{ marginLeft: 8 }} />
          </Tooltip>
        </div>
      </div>

      <Table
        rowKey={(record, index) => String(index)}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        columns={columns}
        dataSource={variants}
        pagination={false}
        scroll={{ y: 400 }}
        size="middle"
      />
    </Modal>
  );
};

export default ProductVariants;
