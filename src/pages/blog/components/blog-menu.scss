.blog-menu-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px 0;
  background-color: #fafafa;
  user-select: none;
}

.vertical-menu {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.menu-toggle,
.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: none;
  border: none;
  padding: 8px;
  width: 100%;
  transition: all 0.3s;
  border-radius: 4px;

  &:hover {
    background-color: #f0f0f0;
  }

  &:focus {
    outline: none;
  }
}

.menu-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.menu-label {
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.drawer-menu {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.drawer-menu-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  background: none;
  border: none;
  transition: all 0.3s;
  text-align: left;

  &:hover {
    background-color: #f0f0f0;
  }

  .drawer-menu-label {
    margin-left: 12px;
    font-size: 14px;
  }
}

// Sidebar styles
.blog-sidebar {
  .ant-collapse {
    border-radius: 4px;
    overflow: hidden;
  }

  .ant-collapse-header {
    font-weight: 500;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .menu-label {
    display: none;
  }
}
