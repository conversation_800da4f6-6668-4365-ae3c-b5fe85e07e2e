.blog-note-drawer {
  .ant-drawer-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
  }

  .ant-drawer-title {
    font-size: 14px;
    font-weight: 500;
  }

  .ant-drawer-body {
    padding: 16px !important;
    height: 100%;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
}

.note-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .note-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;

    .note-author {
      font-size: 13px;
      font-weight: 500;
      color: #262626;
    }

    .note-actions {
      display: flex;
      align-items: center;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.2s ease;

      .ant-btn {
        padding: 2px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        box-shadow: none;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }

  &:hover .note-actions {
    opacity: 1;
  }

  .note-time {
    font-size: 11px;
    color: #8c8c8c;
    margin-bottom: 8px;
    display: block;
  }

  .note-content {
    font-size: 13px;
    line-height: 1.4;
    color: #262626;
    word-break: break-word;
    white-space: pre-wrap;
  }

  .note-edit-textarea {
    font-size: 13px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    resize: none;

    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.note-filter {
  margin-bottom: 16px;

  .ant-btn-link {
    padding: 0;
    height: auto;
    font-size: 12px;
    color: #1890ff;

    &:hover {
      color: #40a9ff;
    }

    .anticon {
      font-size: 10px;
      margin-left: 4px;
    }
  }
}

.note-input-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;

  .note-input {
    font-size: 13px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    resize: none;

    &:focus {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: #bfbfbf;
    }
  }

  .note-input-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
    gap: 8px;

    .ant-btn {
      height: 28px;
      padding: 0 12px;
      font-size: 12px;
      border-radius: 4px;
    }

    .ant-btn-default {
      border-color: #d9d9d9;
      color: #595959;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }

    .ant-btn-primary {
      background-color: #1890ff;
      border-color: #1890ff;

      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }
}

.note-avatar {
  flex-shrink: 0;
  margin-right: 8px;

  .ant-avatar {
    background-color: #1890ff;
    font-size: 12px;
    font-weight: 500;
  }
}

.note-content-wrapper {
  flex: 1;
  min-width: 0;
}

.note-list {
  .ant-list-item {
    padding: 0 !important;
    border: none !important;
  }
}

// Empty state
.note-empty {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;

  .note-empty-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .note-empty-text {
    font-size: 14px;
    color: #8c8c8c;
  }
}

// Responsive
@media (max-width: 480px) {
  .blog-note-drawer {
    .ant-drawer {
      width: 100% !important;
    }

    .ant-drawer-body {
      padding: 12px !important;
    }
  }

  .note-item {
    .note-header {
      .note-author {
        font-size: 12px;
      }
    }

    .note-content {
      font-size: 12px;
    }

    .note-time {
      font-size: 10px;
    }
  }

  .note-input-section {
    .note-input {
      font-size: 12px;
    }
  }
}
