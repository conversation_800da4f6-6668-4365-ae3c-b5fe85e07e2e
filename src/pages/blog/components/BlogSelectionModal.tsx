import {
  CheckOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
  Space,
  Table,
  Tag,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import ConsoleService from '../../../services/console.service';
import { getItems } from '../api';
import { MODULE } from '../config';
import { Blog, BLOG_STATUS, BLOG_STATUS_LABELS } from '../type';

const { Option } = Select;

interface BlogSelectionModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (selectedBlogs: Blog[]) => void;
  excludeIds?: string[]; // IDs of blogs to exclude from selection
  title?: string;
  confirmText?: string;
  showContinueButton?: boolean; // Show "Add and Continue" button
}

const BlogSelectionModal: React.FC<BlogSelectionModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  excludeIds = [],
  title = 'Chọn bài viết',
  confirmText = 'Thêm bài viết',
  showContinueButton = true,
}) => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const [form] = Form.useForm();

  // Pagination
  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });

  // State
  const [loading, setLoading] = useState(false);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedBlogs, setSelectedBlogs] = useState<Blog[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState<any>({});

  // Fetch blogs data
  const fetchBlogs = useCallback(
    async (payload?: any) => {
      setLoading(true);
      try {
        const params = {
          ...filters,
          ...payload,
          //status: 'published', // Only show published blogs
          limit: 10,
        };

        // Exclude already selected blogs
        if (excludeIds.length > 0) {
          params.exclude_ids = excludeIds;
        }

        logger('[fetchBlogs params]', params);
        const response = await getItems(params);

        if (response.status.success) {
          setBlogs(response.data);
          setTotal(response.meta.total || 0);
          setNextCursor(response.meta.nextCursor);
        } else {
          message.error(
            response.status.message || 'Không thể tải danh sách bài viết',
          );
        }
      } catch (error) {
        logger('Error fetching blogs:', error);
        message.error('Lỗi khi tải danh sách bài viết');
      } finally {
        setLoading(false);
      }
    },
    [filters, excludeIds, setNextCursor],
  );

  // Load data when modal opens
  //   useEffect(() => {
  //     if (visible) {
  //       fetchBlogs();
  //     }
  //   }, [visible, fetchBlogs]);

  // Handle search and filters
  const handleSearch = (values: any) => {
    logger('[search values]', values);
    setFilters(values);
    fetchBlogs(values);
  };

  const handleReset = () => {
    form.resetFields();
    setFilters({});
    fetchBlogs({});
  };

  // Handle row selection
  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    selectedRows: Blog[],
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedBlogs(selectedRows.filter((blog) => blog.post_id));
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: Blog) => ({
      disabled: excludeIds.includes(record.post_id?.toString() || ''),
    }),
  };

  // Handle confirm actions
  const handleConfirm = () => {
    if (selectedBlogs.length === 0) {
      message.warning('Vui lòng chọn ít nhất một bài viết');
      return;
    }
    onConfirm(selectedBlogs);
    handleCancel();
  };

  const handleConfirmAndContinue = () => {
    if (selectedBlogs.length === 0) {
      message.warning('Vui lòng chọn ít nhất một bài viết');
      return;
    }
    onConfirm(selectedBlogs);
    // Reset selection but keep modal open
    setSelectedRowKeys([]);
    setSelectedBlogs([]);
    message.success(`Đã thêm ${selectedBlogs.length} bài viết`);
  };

  const handleCancel = () => {
    setSelectedRowKeys([]);
    setSelectedBlogs([]);
    setFilters({});
    form.resetFields();
    onCancel();
  };

  // Table columns
  const columns: ColumnsType<Blog> = [
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text: string, record: Blog) => (
        <div>
          <div className="font-medium">{text}</div>
          {record.description && (
            <div className="text-sm text-gray-500 mt-1">
              {record.description.substring(0, 100)}...
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Danh mục',
      dataIndex: 'categories',
      key: 'categories',
      width: 200,
      render: (categories: any[]) => (
        <div>
          {categories?.map((category) => (
            <Tag key={category.id} color="blue">
              {category.name}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={status === BLOG_STATUS.PUBLISHED ? 'green' : 'orange'}>
          {BLOG_STATUS_LABELS[status as BLOG_STATUS] || status}
        </Tag>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
    },
  ];

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      width={1000}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        showContinueButton && (
          <Button
            key="continue"
            type="default"
            icon={<PlusOutlined />}
            onClick={handleConfirmAndContinue}
            disabled={selectedBlogs.length === 0}
          >
            Thêm và tiếp tục
          </Button>
        ),
        <Button
          key="confirm"
          type="primary"
          icon={<CheckOutlined />}
          onClick={handleConfirm}
          disabled={selectedBlogs.length === 0}
        >
          {confirmText}
        </Button>,
      ].filter(Boolean)}
    >
      {/* Search and Filter Form */}
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSearch}
        className="mb-4 p-4 bg-gray-50 rounded-lg"
      >
        <Row gutter={16}>
          <Col xs={24} md={8}>
            <Form.Item name="search" label="Tìm kiếm">
              <Input placeholder="Tìm theo tiêu đề, nội dung..." allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} md={8}>
            <Form.Item name="category_id" label="Danh mục">
              <Select placeholder="Chọn danh mục" allowClear>
                {/* TODO: Load categories from API */}
                <Option value="1">Công nghệ</Option>
                <Option value="2">Kinh doanh</Option>
                <Option value="3">Giải trí</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} md={8}>
            <Form.Item label=" ">
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  Tìm kiếm
                </Button>
                <Button
                  type="default"
                  onClick={handleReset}
                  icon={<ReloadOutlined />}
                >
                  Đặt lại
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      {/* Selected count */}
      {selectedBlogs.length > 0 && (
        <div className="mb-4 p-2 bg-blue-50 rounded border border-blue-200">
          <span className="text-blue-600">
            Đã chọn {selectedBlogs.length} bài viết
          </span>
        </div>
      )}

      {/* Table */}
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={blogs}
        rowKey="post_id"
        loading={loading}
        pagination={false}
        scroll={{ y: 400 }}
        size="small"
      />

      {/* Pagination */}
      <div className="mt-4 flex justify-between items-center">
        <span className="text-gray-500">Tổng cộng: {total} bài viết</span>
        <CursorPagination
          total={total}
          isNext={isNext}
          isBack={isBack}
          goNext={goNext}
          goBack={goBack}
        />
      </div>
    </Modal>
  );
};

export default BlogSelectionModal;
