import { SearchOutlined, SettingOutlined } from '@ant-design/icons';
import { Drawer } from 'antd';
import React, { useState } from 'react';
import './blog-menu.scss';
import SEOWrapper from './seo-wrapper';

import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import BlogSetupDrawer from './blog-setup-drawer';

const menuItems = [
  {
    key: 'seo',
    icon: <SearchOutlined className="menu-icon" />,
    label: 'SEO',
    path: '/blog/seo',
  },
  {
    key: 'setting',
    icon: <SettingOutlined className="menu-icon" />,
    label: 'Cài đặt',
    path: '/blog/setting',
  },
];

const BlogMenu: React.FC = () => {
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();
  const [menuDrawerVisible, setMenuDrawerVisible] = useState(false);
  const [setupDrawerVisible, setSetupDrawerVisible] = useState(false);
  const [seoDrawerVisible, setSeoDrawerVisible] = useState(false);
  const [currentBlog, setCurrentBlog] = useState<any>(null);

  const handleClick = (item: any) => {
    if (item.key === 'setting') {
      // Show the setup drawer instead of navigating
      // if (currentBlog) {
      //   updateDrawerData(currentBlog);
      // }
      setSetupDrawerVisible(true);
    } else if (item.key === 'seo') {
      // Show the SEO drawer
      // Khi click vào SEO, đảm bảo blog hiện tại được truyền vào SEOWrapper
      // để lấy title, mô tả và content
      //console.log('[formRef]', formRef.getFieldsValue());
      //setCurrentBlog(formRef.getFieldsValue());
      setSeoDrawerVisible(true);
    } else {
      navigate(item.path);
      setMenuDrawerVisible(false);
    }
  };

  // Otherwise, render the vertical menu
  return (
    <div className="blog-menu-container">
      {/* Vertical menu with icons and text below */}
      <div className="vertical-menu">
        {/* <button
          className="menu-toggle"
          onClick={() => setMenuDrawerVisible(true)}
          aria-label="Open menu"
          tabIndex={0}
        >
          <MenuOutlined className="menu-icon" />
          <span className="menu-label">Menu</span>
        </button> */}
        {menuItems.map((item) => (
          <button
            key={item.key}
            className="menu-item"
            onClick={() => handleClick(item)}
            aria-label={item.label}
            tabIndex={0}
          >
            {item.icon}
            <span className="menu-label">{item.label}</span>
          </button>
        ))}
      </div>

      {/* Left drawer that doesn't cover the content */}
      <Drawer
        title="Menu Bài Viết"
        placement="left"
        onClose={() => setMenuDrawerVisible(false)}
        open={menuDrawerVisible}
        mask={false}
        width={280}
        className="blog-menu-drawer"
        rootStyle={{ position: 'absolute', left: '90px' }}
        getContainer={false}
        maskClosable={true}
      >
        <div className="drawer-menu">
          {menuItems.map((item) => (
            <button
              key={item.key}
              className="drawer-menu-item"
              onClick={() => handleClick(item)}
              aria-label={item.label}
              tabIndex={0}
            >
              {item.icon}
              <span className="drawer-menu-label">{item.label}</span>
            </button>
          ))}
        </div>
      </Drawer>

      {/* Blog Setup Drawer */}
      {setupDrawerVisible && (
        <BlogSetupDrawer
          //blog={currentBlog}
          visible={setupDrawerVisible}
          onClose={() => setSetupDrawerVisible(false)}
        />
      )}

      <Drawer
        title="Kiểm tra SEO"
        placement="right"
        onClose={() => setSeoDrawerVisible(false)}
        open={seoDrawerVisible}
        width={600}
        className="seo-drawer"
      >
        {seoDrawerVisible && <SEOWrapper />}
      </Drawer>
    </div>
  );
};

export default BlogMenu;
