import { Collapse } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MediaFile } from '../../../components/upload/type';
import { MODULE } from '../config';
import { useNavigateTenant } from '../../../hooks';
import { Blog } from '../type';
import {
  AuthorPanel,
  CategoryPanel,
  ImagePanel,
  InfoPanel,
  SEOPanel,
  SourcePanel,
  TagPanel,
} from './panels';

const { Panel } = Collapse;

interface BlogSidebarProps {
  form: any;
  formValues?: Blog;
  item?: Blog;
  onImageChange: (file: MediaFile) => void;
}

const BlogSidebar: React.FC<BlogSidebarProps> = ({
  form,
  formValues,
  item,
  onImageChange,
}) => {
  const { t } = useTranslation(MODULE);

  return (
    <Collapse defaultActiveKey={['source']} style={{ width: '100%' }}>
      <Panel key="source" header="Nguồn">
        <SourcePanel form={form} />
      </Panel>

      <Panel key="5" header="Tác giả">
        <AuthorPanel form={form} />
      </Panel>

      <Panel key="8" header="Thông tin">
        <InfoPanel form={form} formValues={formValues} />
      </Panel>

      <Panel key="9" header="Danh mục">
        <CategoryPanel form={form} />
      </Panel>

      <Panel key="11" header="Từ khóa">
        <TagPanel form={form} />
      </Panel>

      <Panel key="12" header="Ảnh đại diện cho tin bài">
        <ImagePanel value={item?.image} onChange={onImageChange} />
      </Panel>

      <Panel key="13" header="Cài đặt SEO">
        <SEOPanel />
      </Panel>
    </Collapse>
  );
};

export default BlogSidebar;
