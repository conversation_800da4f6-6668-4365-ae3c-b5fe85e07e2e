.blog-related-overview {
  .overview-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    margin-bottom: 16px;

    .overview-header {
      display: flex;
      align-items: center;
      font-weight: 500;
      color: #262626;

      .post-count {
        margin-left: 12px;
        padding: 2px 8px;
        background: #e6f7ff;
        color: #1890ff;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
      }
    }

    .select-post-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      border-color: #1890ff;
      color: #1890ff;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }

      &:focus {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }

  .related-posts-list {
    margin-top: 12px;

    .related-post-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      margin-bottom: 8px;
      background: #fff;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      transition: all 0.2s;

      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .post-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .post-title {
          font-weight: 500;
          color: #262626;
          font-size: 14px;
        }

        .post-priority {
          font-size: 12px;
          color: #666;
        }
      }

      .bidirectional-badge {
        padding: 2px 8px;
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
      }
    }
  }

  // Empty state
  &.empty {
    .overview-section {
      .post-count {
        background: #f5f5f5;
        color: #999;
      }
    }
  }

  // Loading state
  &.loading {
    .overview-section {
      opacity: 0.6;
      pointer-events: none;
    }
  }

  // Error state
  &.error {
    .overview-section {
      border-color: #ff4d4f;
      background: #fff2f0;

      .overview-header {
        color: #ff4d4f;
      }

      .post-count {
        background: #ffccc7;
        color: #ff4d4f;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .blog-related-overview {
    .overview-section {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .overview-header {
        justify-content: center;
      }

      .select-post-btn {
        width: 100%;
        justify-content: center;
      }
    }

    .related-posts-list {
      .related-post-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .post-info {
          width: 100%;
        }

        .bidirectional-badge {
          align-self: flex-end;
        }
      }
    }
  }
}

// Dark theme support
.dark {
  .blog-related-overview {
    .overview-section {
      background: #1f1f1f;
      border-color: #303030;

      .overview-header {
        color: #fff;
      }

      .post-count {
        background: #111b26;
        color: #1890ff;
      }

      .select-post-btn {
        background: #1f1f1f;
        border-color: #1890ff;
        color: #1890ff;

        &:hover {
          background: #262626;
          border-color: #40a9ff;
          color: #40a9ff;
        }
      }
    }

    .related-posts-list {
      .related-post-item {
        background: #1f1f1f;
        border-color: #303030;

        &:hover {
          border-color: #434343;
          box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
        }

        .post-info {
          .post-title {
            color: #fff;
          }

          .post-priority {
            color: #999;
          }
        }

        .bidirectional-badge {
          background: #162312;
          color: #52c41a;
          border-color: #274916;
        }
      }
    }

    &.error {
      .overview-section {
        background: #2a1215;
        border-color: #ff4d4f;

        .overview-header {
          color: #ff4d4f;
        }

        .post-count {
          background: #3c1518;
          color: #ff4d4f;
        }
      }
    }
  }
}
