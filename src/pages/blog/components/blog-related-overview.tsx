import { InfoCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../config';
import { Blog, RelatedPost, RelatedPostBulkCreate } from '../type';
import BlogRelated from './blog-related';
import './blog-related-overview.scss';

interface BlogRelatedOverviewProps {
  currentPostId?: number;
  relatedPosts: RelatedPost[];
  onSaveRelations?: (relations: RelatedPostBulkCreate) => void;
  maxRelatedPosts?: number;
  mode?: 'select' | 'manage';
  onConfirm?: (selectedBlogs: Blog[]) => void;
}

const BlogRelatedOverview: React.FC<BlogRelatedOverviewProps> = ({
  currentPostId,
  relatedPosts = [],
  onSaveRelations,
  maxRelatedPosts = 3,
  mode = 'manage',
  onConfirm,
}) => {
  const { t } = useTranslation(MODULE);
  const [modalVisible, setModalVisible] = useState(false);

  const handleOpenModal = () => {
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  const handleConfirmSelection = (selectedBlogs: Blog[]) => {
    if (mode === 'select' && onConfirm) {
      onConfirm(selectedBlogs);
    }
    setModalVisible(false);
  };

  const handleSaveRelations = (relations: RelatedPostBulkCreate) => {
    if (onSaveRelations) {
      onSaveRelations(relations);
    }
    setModalVisible(false);
  };

  return (
    <div className="blog-related-overview">
      <div className="overview-section">
        <div className="overview-header">
          <span>{t('relatedPosts', 'Bài viết liên quan')}</span>
          <Tooltip
            title={t(
              'relatedPostsTooltip',
              'Chọn các bài viết liên quan để tăng tương tác và SEO',
            )}
          >
            <InfoCircleOutlined
              style={{ marginLeft: '5px', color: '#1890ff' }}
            />
          </Tooltip>
          <span className="post-count">
            {relatedPosts.length}/{maxRelatedPosts}
          </span>
        </div>

        <Button
          icon={<PlusOutlined />}
          className="select-post-btn"
          onClick={handleOpenModal}
          type="dashed"
        >
          {t('selectPosts', 'Chọn bài viết')}
        </Button>
      </div>

      {/* Display current related posts */}
      {relatedPosts.length > 0 && (
        <div className="related-posts-list">
          {relatedPosts.map((relation, index) => (
            <div key={relation.id || index} className="related-post-item">
              <div className="post-info">
                <span className="post-title">
                  {relation.related_post?.title ||
                    `Post ID: ${relation.related_post_id}`}
                </span>
                <span className="post-priority">
                  Độ ưu tiên: {relation.priority}
                </span>
              </div>
              {relation.is_bidirectional && (
                <span className="bidirectional-badge">Hai chiều</span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Blog Related Modal */}
      <BlogRelated
        visible={modalVisible}
        onClose={handleCloseModal}
        onConfirm={handleConfirmSelection}
        currentPostId={currentPostId}
        title={
          mode === 'manage'
            ? t('manageRelatedPosts', 'Quản lý bài viết liên quan')
            : t('selectRelatedPosts', 'Chọn bài viết liên quan')
        }
        mode={mode}
        onSaveRelations={handleSaveRelations}
        existingRelations={relatedPosts}
        excludeBlogIds={currentPostId ? [currentPostId] : []}
      />
    </div>
  );
};

export default BlogRelatedOverview;
