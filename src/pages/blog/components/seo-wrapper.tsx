import React, { useCallback, useEffect, useMemo, useState } from 'react';
import ConsoleService from '../../../services/console.service';
import { SEOChecker } from '../../seo-meta/components/index';
import useSEOMetaStore from '../../seo-meta/store';
import { SeoMeta } from '../../seo-meta/type';
import { fetchSEOData } from '../api';
import { MODULE } from '../config';
import { useCurrentBlog, useFormValues } from '../store';

const SEOWrapper: React.FC = () => {
  const logger = ConsoleService.register(MODULE);

  // Lấy dữ liệu từ Zustand store để đảm bảo luôn có dữ liệu mới nhất
  const storeFormValues = useFormValues();
  const currentBlog = useCurrentBlog();
  const [isInitialized, setIsInitialized] = useState(false);
  const [seoData, setSeoData] = useState<any>(null); // State để lưu dữ liệu SEO từ API
  const [loading, setLoading] = useState(false);

  // Kết hợp dữ liệu từ props và store, ưu tiên dữ liệu từ store nếu có
  const rawBlogData = storeFormValues || currentBlog;
  const loadSEOData = async (postId: number) => {
    setLoading(true);
    try {
      logger(`[SEO Fetch] Starting fetch for post ID: ${postId}`);
      const response = await fetchSEOData(postId.toString());
      if (response.status.success && response.data) {
        logger('[SEO Fetch] Data fetched successfully:', response.data);
        setSeoData(response.data);
      } else {
        logger('[SEO Fetch] Failed to fetch data:', response);
      }
    } catch (error) {
      logger('[SEO Fetch] Error:', error);
    } finally {
      logger('[SEO Fetch] Completed, setting loading to false');
      setLoading(false);
    }
  };
  useEffect(() => {
    const postId = rawBlogData?.post_id;
    if (postId) {
      loadSEOData(postId);
    }
  }, [rawBlogData]);

  // Kết hợp dữ liệu từ blog và SEO data, map các trường cho đúng
  const blogData = useMemo(() => {
    // Nếu không có SEO data, sử dụng dữ liệu từ blog
    if (!seoData) {
      logger(
        '[SEO Data Mapping] Using raw blog data only (no SEO data available)',
        {
          rawBlogDataId: rawBlogData?.post_id,
          rawBlogDataTitle: rawBlogData?.title,
          rawBlogDataMetaTitle: rawBlogData?.meta_title,
        },
      );

      // Đảm bảo các trường SEO có giá trị mặc định
      const defaultData = {
        ...rawBlogData,
        meta_title: rawBlogData?.meta_title || rawBlogData?.title || '',
        meta_description:
          rawBlogData?.meta_description || rawBlogData?.description || '',
        og_title:
          rawBlogData?.og_title ||
          rawBlogData?.meta_title ||
          rawBlogData?.title ||
          '',
        og_description:
          rawBlogData?.og_description ||
          rawBlogData?.meta_description ||
          rawBlogData?.description ||
          '',
      };

      logger('[SEO Data Mapping] Default data created:', {
        defaultMetaTitle: defaultData.meta_title,
        defaultOgTitle: defaultData.og_title,
      });

      return defaultData;
    }

    // Kết hợp dữ liệu từ blog và SEO data
    logger('[SEO Data Mapping] Combining raw blog data with SEO data', {
      rawBlogDataId: rawBlogData?.post_id,
      seoDataId: seoData?.meta_id,
    });

    logger('[SEO Data Mapping] Title comparison:', {
      rawBlogTitle: rawBlogData?.title,
      rawMetaTitle: rawBlogData?.meta_title,
      seoMetaTitle: seoData?.meta_title,
      willUse: seoData?.meta_title !== undefined ? 'SEO data' : 'Raw blog data',
    });

    const combinedData = {
      ...rawBlogData,
      // Map các trường SEO
      meta_title:
        seoData?.meta_title !== undefined && seoData?.meta_title !== ''
          ? seoData.meta_title
          : rawBlogData?.meta_title || rawBlogData?.title || '',
      meta_description:
        seoData?.meta_description !== undefined &&
        seoData?.meta_description !== ''
          ? seoData.meta_description
          : rawBlogData?.meta_description || rawBlogData?.description || '',
      meta_keywords:
        seoData?.keywords !== undefined && seoData?.keywords !== ''
          ? seoData.keywords
          : rawBlogData?.meta_keywords || '',
      canonical_url:
        seoData?.canonical_url !== undefined && seoData?.canonical_url !== ''
          ? seoData.canonical_url
          : rawBlogData?.canonical_url || '',
      og_title:
        seoData?.og_title !== undefined && seoData?.og_title !== ''
          ? seoData.og_title
          : rawBlogData?.og_title ||
            rawBlogData?.meta_title ||
            rawBlogData?.title ||
            '',
      og_description:
        seoData?.og_description !== undefined && seoData?.og_description !== ''
          ? seoData.og_description
          : rawBlogData?.og_description ||
            rawBlogData?.meta_description ||
            rawBlogData?.description ||
            '',
      og_image:
        seoData?.og_image !== undefined && seoData?.og_image !== ''
          ? seoData.og_image
          : rawBlogData?.og_image || '',
      robots_index:
        seoData?.robots_index !== undefined
          ? seoData.robots_index
          : rawBlogData?.robots_index !== undefined
            ? rawBlogData.robots_index
            : true,
      robots_follow:
        seoData?.robots_follow !== undefined
          ? seoData.robots_follow
          : rawBlogData?.robots_follow !== undefined
            ? rawBlogData.robots_follow
            : true,
      robots_advanced:
        seoData?.robots_advanced !== undefined &&
        seoData?.robots_advanced !== ''
          ? seoData.robots_advanced
          : rawBlogData?.robots_advanced || '',
      seo_score:
        seoData?.seo_score !== undefined
          ? seoData.seo_score
          : rawBlogData?.seo_score !== undefined
            ? rawBlogData.seo_score
            : 0,
      readability_score:
        seoData?.readability_score !== undefined
          ? seoData.readability_score
          : rawBlogData?.readability_score !== undefined
            ? rawBlogData.readability_score
            : 0,
      schema_data:
        seoData?.schema_data !== undefined && seoData?.schema_data !== ''
          ? seoData.schema_data
          : rawBlogData?.schema_data || '',
    };

    logger('[SEO Data Mapping] Combined data result:', {
      finalMetaTitle: combinedData.meta_title,
      finalMetaDescription:
        combinedData.meta_description?.substring(0, 30) + '...',
      finalOgTitle: combinedData.og_title,
      finalOgDescription: combinedData.og_description?.substring(0, 30) + '...',
      seoScore: combinedData.seo_score,
      readabilityScore: combinedData.readability_score,
      robotsIndex: combinedData.robots_index,
      robotsFollow: combinedData.robots_follow,
    });

    // Kiểm tra xem các trường quan trọng có giá trị không
    if (!combinedData.meta_title) {
      logger('[SEO Data Mapping] WARNING: meta_title is empty!');
    }

    if (!combinedData.og_title) {
      logger('[SEO Data Mapping] WARNING: og_title is empty!');
    }

    return combinedData;
  }, [rawBlogData, seoData, logger]);

  // Extract headings from content
  const extractHeadings = (content: string) => {
    const h2Regex = /<h2[^>]*>(.*?)<\/h2>/gi;
    const h3h6Regex = /<h[3-6][^>]*>(.*?)<\/h[3-6]>/gi;

    const h2Matches = content.match(h2Regex) || [];
    const h3h6Matches = content.match(h3h6Regex) || [];

    const h2 = h2Matches.map((match) => match.replace(/<[^>]*>/g, '').trim());
    const h3h6 = h3h6Matches.map((match) =>
      match.replace(/<[^>]*>/g, '').trim(),
    );

    return { h2, h3h6 };
  };

  // Extract images from content
  const extractImages = (content: string) => {
    const imgRegex = /<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi;
    const images: any[] = [];
    let match;

    while ((match = imgRegex.exec(content)) !== null) {
      images.push({
        url: match[1],
        alt: match[2] || '',
        _id: `img-${images.length}`,
        name: `image-${images.length}`,
        type: 'image',
      });
    }

    return images;
  };

  // Initialize SEO data when blog data changes
  // useEffect(() => {
  //   // Log trạng thái hiện tại
  //   logger('[SEO Initialization] Current state:', {
  //     isInitialized,
  //     loading,
  //     hasSEOData: !!seoData,
  //     hasPostId: !!rawBlogData?.post_id,
  //     blogDataTitle: blogData?.title,
  //     rawBlogDataTitle: rawBlogData?.title,
  //   });

  //   // Chỉ khởi tạo khi không phải đang loading và chưa được khởi tạo
  //   if (!isInitialized && !loading) {
  //     // Đảm bảo rằng seoData đã được tải xong (nếu có post_id)
  //     const postId = rawBlogData?.post_id;
  //     const shouldWaitForSEO = postId && !seoData;

  //     // Nếu cần đợi dữ liệu SEO và đang không loading, thì chưa khởi tạo
  //     if (shouldWaitForSEO) {
  //       logger(
  //         '[SEO Initialization] Waiting for SEO data to be loaded before initialization',
  //       );
  //       return;
  //     }

  //     logger('[SEO Initialization] Starting initialization process');

  //     // Lấy dữ liệu mới nhất từ store hoặc props
  //     logger('[SEO Initialization] Using blogData:', blogData);

  //     const title = blogData?.title || '';
  //     const description = blogData?.description || '';
  //     const content = blogData?.content || '';
  //     const keyword = '';

  //     // Extract headings and images from content
  //     const headings = extractHeadings(content);
  //     const images = extractImages(content);

  //     // Cập nhật SEO data vào store
  //     // const seoStore = useSEOMetaStore.getState();

  //     // Cập nhật dữ liệu SEO vào store
  //     logger('[SEO Initialization] Using title:', title);
  //     // seoStore.setFieldValue('meta_title', title);
  //     // seoStore.setFieldValue('meta_description', description);
  //     // seoStore.setFieldValue('meta_keywords', keyword);

  //     // Lưu dữ liệu phân tích vào data-attribute cho debugging nếu cần
  //     logger('[SEO Initialization] Content analysis data:', {
  //       headings,
  //       imageCount: images.length,
  //       contentLength: content.length,
  //     });

  //     logger('[SEO Initialization] SEO data initialized:', {
  //       title,
  //       description,
  //       contentPreview: content.substring(0, 50) + '...',
  //       keyword,
  //       headings,
  //       images: images.length,
  //       seoDataSource: seoData ? 'API' : 'None',
  //     });

  //     setIsInitialized(true);
  //     logger('[SEO Initialization] Initialization completed');
  //   }
  // }, [isInitialized, blogData, loading, rawBlogData?.post_id, seoData, logger]);

  // Reset initialization when raw blog data changes (từ bất kỳ nguồn nào)
  // useEffect(() => {
  //   // Chỉ reset khi dữ liệu gốc thay đổi, không phải khi seoData thay đổi
  //   if (isInitialized) {
  //     logger('[SEO Reset] Resetting initialization due to blog data change', {
  //       rawBlogDataId: rawBlogData?.post_id,
  //       rawBlogDataTitle: rawBlogData?.title,
  //       currentBlogId: currentBlog?.post_id,
  //       formValuesTitle: storeFormValues?.title,
  //     });
  //     setIsInitialized(false);
  //   }
  // }, [
  //   // Sử dụng rawBlogData thay vì blogData để tránh reset khi seoData thay đổi
  //   rawBlogData?.post_id,
  //   rawBlogData?.title,
  //   rawBlogData?.content,
  //   // Vẫn giữ các nguồn dữ liệu khác
  //   currentBlog?.post_id,
  //   storeFormValues?.title,
  //   // Thêm isInitialized để chỉ log khi thực sự reset
  //   isInitialized,
  //   logger,
  // ]);

  // Callback để nhận dữ liệu SEO từ SEOChecker và cập nhật vào SEO Meta store
  const handleSEODataChange = useCallback(
    (seoData: Partial<SeoMeta>) => {
      logger('SEOWrapper - receiving SEO data change:', seoData);

      // Cập nhật trực tiếp vào SEO Meta store
      const seoStore = useSEOMetaStore.getState();

      // Cập nhật từng trường một vào SEO Meta store
      Object.keys(seoData).forEach((key) => {
        const fieldName = key as keyof SeoMeta;
        const value = seoData[fieldName];
        if (value !== undefined) {
          seoStore.setFieldValue(fieldName, value);
        }
      });

      logger('SEOWrapper - updated SEO Meta store');
    },
    [logger],
  );

  // Map blogData sang định dạng props mà SEOChecker mong đợi
  const seoCheckerProps = useMemo(() => {
    logger('[SEO Props] Preparing SEOChecker props', {
      hasBlogData: !!blogData,
      blogDataTitle: blogData?.title,
      blogDataMetaTitle: blogData?.meta_title,
      blogDataOgTitle: blogData?.og_title,
      rawBlogDataTitle: rawBlogData?.title,
      rawBlogDataMetaTitle: rawBlogData?.meta_title,
      rawBlogDataOgTitle: rawBlogData?.og_title,
      seoDataMetaTitle: seoData?.meta_title,
      seoDataOgTitle: seoData?.og_title,
    });

    // Tạo các giá trị kết hợp với ưu tiên rõ ràng
    const combinedTitle = blogData?.meta_title || blogData?.title || '';
    const combinedOgTitle =
      blogData?.og_title || blogData?.meta_title || blogData?.title || '';

    logger('[SEO Props] Combined values:', {
      combinedTitle,
      combinedOgTitle,
    });

    return {
      // Thông tin cơ bản của bài viết
      title: blogData?.title || '',
      description: blogData?.description || '',
      content: blogData?.content || '',
      url: blogData?.slug || '',

      // Thông tin SEO Meta - đảm bảo truyền giá trị rõ ràng
      meta_title: combinedTitle,
      meta_description: blogData?.meta_description || '',
      meta_keywords: blogData?.meta_keywords || '',
      canonical_url: blogData?.canonical_url || '',
      og_title: combinedOgTitle,
      og_description:
        blogData?.og_description ||
        blogData?.meta_description ||
        blogData?.description ||
        '',
      og_image: blogData?.og_image || '',
      robots_index:
        blogData?.robots_index !== undefined ? blogData.robots_index : true,
      robots_follow:
        blogData?.robots_follow !== undefined ? blogData.robots_follow : true,
      robots_advanced: blogData?.robots_advanced || '',
      seo_score: blogData?.seo_score !== undefined ? blogData.seo_score : 0,
      readability_score:
        blogData?.readability_score !== undefined
          ? blogData.readability_score
          : 0,
      schema_data: blogData?.schema_data || '',

      // Callback và trạng thái
      onSEODataChange: handleSEODataChange,
      loading: loading,
    };
  }, [blogData, rawBlogData, seoData, handleSEODataChange, loading, logger]);

  // Log chi tiết các props trước khi truyền vào SEOChecker
  logger('[SEOChecker] Props being passed to SEOChecker:', {
    meta_title: seoCheckerProps.meta_title,
    og_title: seoCheckerProps.og_title,
    title: seoCheckerProps.title,
    robots_index: seoCheckerProps.robots_index,
    robots_follow: seoCheckerProps.robots_follow,
  });

  // Kiểm tra xem blogData có đúng không
  logger('[SEOChecker] Current blogData:', {
    id: blogData?.post_id,
    title: blogData?.title,
    meta_title: blogData?.meta_title,
    og_title: blogData?.og_title,
  });

  return <SEOChecker {...seoCheckerProps} />;
};

export default SEOWrapper;
