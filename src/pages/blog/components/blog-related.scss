.blog-related-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  .ant-modal-body {
    padding: 20px;
  }

  .blog-related-search {
    margin-bottom: 16px;

    .ant-input {
      border-radius: 6px;
    }

    .ant-select {
      .ant-select-selector {
        border-radius: 6px;
      }
    }
  }

  .blog-related-filters {
    margin-top: 16px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 6px;

    .ant-checkbox-wrapper {
      display: block;
      margin-bottom: 8px;
    }
  }

  .blog-related-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    margin-bottom: 16px;

    .blog-related-header {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
      position: sticky;
      top: 0;
      z-index: 1;
    }

    .ant-list-item {
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f5f5f5;
      }

      &.selected {
        background-color: #f6ffed;
        border-left: 3px solid #52c41a;
      }
    }

    .blog-item-content {
      display: flex;
      align-items: center;
      width: 100%;

      .blog-item-checkbox {
        margin-right: 12px;
        flex-shrink: 0;
      }

      .blog-item-avatar {
        margin-right: 12px;
        flex-shrink: 0;
        border-radius: 4px;
        overflow: hidden;
      }

      .blog-item-info {
        flex: 1;
        min-width: 0;

        .blog-item-title {
          font-weight: 500;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #262626;
        }

        .blog-item-meta {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;

          .ant-tag {
            margin-right: 8px;
            font-size: 11px;
            padding: 0 6px;
            height: 20px;
            line-height: 20px;
          }

          .meta-item {
            margin-left: 8px;

            .anticon {
              margin-right: 4px;
            }
          }
        }
      }
    }
  }

  .blog-related-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .selected-count {
      color: #666;
      font-size: 14px;
    }

    .footer-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .filter-toggle {
    margin-top: 12px;

    .ant-btn-link {
      padding: 0;
      height: auto;
      color: #1890ff;

      &:hover {
        color: #40a9ff;
      }
    }
  }

  .empty-state {
    padding: 40px;
    text-align: center;

    .ant-empty-description {
      color: #999;
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .blog-related-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }

    .blog-related-list {
      max-height: 300px;
    }

    .blog-item-content {
      .blog-item-avatar {
        width: 48px !important;
        height: 48px !important;
      }

      .blog-item-info {
        .blog-item-title {
          font-size: 14px;
        }

        .blog-item-meta {
          font-size: 11px;

          .meta-item {
            display: block;
            margin-left: 0;
            margin-top: 2px;
          }
        }
      }
    }

    .blog-related-footer {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .footer-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .ant-btn {
          margin-left: 0;
        }
      }
    }
  }
}

// Dark theme support
.dark {
  .blog-related-modal {
    .blog-related-filters {
      background: #1f1f1f;
      border: 1px solid #303030;
    }

    .blog-related-list {
      border-color: #303030;

      .blog-related-header {
        background: #1f1f1f;
        border-bottom-color: #303030;
      }

      .ant-list-item {
        &:hover {
          background-color: #262626;
        }

        &.selected {
          background-color: #162312;
          border-left-color: #52c41a;
        }
      }
    }

    .blog-related-footer {
      border-top-color: #303030;
    }
  }
}
