import {
  CloseOutlined,
  InfoCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Drawer,
  Input,
  Select,
  Spin,
  Switch,
  Tabs,
  Tooltip,
} from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import React, { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MediaFile } from '../../../components/upload/type';
import ConsoleService from '../../../services/console.service';
import { SelectBlogAuthor } from '../../blog-author/components';
import { getTree } from '../../blog-category/api';
import { BlogCategory } from '../../blog-category/type';
import { TagInput } from '../../blog-tag/components';
import MediaUpload from '../../editor-media/components/media-upload';
import { getPostRelatedPosts } from '../api';
import { MODULE } from '../config';
import useBlogStore from '../store';
import { Blog, RelatedPost } from '../type';
import BlogRelated from './blog-related';

interface BlogSetupDrawerProps {
  blog?: Blog;
  visible: boolean;
  onClose: () => void;
}

interface CategoryOption {
  id: string | number;
  name: string;
}

export const BlogSetupDrawer: FC<BlogSetupDrawerProps> = ({
  visible,
  onClose,
}) => {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation('blog');
  const { updateDrawerData, formValues } = useBlogStore();

  // Get drawer data from store reactively
  const drawerData = useBlogStore((state) => state.form.drawerData);
  const blog = useBlogStore((state) => state.form.formValues);

  // Debug drawer data changes
  useEffect(() => {
    logger('Drawer data changed:', drawerData);
  }, [drawerData, logger]);

  // Lưu trữ blog data trong một state nội bộ để tránh mất dữ liệu khi prop thay đổi
  const [internalBlogData, setInternalBlogData] = useState<Blog | undefined>(
    blog,
  );
  const [loading, setLoading] = useState<boolean>(false);

  // Cập nhật internalBlogData khi blog prop thay đổi và không phải là null
  useEffect(() => {
    if (blog) {
      setLoading(true);
      logger('Updating internal blog data from prop:', blog);
      setInternalBlogData(blog);
      setTimeout(() => setLoading(false), 500); // Đảm bảo có hiệu ứng loading khi dữ liệu thay đổi
    }
  }, [blog, logger]);

  // Sử dụng internalBlogData thay vì blog trực tiếp
  const blogData = internalBlogData || blog;

  const [activeTab, setActiveTab] = useState('overview');
  const [tagIds, setTagIds] = useState<string[]>(
    blogData?.tags
      ? blogData.tags.map((tag: any) => String(tag.id))
      : blogData?.tag_ids?.map(String) || [],
  );
  const [tagObjects, setTagObjects] = useState<any[]>(blogData?.tags || []);
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  // Các trạng thái cho tab Overview
  const [featuredImages, setFeaturedImages] = useState<MediaFile[]>([]);
  const [excerpt, setExcerpt] = useState(blogData?.description ?? '');
  const [isFeatured, setIsFeatured] = useState(blogData?.suggest ?? false);
  const [allowComments, setAllowComments] = useState(true);
  const [relatedPosts, setRelatedPosts] = useState<Blog[]>([]);
  const [relatedPostIds, setRelatedPostIds] = useState<number[]>([]);
  const [loadingRelatedPosts, setLoadingRelatedPosts] = useState(false);

  // BlogRelated modal state
  const [blogRelatedVisible, setBlogRelatedVisible] = useState(false);

  // Các trạng thái cho tab Settings
  const [status, setStatus] = useState<string>(blogData?.status || 'draft');
  const [visibility, setVisibility] = useState<string>(
    blogData?.visibility || 'public',
  );
  const [commentStatus, setCommentStatus] = useState<string>(
    blogData?.comment_status || 'open',
  );
  const [password, setPassword] = useState<string>(blogData?.password || '');

  // Cập nhật excerpt khi blogData thay đổi
  useEffect(() => {
    if (blogData?.description !== undefined) {
      setExcerpt(blogData.description);
      logger('Updated excerpt from blogData:', blogData.description);
    }
  }, [blogData?.description, logger]);

  // Cập nhật các state khác khi blogData thay đổi
  useEffect(() => {
    if (blogData) {
      setIsFeatured(blogData.suggest ?? false);

      // Initialize featured images from blogData
      if (blogData.image) {
        const initialImage: MediaFile = {
          id: 0, // Temporary ID for existing images
          url: blogData.image, // Use the actual URL from blogData
          status: 'ready',
          // Optional fields
          filename: 'featured-image',
          original_filename: 'featured-image',
          public_url: blogData.image,
          media_type: 'image',
          content_type: 'image/*',
          size: 0,
          // Legacy compatibility
          _id: 'current-image',
          mimetype: 'image/*',
        };
        setFeaturedImages([initialImage]);
      } else {
        setFeaturedImages([]);
      }

      // Initialize related posts from blogData
      if (
        blogData.related_post_ids &&
        Array.isArray(blogData.related_post_ids)
      ) {
        setRelatedPostIds(blogData.related_post_ids);
        logger('Initialized related post IDs:', blogData.related_post_ids);
      } else {
        setRelatedPostIds([]);
      }

      logger('Updated drawer states from blogData');
    }
  }, [blogData, logger]);

  const [tagNames, setTagNames] = useState<string[]>(
    blogData?.tags ? blogData.tags.map((tag: any) => tag.name) : [],
  );

  // Type-safe function to update drawer data
  const updateDataToStore = React.useCallback(() => {
    // Check if blogData exists
    if (!blogData) {
      logger(
        'Warning: Cannot update store data because blog data is null or undefined',
      );
      return;
    }

    logger('updateDataToStore', {
      tagIds,
      selectedCategories,
      excerpt,
      isFeatured,
      relatedPostIds,
      blogData,
    });

    // Create drawer data update object as Partial<Blog> for store compatibility
    const drawerDataUpdate: Partial<Blog> = {
      tag_ids: tagIds
        .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
        .filter((id) => !isNaN(id)),
      category_ids: selectedCategories
        .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
        .filter((id) => !isNaN(id)),
      description: excerpt,
      suggest: isFeatured,
      related_post_ids: relatedPostIds,
    };

    // Add image if it exists
    if (blogData.image) {
      drawerDataUpdate.image = blogData.image;
    }

    logger('Updating drawer data with:', drawerDataUpdate);
    updateDrawerData(drawerDataUpdate);

    // Verify update
    setTimeout(() => {
      const { form } = useBlogStore.getState();
      logger('Store drawer data after update:', form.drawerData);
    }, 100);
  }, [
    tagIds,
    selectedCategories,
    excerpt,
    isFeatured,
    relatedPostIds,
    blogData,
    logger,
    updateDrawerData,
  ]);

  const fetchCategories = React.useCallback(async () => {
    try {
      const response = await getTree();
      if (response.status.success && response.data) {
        // Chuyển đổi dữ liệu từ API thành dạng phẳng để hiển thị
        const flatCategories = response.data.map((item: BlogCategory) => ({
          id: item.id,
          name: item.name,
        }));
        setCategories(flatCategories);
        logger('Fetched categories for display:', flatCategories.length);
      }
    } catch (error) {
      console.error('Lỗi khi lấy danh mục:', error);
    }
  }, [setCategories, logger]);

  // Fetch related posts from API
  const fetchRelatedPosts = React.useCallback(async () => {
    if (!blogData?.post_id) {
      logger('No post_id available for fetching related posts');
      return;
    }

    setLoadingRelatedPosts(true);
    try {
      const response = await getPostRelatedPosts(String(blogData.post_id));
      if (response.status.success && response.data) {
        const relatedPostsData = response.data as RelatedPost[];

        // Extract related posts and IDs

        const postIds = relatedPostsData.map((relation) => relation.post_id);
        logger(
          '[getPostRelatedPosts]',

          relatedPostsData,
        );
        setRelatedPosts(relatedPostsData);
        // setRelatedPostIds(postIds);

        // Update drawer data
        const drawerUpdate: Partial<Blog> = {
          related_post_ids: postIds,
        };
        updateDrawerData(drawerUpdate);
      }
    } catch (error) {
      console.error('Error fetching related posts:', error);
    } finally {
      setLoadingRelatedPosts(false);
    }
  }, [blogData?.post_id, logger, updateDrawerData]);

  // Cập nhật dữ liệu vào store khi component khởi tạo
  useEffect(() => {
    if (blogData) {
      logger('Blog data received in drawer:', blogData);
      updateDrawerData(blogData);
    }
  }, [blogData, updateDrawerData, logger]);

  // Khởi tạo selectedCategories khi blogData thay đổi
  useEffect(() => {
    if (blogData) {
      // Xử lý category_ids (mảng số)
      if (blogData.category_ids && Array.isArray(blogData.category_ids)) {
        const categoryIds = blogData.category_ids.map((id) => String(id));
        setSelectedCategories(categoryIds);
        logger('Initialized categories from category_ids:', categoryIds);
      }
      // Xử lý categories (mảng đối tượng)
      else if (blogData.categories && Array.isArray(blogData.categories)) {
        const categoryIds = blogData.categories.map((cat: any) =>
          String(cat.id),
        );
        setSelectedCategories(categoryIds);
        logger('Initialized categories from categories objects:', categoryIds);
      }
    }
  }, [blogData, logger, setSelectedCategories]);

  // Khởi tạo tags khi blogData thay đổi
  useEffect(() => {
    if (blogData) {
      // Xử lý tag_ids (mảng số)
      if (blogData.tag_ids && Array.isArray(blogData.tag_ids)) {
        const tagIds = blogData.tag_ids.map((id) => String(id));
        setTagIds(tagIds);
        logger('Initialized tags from tag_ids:', tagIds);
      }
      // Xử lý tags (mảng đối tượng)
      else if (blogData.tags && Array.isArray(blogData.tags)) {
        const tagIds = blogData.tags.map((tag: any) => String(tag.id));
        setTagIds(tagIds);
        setTagObjects(blogData.tags);
        // Cập nhật tagNames từ danh sách tag
        const names = blogData.tags.map((tag: any) => tag.name);
        setTagNames(names);
        logger(
          'Initialized tags from tags objects:',
          tagIds,
          'with names:',
          names,
        );
      }
    }
  }, [blogData, logger, setTagIds, setTagObjects]);

  // Cập nhật dữ liệu khi drawer hiển thị
  useEffect(() => {
    if (visible) {
      setLoading(true);
      // Kiểm tra nếu có dữ liệu blogData trước khi cập nhật
      if (blogData) {
        logger('Drawer opened with blog data:', blogData);
        // Cập nhật dữ liệu vào store khi drawer hiển thị
        updateDataToStore();

        // Fetch related posts if post_id exists
        if (blogData.post_id) {
          fetchRelatedPosts();
        }

        setLoading(false);
      } else {
        logger('Warning: Drawer opened without blog data');
        setLoading(false);
      }
    }
  }, [visible, updateDataToStore, blogData, logger]);

  useEffect(() => {
    if (visible && activeTab === 'category') {
      fetchCategories();
    }
  }, [visible, activeTab, fetchCategories]);

  const handleTabChange = (key: string) => {
    // Lưu dữ liệu hiện tại vào store trước khi chuyển tab
    updateDataToStore();
    setActiveTab(key);
  };

  const handleCategoryChange = React.useCallback(
    (categoryId: string | number, checked: boolean) => {
      const categoryIdStr = String(categoryId);
      const newSelectedCategories = checked
        ? [...selectedCategories, categoryIdStr]
        : selectedCategories.filter((id) => id !== categoryIdStr);

      logger('Category selection changed:', {
        categoryId,
        checked,
        oldSelection: selectedCategories,
        newSelection: newSelectedCategories,
      });

      setSelectedCategories(newSelectedCategories);

      // Update drawer data with proper type conversion
      const categoryIdsAsNumbers = newSelectedCategories
        .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
        .filter((id) => !isNaN(id));

      const drawerUpdate: Partial<Blog> = {
        category_ids: categoryIdsAsNumbers,
      };
      updateDrawerData(drawerUpdate);
    },
    [selectedCategories, setSelectedCategories, updateDrawerData, logger],
  );

  const handleTagsChange = (newTags: string[]) => {
    setTagNames(newTags);

    // Convert tag names to tag IDs with proper type handling
    const newTagIds = newTags.map((name) => {
      // Find tag ID by name
      const foundTag = tagObjects.find((tag) => tag.name === name);
      if (foundTag) {
        return String(foundTag.id);
      }
      // For new tags, temporarily use name as ID (should be handled by backend)
      return name;
    });

    setTagIds(newTagIds);

    // Update drawer data with proper type conversion
    const tagIdsAsNumbers = newTagIds
      .map((id) => (typeof id === 'string' ? parseInt(id, 10) : id))
      .filter((id) => !isNaN(id));

    const drawerUpdate: Partial<Blog> = {
      tag_ids: tagIdsAsNumbers,
    };
    updateDrawerData(drawerUpdate);
  };

  const handleExcerptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= 140) {
      setExcerpt(value);
      // Update drawer data with proper type safety
      const drawerUpdate: Partial<Blog> = { description: value };
      updateDrawerData(drawerUpdate);
    }
  };

  // Handle media upload changes with type safety
  const handleMediaUploadChange = (files: MediaFile[]) => {
    setFeaturedImages(files);
    const imageUrl =
      files.length > 0 ? files[0].url || files[0].public_url : undefined;

    // Update drawer data with proper type safety
    const drawerUpdate: Partial<Blog> = {
      image: imageUrl,
    };
    updateDrawerData(drawerUpdate);
    logger('Featured image changed:', imageUrl);
  };

  // Handle featured post toggle with type safety
  const handleFeaturedChange = (checked: boolean) => {
    setIsFeatured(checked);
    // Update drawer data with proper type safety
    const drawerUpdate: Partial<Blog> = { suggest: checked };
    updateDrawerData(drawerUpdate);
  };

  // Xử lý thay đổi của Switch allowComments
  const handleAllowCommentsChange = (checked: boolean) => {
    setAllowComments(checked);
    // Cập nhật vào store khi có thay đổi (cần thêm trường vào interface Blog nếu cần)
    // updateDrawerData({ allowComments: checked });
  };

  // Handle BlogRelated modal
  const handleOpenBlogRelated = () => {
    setBlogRelatedVisible(true);
  };

  const handleCloseBlogRelated = () => {
    setBlogRelatedVisible(false);
  };

  const handleRelatedPostsConfirm = (selectedBlogs: Blog[]) => {
    // Merge với state hiện có thay vì ghi đè
    setRelatedPosts((prevPosts) => {
      const existingIds = prevPosts.map((post) => post.post_id);
      const newPosts = selectedBlogs.filter(
        (blog) => !existingIds.includes(blog.post_id),
      );
      return [...prevPosts, ...newPosts];
    });

    // Get post IDs from selected blogs (ensuring they're numbers)
    const selectedIds = selectedBlogs
      .map((blog) => blog.post_id)
      .filter(Boolean) as number[];

    // Get post IDs from current related posts
    const currentRelatedIds = relatedPosts
      .map((blog) => blog.post_id)
      .filter(Boolean) as number[];

    // Create a merged, deduplicated array
    const mergedIds = [...new Set([...currentRelatedIds, ...selectedIds])];

    // Update local state first
    setRelatedPostIds(mergedIds);

    // Log the update for debugging
    logger('Related posts update:', {
      selectedBlogs,
      selectedIds,
      currentRelatedIds,
      mergedIds,
    });

    // Update drawer data with merged IDs
    const drawerUpdate: Partial<Blog> = {
      related_post_ids: mergedIds,
    };

    updateDrawerData(drawerUpdate);

    // Verify update immediately
    setTimeout(() => {
      const { form } = useBlogStore.getState();
      logger('Store drawer data after related post update:', form.drawerData);
    }, 100);

    setBlogRelatedVisible(false);
  };

  // Handlers for settings tab
  const handleStatusChange = (value: string) => {
    setStatus(value);
    updateDrawerData({ status: value });
  };

  const handleVisibilityChange = (value: string) => {
    setVisibility(value);
    updateDrawerData({ visibility: value });
  };

  const handleCommentStatusChange = (value: string) => {
    setCommentStatus(value);
    updateDrawerData({ comment_status: value });
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);
    updateDrawerData({ password: value });
  };

  const renderOverviewContent = () => (
    <div className="overview-container">
      <div className="overview-section">
        <div className="overview-header">
          <span>Ảnh bìa</span>
        </div>
        <div className="upload-container">
          <MediaUpload
            multiple={false}
            maxCount={1}
            accept="image/*"
            initialImages={featuredImages}
            onChange={handleMediaUploadChange}
            mode="card"
            uploadText="Tải ảnh bìa"
          />
        </div>
      </div>

      <div className="overview-section">
        <div className="overview-header">
          <span>{t('author')}</span>
          <Tooltip title={t('authorTooltip')}>
            <InfoCircleOutlined
              style={{ marginLeft: '5px', color: '#1890ff' }}
            />
          </Tooltip>
        </div>
        <SelectBlogAuthor
          className="select-full"
          value={
            // Prioritize drawer data, fallback to blog data
            drawerData?.author_id
              ? String(drawerData.author_id)
              : blogData?.author_id
                ? String(blogData.author_id)
                : undefined
          }
          onChange={(value) => {
            console.log('[SelectBlogAuthor]', value);
            updateDrawerData({ author_id: value ? Number(value) : undefined });
          }}
        />
      </div>

      <div className="overview-section">
        <div className="overview-header">
          <span>{t('excerpt')}</span>
          <Tooltip title={t('excerptTooltip')}>
            <InfoCircleOutlined
              style={{ marginLeft: '5px', color: '#1890ff' }}
            />
          </Tooltip>
          <span className="char-count">{excerpt?.length}/140</span>
        </div>
        <TextArea
          value={excerpt}
          onChange={handleExcerptChange}
          placeholder="VD: Món mì ống nhanh chóng và dễ dàng này rất phù hợp cho những bữa ăn gia đình bận rộn vào cuối tuần."
          rows={4}
          maxLength={140}
          className="textarea-full"
        />
      </div>

      <div className="overview-section">
        <div className="overview-header">
          <span>{t('relatedPosts')}</span>
          <Tooltip title={t('relatedPostsTooltip')}>
            <InfoCircleOutlined
              style={{ marginLeft: '5px', color: '#1890ff' }}
            />
          </Tooltip>
          <span className="post-count">{relatedPosts.length}/3</span>
        </div>
        <Button
          icon={<PlusOutlined />}
          className="select-post-btn"
          onClick={handleOpenBlogRelated}
        >
          {t('selectPosts')}
        </Button>

        {/* Display selected related posts */}
        {relatedPosts.length > 0 && (
          <div style={{ marginTop: '12px' }}>
            {relatedPosts.map((post, index) => (
              <div
                key={post.post_id || index}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  fontSize: '12px',
                }}
              >
                <span
                  style={{
                    flex: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {post.title}
                </span>
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => {
                    const newPosts = relatedPosts.filter((_, i) => i !== index);
                    const newIds = newPosts
                      .map((p) => p.post_id)
                      .filter(Boolean) as number[];
                    setRelatedPosts(newPosts);
                    setRelatedPostIds(newIds);
                    updateDrawerData({ related_post_ids: newIds });
                  }}
                  style={{ marginLeft: '8px' }}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="overview-section">
        <div className="overview-header">
          <span>{t('featuredPost')}</span>
          <Tooltip title={t('featuredPostTooltip')}>
            <InfoCircleOutlined
              style={{ marginLeft: '5px', color: '#1890ff' }}
            />
          </Tooltip>
          <Switch
            checked={isFeatured}
            onChange={handleFeaturedChange}
            className="overview-switch"
          />
        </div>
      </div>

      <div className="overview-section">
        <div className="overview-header">
          <span>{t('allowComments')}</span>
          <Tooltip title={t('allowCommentsTooltip')}>
            <InfoCircleOutlined
              style={{ marginLeft: '5px', color: '#1890ff' }}
            />
          </Tooltip>
          <Switch
            checked={allowComments}
            onChange={handleAllowCommentsChange}
            className="overview-switch"
          />
        </div>
      </div>
    </div>
  );

  const renderCategoryContent = () => (
    <div>
      <p className="tag-description">{t('categoryDescription')}</p>
      <div className="category-container">
        <div className="category-attach">
          <span>{t('attachCategory')}</span>
          <Tooltip title={t('categoryTooltip')}>
            <InfoCircleOutlined
              style={{ marginLeft: '5px', color: '#1890ff' }}
            />
          </Tooltip>
        </div>
        <div className="category-list">
          {categories.map((category) => (
            <div key={category.id} className="category-item">
              <Checkbox
                checked={selectedCategories.includes(String(category.id))}
                onChange={(e) =>
                  handleCategoryChange(category.id, e.target.checked)
                }
              >
                {category.name}
              </Checkbox>
            </div>
          ))}
        </div>
        <div className="category-actions">
          <Button
            type="text"
            icon={<PlusOutlined />}
            className="create-category-btn"
          >
            {t('createCategory')}
          </Button>
        </div>
      </div>
    </div>
  );

  const renderTagContent = () => (
    <div>
      <p className="tag-description">{t('tagDescription')}</p>
      <TagInput tags={tagNames} onChange={handleTagsChange} maxTags={30} />
    </div>
  );

  const renderSettingsContent = () => (
    <div className="settings-container">
      <div className="settings-section">
        <div className="settings-header">
          <span>Hiển thị</span>
        </div>
        <Select
          value={visibility}
          onChange={handleVisibilityChange}
          style={{ width: '100%' }}
          placeholder="Chọn hiển thị"
        >
          <Select.Option value="public">Công khai</Select.Option>
          <Select.Option value="private">Riêng tư</Select.Option>
          <Select.Option value="password_protected">
            Bảo vệ mật khẩu
          </Select.Option>
        </Select>
      </div>

      <div className="settings-section">
        <div className="settings-header">
          <span>Bình luận</span>
        </div>
        <Select
          value={commentStatus}
          onChange={handleCommentStatusChange}
          style={{ width: '100%' }}
          placeholder="Trạng thái bình luận"
        >
          <Select.Option value="open">Cho phép bình luận</Select.Option>
          <Select.Option value="closed">Không cho phép bình luận</Select.Option>
        </Select>
      </div>

      <div className="settings-section">
        <div className="settings-header">
          <span>Mật khẩu bảo vệ</span>
        </div>
        <Input.Password
          value={password}
          onChange={handlePasswordChange}
          placeholder="Nhập mật khẩu (nếu cần)"
        />
      </div>
    </div>
  );

  const tabItems = [
    {
      key: 'overview',
      label: t('overview'),
      children: renderOverviewContent(),
    },
    {
      key: 'category',
      label: t('category'),
      children: renderCategoryContent(),
    },
    {
      key: 'tag',
      label: t('tag'),
      children: renderTagContent(),
    },
    {
      key: 'settings',
      label: 'Cài đặt bài viết',
      children: renderSettingsContent(),
    },
  ];

  // Xử lý khi đóng drawer
  const handleCloseDrawer = () => {
    // Lưu dữ liệu trước khi đóng
    updateDataToStore();
    onClose();
  };

  return (
    <Drawer
      title={t('setupPost')}
      placement="right"
      onClose={handleCloseDrawer}
      open={visible}
      width={400}
    >
      {loading ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
          }}
        >
          <Spin size="large" tip={t('loading')} />
        </div>
      ) : (
        <Tabs
          defaultActiveKey="overview"
          activeKey={activeTab}
          onChange={handleTabChange}
          items={tabItems}
        />
      )}

      {/* BlogRelated Modal */}
      <BlogRelated
        visible={blogRelatedVisible}
        onClose={handleCloseBlogRelated}
        onConfirm={handleRelatedPostsConfirm}
        selectedBlogIds={relatedPostIds}
        excludeBlogIds={
          blogData?.post_id
            ? ([blogData.post_id].filter(Boolean) as number[])
            : []
        }
        title="Chọn bài đăng liên quan"
        mode="select"
      />
    </Drawer>
  );
};

export default BlogSetupDrawer;
