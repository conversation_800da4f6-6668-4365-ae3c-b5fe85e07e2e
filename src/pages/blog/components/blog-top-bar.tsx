import {
  ArrowLeftOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  EllipsisOutlined,
  EyeOutlined,
  FileTextOutlined,
  GlobalOutlined,
  LockOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Dropdown,
  Space,
  Switch,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { MODULE } from '../config';
import { Blog, BLOG_STATUS, BLOG_STATUS_LABELS, VISIBILITY } from '../type';
import BlogNote from './blog-note';

const { Text } = Typography;

interface BlogTopBarProps {
  blog?: Blog;
  isNew?: boolean;
  onSave?: () => void;
  onPreview?: () => void;
  onPublish?: () => void;
  onSchedule?: () => void;
  onDraft?: () => void;
  onOptions?: () => void;
  onAddNote?: (content: string) => void;
  onEditNote?: (id: string, content: string) => void;
  onDeleteNote?: (id: string) => void;
  notes?: Array<{
    id: string;
    author: { name: string; avatar?: string };
    content: string;
    createdAt: string;
  }>;
}

export const BlogTopBar: FC<BlogTopBarProps> = ({
  blog,
  isNew = true,
  onSave,
  onPreview,
  onPublish,
  onSchedule,
  onDraft,
  onOptions,
  onAddNote,
  onEditNote,
  onDeleteNote,
  notes,
}) => {
  const { t } = useTranslation(MODULE);
  const [saving, setSaving] = useState<boolean>(false);
  const [autoSave, setAutoSave] = useState<boolean>(true);
  const [noteVisible, setNoteVisible] = useState<boolean>(false);

  // Get status display info using proper enums
  const getStatusInfo = () => {
    if (!blog)
      return { color: 'default', text: BLOG_STATUS_LABELS[BLOG_STATUS.DRAFT] };

    const status = blog.status as BLOG_STATUS;

    switch (status) {
      case BLOG_STATUS.PUBLISHED:
        return {
          color: 'success',
          text: BLOG_STATUS_LABELS[BLOG_STATUS.PUBLISHED],
        };
      case BLOG_STATUS.SCHEDULE:
        return {
          color: 'processing',
          text: BLOG_STATUS_LABELS[BLOG_STATUS.SCHEDULE],
        };
      case BLOG_STATUS.DRAFT:
        return {
          color: 'default',
          text: BLOG_STATUS_LABELS[BLOG_STATUS.DRAFT],
        };
      case BLOG_STATUS.PENDING:
        return {
          color: 'warning',
          text: BLOG_STATUS_LABELS[BLOG_STATUS.PENDING],
        };
      case BLOG_STATUS.APPROVED:
        return {
          color: 'processing',
          text: BLOG_STATUS_LABELS[BLOG_STATUS.APPROVED],
        };
      case BLOG_STATUS.RETURN:
        return { color: 'error', text: BLOG_STATUS_LABELS[BLOG_STATUS.RETURN] };
      case BLOG_STATUS.TRASH:
        return {
          color: 'default',
          text: BLOG_STATUS_LABELS[BLOG_STATUS.TRASH],
        };
      default:
        return {
          color: 'default',
          text: BLOG_STATUS_LABELS[BLOG_STATUS.DRAFT],
        };
    }
  };

  const statusInfo = getStatusInfo();

  // Handle save with animation
  const handleSave = () => {
    setSaving(true);

    // Call the actual save function
    if (onSave) {
      onSave();
    }

    // Simulate save completion after 1 second
    setTimeout(() => {
      setSaving(false);
    }, 1000);
  };

  // Handle auto-save toggle
  const handleAutoSaveChange = (checked: boolean) => {
    setAutoSave(checked);
    // Add actual auto-save logic here
  };

  // Actions menu items
  const menu = {
    items: [
      ...(blog?.status !== BLOG_STATUS.PUBLISHED
        ? [
            {
              key: 'publish',
              icon: <CheckCircleOutlined />,
              label: t('publishNow'),
              onClick: onPublish,
            },
          ]
        : []),
      {
        key: 'schedule',
        icon: <ClockCircleOutlined />,
        label: t('schedule'),
        onClick: onSchedule,
      },
      {
        key: 'draft',
        icon: <EditOutlined />,
        label: t('saveDraft'),
        onClick: onDraft,
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'preview',
        icon: <EyeOutlined />,
        label: t('preview'),
        onClick: onPreview,
      },
    ],
  };

  // Xử lý sự kiện khi nhấn nút xuất bản
  const handlePublishClick = () => {
    if (blog?.status === BLOG_STATUS.PUBLISHED) {
      onPublish();
    } else {
      // Nếu chưa xuất bản, hiển thị dropdown menu
      // Dropdown menu sẽ tự hiển thị do trigger={['click']}
    }
  };

  // More options menu items
  const moreMenu = {
    items: [
      {
        key: 'duplicate',
        label: t('duplicate'),
      },
      {
        key: 'archive',
        label: t('archive'),
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'delete',
        danger: true,
        icon: <DeleteOutlined />,
        label: t('delete'),
      },
    ],
  };

  return (
    <div className="blog-top-bar bg-white shadow-sm px-4 py-2 flex items-center justify-between sticky top-0 z-10 border-b border-gray-200">
      {/* Left section */}
      <div className="flex items-center space-x-3">
        <Link to="/blog" className="text-gray-600 hover:text-primary">
          <Button type="text" icon={<ArrowLeftOutlined />} />
        </Link>

        <div className="flex flex-col">
          <Text className="text-sm text-gray-500">
            {isNew ? t('creatingPost') : t('editingPost')}
          </Text>
          <Text
            className="text-lg font-medium truncate max-w-md"
            title={blog?.title ?? t('untitled')}
          >
            {blog?.title ?? t('untitled')}
          </Text>
        </div>
      </div>

      {/* Center section */}
      <div className="flex items-center space-x-3">
        <Space>
          <Badge status={statusInfo.color as any} text={statusInfo.text} />

          {blog?.publishedAt && (
            <Tooltip title={dayjs(blog.publishedAt).format('DD/MM/YYYY HH:mm')}>
              <Tag icon={<CalendarOutlined />} color="blue">
                {dayjs(blog.publishedAt).format('DD/MM/YYYY')}
              </Tag>
            </Tooltip>
          )}

          {blog?.visibility && (
            <Tooltip
              title={`Hiển thị: ${blog.visibility === VISIBILITY.PUBLIC ? 'Công khai' : blog.visibility === VISIBILITY.PRIVATE ? 'Riêng tư' : 'Bảo vệ bằng mật khẩu'}`}
            >
              <Tag
                icon={
                  blog.visibility === VISIBILITY.PUBLIC ? (
                    <GlobalOutlined />
                  ) : (
                    <LockOutlined />
                  )
                }
              >
                {blog.visibility === VISIBILITY.PUBLIC
                  ? 'Công khai'
                  : blog.visibility === VISIBILITY.PRIVATE
                    ? 'Riêng tư'
                    : 'Bảo vệ'}
              </Tag>
            </Tooltip>
          )}
        </Space>
      </div>

      {/* Right section */}
      <div className="flex items-center space-x-2">
        <Space>
          <Tooltip title={t('autoSave')}>
            <Switch
              checkedChildren={t('autoSaveOn')}
              unCheckedChildren={t('autoSaveOff')}
              size="small"
              checked={autoSave}
              onChange={handleAutoSaveChange}
            />
          </Tooltip>

          <Button onClick={handleSave} loading={saving} icon={<SaveOutlined />}>
            {t('save')}
          </Button>

          <Dropdown menu={menu} trigger={['click']}>
            <Button type="primary" onClick={handlePublishClick}>
              {blog?.status === BLOG_STATUS.PUBLISHED ? 'Cập nhật' : 'Xuất bản'}{' '}
              <DownOutlined />
            </Button>
          </Dropdown>

          <Tooltip title={t('preview')}>
            <Button icon={<EyeOutlined />} onClick={onPreview} />
          </Tooltip>

          <Tooltip title="Ghi chú">
            <Button
              icon={<FileTextOutlined />}
              onClick={() => setNoteVisible(true)}
            />
          </Tooltip>

          <Tooltip title={t('moreOptions')}>
            <Dropdown menu={moreMenu} trigger={['click']}>
              <Button icon={<EllipsisOutlined />} />
            </Dropdown>
          </Tooltip>
        </Space>
      </div>

      {/* Blog Note Drawer */}
      <BlogNote
        visible={noteVisible}
        onClose={() => setNoteVisible(false)}
        notes={notes}
        onAddNote={onAddNote}
        onEditNote={onEditNote}
        onDeleteNote={onDeleteNote}
      />
    </div>
  );
};

export default BlogTopBar;
