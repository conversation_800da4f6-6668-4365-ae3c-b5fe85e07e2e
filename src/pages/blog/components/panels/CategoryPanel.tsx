import React from 'react';
import { Col, Form } from 'antd';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../../config';
import { SelectBlogCategoryTree } from '../../../blog-category/components';

const FormItem = Form.Item;

interface CategoryPanelProps {
  form: any;
}

const CategoryPanel: React.FC<CategoryPanelProps> = ({ form }) => {
  const { t } = useTranslation(MODULE);

  return (
    <>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Danh mục chính')}
          name="categoryId"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <SelectBlogCategoryTree multiple={false} />
        </FormItem>
      </Col>

      <Col xs={24} lg={24}>
        <FormItem
          label={t('Danh mục con')}
          name="category_ids"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <SelectBlogCategoryTree multiple />
        </FormItem>
      </Col>
    </>
  );
};

export default CategoryPanel;
