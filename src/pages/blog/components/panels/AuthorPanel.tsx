import React from 'react';
import { Col, Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../../config';
import { SelectUser } from '../../../user/components';

const FormItem = Form.Item;

interface AuthorPanelProps {
  form: any;
}

const AuthorPanel: React.FC<AuthorPanelProps> = ({ form }) => {
  const { t } = useTranslation(MODULE);

  return (
    <>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Người viết (Mặc định tài khoản đăng nhập)')}
          name="authorId"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <SelectUser />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Tác giả')}
          name="authorsName"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <Input />
        </FormItem>
      </Col>
    </>
  );
};

export default AuthorPanel;
