import React from 'react';
import { Col } from 'antd';
import ImageUpload from '../../../../components/upload/image-upload';
import { MediaFile } from '../../../../components/upload/type';

interface ImagePanelProps {
  value?: string;
  onChange: (file: MediaFile) => void;
}

const ImagePanel: React.FC<ImagePanelProps> = ({ value, onChange }) => {
  return (
    <Col xs={24} lg={24}>
      <ImageUpload onChange={onChange} value={value} />
    </Col>
  );
};

export default ImagePanel;
