import React from 'react';
import { Col, Form } from 'antd';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../../config';
import { SelectBlogTag } from '../../../blog-tag/components';

const FormItem = Form.Item;

interface TagPanelProps {
  form: any;
}

const TagPanel: React.FC<TagPanelProps> = ({ form }) => {
  const { t } = useTranslation(MODULE);

  return (
    <Col xs={24} lg={24}>
      <FormItem
        label={t('Từ khóa')}
        name="tag_ids"
        rules={[{ required: false, message: t('pleaseEnterData') }]}
      >
        <SelectBlogTag />
      </FormItem>
    </Col>
  );
};

export default TagPanel;
