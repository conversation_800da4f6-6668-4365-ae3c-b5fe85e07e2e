import React from 'react';
import { Col, Form, Input, Checkbox } from 'antd';
import { useTranslation } from 'react-i18next';
import { MODULE } from '../../config';

const FormItem = Form.Item;

interface SourcePanelProps {
  form: any;
}

const SourcePanel: React.FC<SourcePanelProps> = ({ form }) => {
  const { t } = useTranslation(MODULE);

  return (
    <>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Nguồn dẫn báo/tạp chí giấy')}
          name="source"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <Input />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Link dẫn nguồn')}
          name="sourceLink"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <Input />
        </FormItem>
      </Col>
      <Col xs={24} lg={24}>
        <FormItem
          label={t('Tắt "Nguồn dẫn"')}
          name="sourceHide"
          rules={[{ required: false, message: t('pleaseEnterData') }]}
        >
          <Checkbox
            onChange={(e) => console.log('Checked:', e.target.checked)}
          />
        </FormItem>
      </Col>
    </>
  );
};

export default SourcePanel;
