import React, { useState } from 'react';
import {
  Drawer,
  Avatar,
  Typography,
  Button,
  Input,
  List,
  Dropdown,
  Space,
  Divider,
} from 'antd';
import {
  MoreOutlined,
  CheckOutlined,
  CaretDownOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import 'dayjs/locale/vi';
import './blog-note.scss';

const { Text, Title } = Typography;
const { TextArea } = Input;

interface Note {
  id: string;
  author: {
    name: string;
    avatar?: string;
  };
  content: string;
  createdAt: string;
  isEditing?: boolean;
}

interface BlogNoteProps {
  visible: boolean;
  onClose: () => void;
  notes?: Note[];
  onAddNote?: (content: string) => void;
  onEditNote?: (id: string, content: string) => void;
  onDeleteNote?: (id: string) => void;
}

const BlogNote: React.FC<BlogNoteProps> = ({
  visible,
  onClose,
  notes = [],
  onAddNote,
  onEditNote,
  onDeleteNote,
}) => {
  const [newNoteContent, setNewNoteContent] = useState('');
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');

  // Mock data for demonstration
  const mockNotes: Note[] = [
    {
      id: '1',
      author: {
        name: 'Thanh Loi',
        avatar: '/api/placeholder/32/32',
      },
      content: 'eee',
      createdAt: '1 ngày trước',
    },
    {
      id: '2',
      author: {
        name: 'Thanh Loi',
        avatar: '/api/placeholder/32/32',
      },
      content: 'abccc',
      createdAt: '1 ngày trước',
    },
    {
      id: '3',
      author: {
        name: 'Thanh Loi',
        avatar: '/api/placeholder/32/32',
      },
      content: 'abc',
      createdAt: '1 ngày trước',
    },
  ];

  const displayNotes = notes.length > 0 ? notes : mockNotes;

  const handleAddNote = () => {
    if (newNoteContent.trim()) {
      if (onAddNote) {
        onAddNote(newNoteContent);
      }
      setNewNoteContent('');
    }
  };

  const handleEditNote = (note: Note) => {
    setEditingNoteId(note.id);
    setEditContent(note.content);
  };

  const handleSaveEdit = () => {
    if (editingNoteId && editContent.trim()) {
      if (onEditNote) {
        onEditNote(editingNoteId, editContent);
      }
      setEditingNoteId(null);
      setEditContent('');
    }
  };

  const handleCancelEdit = () => {
    setEditingNoteId(null);
    setEditContent('');
  };

  const handleDeleteNote = (noteId: string) => {
    if (onDeleteNote) {
      onDeleteNote(noteId);
    }
  };

  const getMenuItems = (note: Note) => [
    {
      key: 'edit',
      label: 'Chỉnh sửa',
      onClick: () => handleEditNote(note),
    },
    {
      key: 'delete',
      label: 'Xóa',
      onClick: () => handleDeleteNote(note.id),
    },
  ];

  return (
    <Drawer
      className="blog-note-drawer"
      title={
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Title level={5} style={{ margin: 0 }}>
              Ghi chú
            </Title>
            <CaretDownOutlined style={{ fontSize: '12px', color: '#666' }} />
          </div>
        </div>
      }
      open={visible}
      onClose={onClose}
      placement="right"
      width={400}
      styles={{
        body: {
          padding: '16px',
        },
      }}
    >
      <div>
        {/* Filter/Sort Section */}
        <div style={{ marginBottom: '16px' }}>
          <Button
            type="link"
            size="small"
            style={{
              padding: 0,
              height: 'auto',
              color: '#1890ff',
              fontSize: '12px',
            }}
          >
            Mở <CaretDownOutlined style={{ fontSize: '10px' }} />
          </Button>
        </div>

        {/* Notes List */}
        <List
          dataSource={displayNotes}
          renderItem={(note) => (
            <List.Item
              style={{
                padding: '12px 0',
                border: 'none',
                borderBottom: '1px solid #f0f0f0',
              }}
            >
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '8px',
                  }}
                >
                  <Avatar
                    size={32}
                    src={note.author.avatar}
                    style={{ flexShrink: 0 }}
                  >
                    {note.author.name.charAt(0)}
                  </Avatar>

                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '4px',
                      }}
                    >
                      <Text strong style={{ fontSize: '13px' }}>
                        {note.author.name}
                      </Text>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                        }}
                      >
                        {editingNoteId === note.id && (
                          <Button
                            type="text"
                            size="small"
                            icon={<CheckOutlined />}
                            onClick={handleSaveEdit}
                            style={{ padding: '2px' }}
                          />
                        )}
                        <Dropdown
                          menu={{ items: getMenuItems(note) }}
                          trigger={['click']}
                          placement="bottomRight"
                        >
                          <Button
                            type="text"
                            size="small"
                            icon={<MoreOutlined />}
                            style={{ padding: '2px' }}
                          />
                        </Dropdown>
                      </div>
                    </div>

                    <Text
                      type="secondary"
                      style={{
                        fontSize: '11px',
                        display: 'block',
                        marginBottom: '8px',
                      }}
                    >
                      {note.createdAt}
                    </Text>

                    {editingNoteId === note.id ? (
                      <TextArea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        autoSize={{ minRows: 1, maxRows: 4 }}
                        style={{ fontSize: '13px' }}
                        onPressEnter={(e) => {
                          if (e.shiftKey) return;
                          e.preventDefault();
                          handleSaveEdit();
                        }}
                        onBlur={handleCancelEdit}
                        autoFocus
                      />
                    ) : (
                      <Text
                        style={{
                          fontSize: '13px',
                          wordBreak: 'break-word',
                        }}
                      >
                        {note.content}
                      </Text>
                    )}
                  </div>
                </div>
              </div>
            </List.Item>
          )}
        />

        <Divider style={{ margin: '16px 0' }} />

        {/* Add New Note */}
        <div>
          <TextArea
            value={newNoteContent}
            onChange={(e) => setNewNoteContent(e.target.value)}
            placeholder="Viết ghi chú hoặc @để cập..."
            autoSize={{ minRows: 2, maxRows: 4 }}
            style={{
              fontSize: '13px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
            }}
            onPressEnter={(e) => {
              if (e.shiftKey) return;
              e.preventDefault();
              handleAddNote();
            }}
          />

          {newNoteContent.trim() && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '8px',
              }}
            >
              <Space>
                <Button size="small" onClick={() => setNewNoteContent('')}>
                  Hủy
                </Button>
                <Button type="primary" size="small" onClick={handleAddNote}>
                  Gửi
                </Button>
              </Space>
            </div>
          )}
        </div>
      </div>
    </Drawer>
  );
};

export default BlogNote;
