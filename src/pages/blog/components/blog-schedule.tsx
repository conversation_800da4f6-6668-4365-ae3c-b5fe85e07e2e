import React, { useState } from 'react';
import { Modal, Calendar, TimePicker, Button, Typography, Space } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/vi';

const { Title, Text } = Typography;

interface BlogScheduleProps {
  visible: boolean;
  onClose: () => void;
  onSchedule: (dateTime: Dayjs) => void;
  initialDateTime?: Dayjs;
}

const BlogSchedule: React.FC<BlogScheduleProps> = ({
  visible,
  onClose,
  onSchedule,
  initialDateTime,
}) => {
  const [selectedDate, setSelectedDate] = useState<Dayjs>(
    initialDateTime || dayjs(),
  );
  const [selectedTime, setSelectedTime] = useState<Dayjs>(
    initialDateTime || dayjs().hour(22).minute(3),
  );

  const handleDateChange = (date: Dayjs) => {
    setSelectedDate(date);
  };

  const onPanelChange = (value: Dayjs, mode: string) => {
    console.log('Panel changed:', value.format('YYYY-MM-DD'), mode);
  };

  const handleTimeChange = (time: Dayjs | null) => {
    if (time) {
      setSelectedTime(time);
    }
  };

  const handleSchedule = () => {
    const scheduledDateTime = selectedDate
      .hour(selectedTime.hour())
      .minute(selectedTime.minute())
      .second(0);

    onSchedule(scheduledDateTime);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Modal
      title={
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            color: 'white',
          }}
        >
          <Title level={4} style={{ margin: 0, color: 'white' }}>
            Lên lịch bài đăng
          </Title>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
            style={{ color: 'white' }}
          />
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      closable={false}
      styles={{
        header: {
          background: '#4A90E2',
          borderBottom: 'none',
          padding: '16px 24px',
        },
        body: {
          padding: '24px',
        },
      }}
    >
      <div>
        <div style={{ marginBottom: '24px' }}>
          <Text style={{ fontSize: '16px', color: '#333' }}>
            Khi nào bạn muốn xuất bản bài đăng của mình?
          </Text>
        </div>

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong style={{ display: 'block', marginBottom: '8px' }}>
              Ngày xuất bản
            </Text>
            <Calendar
              fullscreen={false}
              value={selectedDate}
              onChange={handleDateChange}
              onPanelChange={onPanelChange}
            />
          </div>

          <div>
            <Text strong style={{ display: 'block', marginBottom: '8px' }}>
              Thời gian
            </Text>
            <TimePicker
              value={selectedTime}
              onChange={handleTimeChange}
              format="HH:mm"
              size="large"
              style={{ width: '100%' }}
              placeholder="Chọn giờ"
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Múi giờ: (GMT +07:00) Asia/Saigon
              </Text>
            </div>
          </div>
        </Space>

        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '12px',
            marginTop: '32px',
            paddingTop: '16px',
            borderTop: '1px solid #f0f0f0',
          }}
        >
          <Button onClick={handleCancel} size="large">
            Hủy
          </Button>
          <Button
            type="primary"
            onClick={handleSchedule}
            size="large"
            style={{
              background: '#4A90E2',
              borderColor: '#4A90E2',
            }}
          >
            Lên lịch
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default BlogSchedule;
