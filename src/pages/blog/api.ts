import ConsoleService from 'services/console.service';
import {
  ApiResponse,
  ApiResponsePaginationCursor,
  apiService,
} from '../../services/api.service';
import { Blog } from './type';
// Status Statistics Interface
export interface StatusCounts {
  draft: number;
  pending: number;
  approved: number;
  schedule: number;
  published: number;
  return: number;
  trash: number;
  storage: number;
  request: number;
  auto: number;
  delete: number;
  total: number;
}

export interface StatusStatistics {
  status_counts: StatusCounts;
}

export const MODULE = 'blog';
export const MODULE_NAME = 'Blog Management';
export const MODULE_POPUP = false;
const logger = ConsoleService.register(MODULE);

const postsUrl = `/api/admin/v1/blog/posts`;
const relatedPostsUrl = `/api/admin/v1/blog/related-posts`;

// Posts API Functions
export async function getItems(
  params: any,
): Promise<ApiResponsePaginationCursor<Blog[]>> {
  logger('getItems', params);
  const response = await apiService.get<ApiResponsePaginationCursor<Blog[]>>(
    postsUrl,
    { params: params },
  );
  return response.data;
}

export async function getItem(id: string): Promise<ApiResponse<Blog>> {
  const response = await apiService.get<ApiResponse<Blog>>(`${postsUrl}/${id}`);
  return response.data;
}

export async function getItemBySlug(slug: string): Promise<ApiResponse<Blog>> {
  const response = await apiService.get<ApiResponse<Blog>>(
    `${postsUrl}/slug/${slug}`,
  );
  return response.data;
}

export async function createItem(payload: any): Promise<ApiResponse<Blog>> {
  const response = await apiService.post<ApiResponse<Blog>>(postsUrl, payload);
  return response.data;
}

export async function updateItem(
  id: string,
  payload: any,
): Promise<ApiResponse<Blog>> {
  const response = await apiService.put<ApiResponse<Blog>>(
    `${postsUrl}/${id}`,
    payload,
  );
  return response.data;
}

export async function deleteItem(id: string): Promise<ApiResponse<Blog>> {
  const response = await apiService.delete<ApiResponse<Blog>>(
    `${postsUrl}/${id}`,
  );
  return response.data;
}

export async function bulkDeleteItems(
  ids: string[],
): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${postsUrl}/bulk-delete`,
    { ids },
  );
  return response.data;
}

export async function publishPost(id: string): Promise<ApiResponse<Blog>> {
  const response = await apiService.post<ApiResponse<Blog>>(
    `${postsUrl}/${id}/publish`,
  );
  return response.data;
}

export async function exportPosts(params: any): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `${postsUrl}/export`,
    params,
  );
  return response.data;
}

export async function importPosts(payload: any): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${postsUrl}/import`,
    payload,
  );
  return response.data;
}

export async function searchPosts(params: any): Promise<ApiResponse<Blog[]>> {
  const response = await apiService.get<ApiResponse<Blog[]>>(
    `${postsUrl}/search`,
    params,
  );
  return response.data;
}

// Legacy compatibility functions
export async function getOptions(): Promise<ApiResponse<Blog[]>> {
  return getItems({ limit: 100 });
}

export async function getSearch(params: any): Promise<ApiResponse<Blog[]>> {
  return searchPosts(params);
}

export async function getAll(): Promise<ApiResponse<Blog[]>> {
  return getItems({ limit: 1000 });
}

// Related Posts API Functions
export async function getRelatedPosts(
  params: any,
): Promise<ApiResponsePaginationCursor<any[]>> {
  const response = await apiService.get<ApiResponsePaginationCursor<any[]>>(
    relatedPostsUrl,
    { params },
  );
  return response.data;
}

// Get related posts for a specific post (based on API docs)
export async function getPostRelatedPosts(
  postId: string,
  params?: any,
): Promise<ApiResponse<any[]>> {
  const response = await apiService.get<ApiResponse<any[]>>(
    `${postsUrl}/${postId}/related-posts`,
    { params },
  );
  return response.data;
}

// Update related posts for a specific post (based on API docs)
export async function updatePostRelatedPosts(
  postId: string,
  payload: {
    related_post_ids: number[];
    is_bidirectional?: boolean;
  },
): Promise<ApiResponse<any[]>> {
  const response = await apiService.post<ApiResponse<any[]>>(
    `${postsUrl}/${postId}/related`,
    payload,
  );
  return response.data;
}

export async function createRelatedPost(payload: {
  post_id: number;
  related_post_id: number;
  priority?: number;
  is_bidirectional?: boolean;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    relatedPostsUrl,
    payload,
  );
  return response.data;
}

export async function bulkCreateRelatedPosts(payload: {
  post_id: number;
  is_bidirectional?: boolean;
  related_posts: Array<{
    related_post_id: number;
    priority?: number;
  }>;
}): Promise<ApiResponse<any>> {
  const response = await apiService.post<ApiResponse<any>>(
    `${relatedPostsUrl}/bulk`,
    payload,
  );
  return response.data;
}

export async function updateRelatedPost(
  relationId: string,
  payload: {
    priority?: number;
    is_bidirectional?: boolean;
  },
): Promise<ApiResponse<any>> {
  const response = await apiService.put<ApiResponse<any>>(
    `${relatedPostsUrl}/${relationId}`,
    payload,
  );
  return response.data;
}

export async function deleteRelatedPost(
  relationId: string,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${relatedPostsUrl}/${relationId}`,
  );
  return response.data;
}

// Status Statistics API Function
export async function getStatusStatistics(): Promise<
  ApiResponse<StatusStatistics>
> {
  const response = await apiService.get<ApiResponse<StatusStatistics>>(
    '/api/admin/v1/blog/statistics/status',
  );
  return response.data;
}

// SEO Data API Function
export async function fetchSEOData(postId: string): Promise<ApiResponse<any>> {
  const response = await apiService.get<ApiResponse<any>>(
    `/api/admin/v1/blog/posts/${postId}/seo`,
  );
  return response.data;
}
