import { Dayjs } from 'dayjs';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useShallow } from 'zustand/shallow';
import ConsoleService from '../../services/console.service';
import { deleteItem, getOptions } from './api';
import { MODULE } from './config';
import { Blog, BlogDrawerData, extractDrawerFields } from './type';
const logger = ConsoleService.register(MODULE);

export interface StoreState {
  // UI State - tách riêng để tránh re-render không cần thiết
  ui: {
    loading: boolean;
    drawerVisible: boolean;
    scheduleVisible: boolean;
  };

  // Data State - quản lý dữ liệu chính
  data: {
    items: Blog[];
    currentBlog?: Blog;
    options: any[];
  };

  // Form State - tách riêng để tối ưu performance
  form: {
    formValues?: Blog;
    drawerData?: BlogDrawerData;
    scheduledDateTime?: Dayjs | null;
  };

  // Batch update actions
  setUIState: (state: Partial<StoreState['ui']>) => void;
  setDataState: (state: Partial<StoreState['data']>) => void;
  setFormState: (state: Partial<StoreState['form']>) => void;

  // Legacy compatibility methods
  setLoading: (loading: boolean) => void;
  delete: (id: string) => void;
  setOptions: (options: any) => void;
  deleteItem: (id: string) => Promise<void>;
  getOptions: () => Promise<void>;
  updateDrawerData: (data: Partial<Blog>) => void;

  // Optimized specific actions
  updateCurrentBlog: (blog: Partial<Blog>) => void;
  updateFormValues: (values: Partial<Blog>) => void;

  // Memoized selectors
  selectors: {
    getFormFields: () => Partial<Blog>;
    getDrawerFields: () => BlogDrawerData;
    isLoading: () => boolean;
  };
}

const useBlogStore = create<StoreState>()(
  devtools(
    (set, get) => ({
      // Initial state structure
      ui: {
        loading: false,
        drawerVisible: false,
        scheduleVisible: false,
      },

      data: {
        items: [],
        currentBlog: undefined,
        options: [],
      },

      form: {
        formValues: undefined,
        drawerData: undefined,
        scheduledDateTime: null,
      },

      // Batch UI updates
      setUIState: (newState) =>
        set((state) => ({
          ui: { ...state.ui, ...newState },
        })),

      // Batch data updates
      setDataState: (newState) =>
        set((state) => ({
          data: { ...state.data, ...newState },
        })),

      // Batch form updates
      setFormState: (newState) =>
        set((state) => ({
          form: { ...state.form, ...newState },
        })),

      // Legacy compatibility methods
      setLoading: (loading: boolean) => {
        set((state) => ({
          ui: { ...state.ui, loading },
        }));
      },

      delete: (id: string) => {
        set((state) => {
          const itemsNew = state.data.items.filter(
            (b: Blog) => String(b.post_id) !== id,
          );
          return {
            data: { ...state.data, items: itemsNew },
          };
        });
      },

      setOptions: (options: any) => {
        set((state) => ({
          data: { ...state.data, options },
        }));
      },

      deleteItem: async (id: string) => {
        set((state) => ({
          ui: { ...state.ui, loading: true },
        }));

        await deleteItem(id);

        set((state) => {
          const itemsNew = state.data.items.filter(
            (b: Blog) => String(b.post_id) !== id,
          );
          return {
            ui: { ...state.ui, loading: false },
            data: { ...state.data, items: itemsNew },
          };
        });
      },

      getOptions: async () => {
        const response = await getOptions();
        set((state) => ({
          data: { ...state.data, options: response.data },
        }));
      },

      updateDrawerData: (data: Partial<Blog>) => {
        logger('updateDrawerData - input:', data);

        // Extract only allowed drawer fields
        const safeDrawerData = extractDrawerFields(data);
        logger('updateDrawerData - filtered:', safeDrawerData);

        set((state) => ({
          form: {
            ...state.form,
            drawerData: { ...state.form.drawerData, ...safeDrawerData },
          },
        }));
      },

      // Optimized blog update - chỉ update field thay đổi
      updateCurrentBlog: (blogUpdate: Partial<Blog>) =>
        set((state) => ({
          data: {
            ...state.data,
            currentBlog: state.data.currentBlog
              ? { ...state.data.currentBlog, ...blogUpdate }
              : (blogUpdate as Blog),
          },
        })),

      // Optimized form values update
      updateFormValues: (values: Partial<Blog>) =>
        set((state) => ({
          form: {
            ...state.form,
            formValues: state.form.formValues
              ? { ...state.form.formValues, ...values }
              : (values as Blog),
          },
        })),

      // Memoized selectors
      selectors: {
        getFormFields: () => {
          const state = get();
          return state.form.formValues || {};
        },

        getDrawerFields: () => {
          const state = get();
          const blog = state.data.currentBlog;
          if (!blog) return {};

          return extractDrawerFields(blog);
        },

        isLoading: () => {
          const state = get();
          return state.ui.loading;
        },
      },
    }),
    {
      name: 'blog-store',
      enabled: process.env.NODE_ENV === 'development',
    },
  ),
);

// Custom hooks với shallow comparison
export const useBlogUI = () => useBlogStore(useShallow((state) => state.ui));

export const useBlogData = () =>
  useBlogStore(useShallow((state) => state.data));

export const useBlogForm = () =>
  useBlogStore(useShallow((state) => state.form));

// Specific selectors để tránh re-render
export const useCurrentBlog = () =>
  useBlogStore((state) => state.data.currentBlog);

export const useFormValues = () =>
  useBlogStore((state) => state.form.formValues);

export const useLoading = () => useBlogStore((state) => state.ui.loading);

export const useDrawerData = () =>
  useBlogStore((state) => state.form.drawerData);

export default useBlogStore;
