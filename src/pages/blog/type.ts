import dayjs, { Dayjs } from 'dayjs';
import { BlogCategory } from '../blog-category/type';
import { SeoMeta } from '../seo-meta/type';

export enum BLOG_STATUS {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  SCHEDULE = 'schedule',
  PUBLISHED = 'published',
  RETURN = 'return',
  TRASH = 'trash',
  STORAGE = 'storage',
  REQUEST = 'request',
  AUTO = 'auto',
  DELETE = 'delete',
}

export const BLOG_STATUS_LABELS = {
  [BLOG_STATUS.DRAFT]: 'Bản nháp',
  [BLOG_STATUS.PENDING]: 'Chờ duyệt',
  [BLOG_STATUS.APPROVED]: 'Đã duyệt',
  [BLOG_STATUS.SCHEDULE]: 'Đã lên lịch',
  [BLOG_STATUS.PUBLISHED]: 'Đã xuất bản',
  [BLOG_STATUS.RETURN]: 'Tr<PERSON> về chỉnh sửa',
  [BLOG_STATUS.TRASH]: 'Thùng rác',
  [BLOG_STATUS.STORAGE]: 'Lưu trữ',
  [BLOG_STATUS.REQUEST]: 'Yêu cầu',
  [BLOG_STATUS.AUTO]: 'Tự động',
  [BLOG_STATUS.DELETE]: 'Đã xóa',
};

export enum VISIBILITY {
  PUBLIC = 'public',
  PRIVATE = 'private',
  PASSWORD_PROTECTED = 'password_protected',
}

export enum COMMENT_STATUS {
  OPEN = 'open',
  CLOSED = 'closed',
}

export interface User {
  id?: number;
  username?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

export interface SEOMetaData {
  meta_id?: number;
  seo_title?: string;
  meta_description?: string;
  focus_keyphrase?: string;
  canonical_url?: string;
  is_cornerstone?: number;
  og_title?: string;
  og_description?: string;
  og_image?: string;
  twitter_title?: string;
  twitter_description?: string;
  twitter_image?: string;
  robots_index?: boolean;
  robots_follow?: boolean;
  robots_advanced?: string;
  seo_score?: string;
  readability_score?: string;
  schema_data?: any;
}

export interface Blog {
  // Primary fields (v2.0 API)
  tenant_id?: number;
  title?: string;
  slug?: string;
  description?: string;
  content?: string;
  image?: string;
  status?: BLOG_STATUS | string;
  visibility?: VISIBILITY | string;
  password?: string;
  comment_status?: COMMENT_STATUS | string;
  published_at?: string;
  schedule_at?: string;
  created_at?: string;
  updated_at?: string;
  author_id?: number;
  created_by?: number;

  // Related data
  author?: User;
  created_by_user?: User;
  categories?: BlogCategory[];
  tags?: any[];
  seo_meta?: SEOMetaData;

  // Backward compatibility fields
  post_id?: number;
  publish_date?: string;

  // Fields for UI compatibility
  category_ids?: number[] | string[];
  tag_ids?: number[] | string[];
  related_post_ids?: number[];
  categoryId?: number | string;
  scheduleAt?: Dayjs;
  seoMeta?: SeoMeta;

  // Legacy fields that might still be used in UI
  name?: string; // Maps to title
  imageMobile?: string;
  sourceId?: string;
  sourceLink?: string;
  sort?: number;
  template?: string;
  isAuto?: boolean;
  userId?: string;
  typeId?: string;
  kindId?: string;
  isMagazine?: boolean;
  publishedBy?: string;
  magazineId?: string;
  suggest?: boolean;
  sourceHide?: boolean;
  hideList?: string[];
  subName?: string;
  tocHide?: boolean;
  authorsName?: string;
}

export const VisibilityOptions = [
  {
    label: 'Công khai',
    value: VISIBILITY.PUBLIC,
  },
  {
    label: 'Bảo vệ bằng mật khẩu',
    value: VISIBILITY.PASSWORD_PROTECTED,
  },
  {
    label: 'Riêng tư',
    value: VISIBILITY.PRIVATE,
  },
];

export const CommentStatusOptions = [
  {
    label: 'Cho phép bình luận',
    value: COMMENT_STATUS.OPEN,
  },
  {
    label: 'Không cho phép bình luận',
    value: COMMENT_STATUS.CLOSED,
  },
];

// Define specific fields that can be managed in the drawer
export interface BlogDrawerData {
  // Tags and Categories
  tag_ids?: number[] | string[];
  category_ids?: number[] | string[];

  // Publishing settings
  published_at?: Dayjs;
  publish_date?: string;
  schedule_at?: string;

  // Content metadata
  description?: string; // excerpt/sapo
  suggest?: boolean; // isFeatured

  // Media
  image?: string; // featured image

  // Related posts
  related_post_ids?: number[];

  // Status and visibility (if managed in drawer)
  status?: BLOG_STATUS | string;
  visibility?: VISIBILITY | string;
  comment_status?: COMMENT_STATUS | string;

  author_id?: number;
}

// Related Posts Types
export interface RelatedPost {
  id?: number;
  post_id: number;
  related_post_id: number;
  priority: number;
  is_bidirectional: boolean;
  created_at?: string;
  updated_at?: string;

  // Enriched data
  post?: Blog;
  related_post?: Blog;
}

export interface RelatedPostCreate {
  post_id: number;
  related_post_id: number;
  priority?: number;
  is_bidirectional?: boolean;
}

export interface RelatedPostBulkCreate {
  post_id: number;
  is_bidirectional?: boolean;
  related_posts: Array<{
    related_post_id: number;
    priority?: number;
  }>;
}

export interface RelatedPostUpdate {
  priority?: number;
  is_bidirectional?: boolean;
}

// Type-safe function to extract only allowed drawer fields
export const extractDrawerFields = (data: Partial<Blog>): BlogDrawerData => {
  const allowedFields: (keyof BlogDrawerData)[] = [
    'tag_ids',
    'category_ids',
    'published_at',
    'publish_date',
    'description',
    'suggest',
    'image',
    'related_post_ids',
    'status',
    'visibility',
    'comment_status',
    'author_id',
  ];

  const drawerData: BlogDrawerData = {};

  allowedFields.forEach((field) => {
    if (data[field] !== undefined) {
      (drawerData as any)[field] = data[field];
    }
  });

  return drawerData;
};

export const transformBlog = (values: any) => {
  const item: Blog = { ...values };

  // Map API fields to UI fields for backward compatibility
  if (values.title && !values.name) {
    item.name = values.title;
  }

  // Extract category IDs from category objects if needed
  if (
    Array.isArray(values.categories) &&
    values.categories.length > 0 &&
    typeof values.categories[0] !== 'number'
  ) {
    item.category_ids = values.categories.map((category: any) => category.id);
  }

  // Convert date strings to Dayjs objects
  // Prioritize schedule_at over publish_date
  if (values.schedule_at) {
    item.scheduleAt = dayjs(values.schedule_at);
  } else if (values.publish_date) {
    item.scheduleAt = dayjs(values.publish_date);
  }

  // Legacy conversions for scheduleAt
  if (values.scheduleAt) {
    item.scheduleAt = dayjs(values.scheduleAt);
  }

  return item;
};
