{"module": "Blog posts", "id": "ID", "key": "Key", "name": "Name", "description": "Description", "search": "Search", "reset": "Reset", "inputData": "Please enter data!", "btnAdd": "Add", "btnEdit": "Edit", "btnUpdate": "Save", "btnDelete": "Delete", "addSuccess": "Save successfully", "updateSuccess": "Update successfully", "deleteSuccess": "Delete successfully", "submitError": "Save failed. Try again later", "pleaseEnterData": "Please enter data", "actions": "Actions", "deleteConfirm": "Are you sure to delete", "setupPost": "Post Setup", "overview": "Overview", "category": "Category", "tag": "Tags", "tagDescription": "Create and attach tags to help readers find the blog posts they need.", "categoryDescription": "Create categories to organize topics and help readers find posts they are interested in.", "attachCategory": "Attach categories", "categoryTooltip": "Select categories to classify your post", "createCategory": "Create category", "showFeaturedImage": "Show featured image", "featuredImageTooltip": "Display the featured image of the post", "publishDate": "Publish date", "publishDateTooltip": "Date when the post is published", "author": "Author", "authorTooltip": "Author of the post", "excerpt": "Excerpt", "excerptTooltip": "Excerpt appears in search results and social media sharing", "relatedPosts": "Related posts", "relatedPostsTooltip": "Select related posts", "selectPosts": "Select posts", "featuredPost": "Set as featured post", "featuredPostTooltip": "Featured posts appear in special locations on the website", "allowComments": "<PERSON><PERSON> comments on this post", "allowCommentsTooltip": "Allow readers to comment on this post", "save": "Save", "cancel": "Cancel"}