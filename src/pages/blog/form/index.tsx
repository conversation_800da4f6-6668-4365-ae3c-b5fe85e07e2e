import React from 'react';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import { useNavigateTenant } from '../../../hooks';
import IndexForm from './form';

const Detail: React.FC = (props) => {
  const { id } = useParams();
  // const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();

  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    navigate(`/${MODULE}/`);
  };

  return (
    <div className="">
      <IndexForm onChange={handleChangeForm} id={id}></IndexForm>
    </div>
  );
};

export default Detail;
