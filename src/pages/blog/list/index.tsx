import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  EyeOutlined,
  FileOutlined,
  FileTextOutlined,
  RestOutlined,
  RollbackOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import { Badge, Menu, message } from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks/index';
import ConsoleService from '../../../services/console.service';
import { getStatusStatistics, StatusCounts } from '../api';
import '../assets/styles.scss';
import List from './list';

export default function Index() {
  const navigate = useNavigateTenant();
  const location = useLocation();
  const logger = ConsoleService.register('blog-list');

  // Get status from query params
  const query = queryString.parse(location.search);
  const statusFilter = (query.status as string) || null;

  // State for status statistics
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    draft: 0,
    pending: 0,
    approved: 0,
    schedule: 0,
    published: 0,
    return: 0,
    trash: 0,
    storage: 0,
    request: 0,
    auto: 0,
    delete: 0,
    total: 0,
  });

  // Fetch status statistics from API
  const fetchStatusStatistics = useCallback(async () => {
    try {
      const response = await getStatusStatistics();

      if (response.status.success) {
        setStatusCounts(response.data.status_counts);
        logger('Status statistics loaded:', response.data.status_counts);
      } else {
        message.error(
          response.status.message || 'Không thể tải thống kê trạng thái',
        );
      }
    } catch (error) {
      logger('Error fetching status statistics:', error);
      message.error('Lỗi khi tải thống kê trạng thái blog');
    }
  }, [logger]);

  // Load status statistics on component mount
  useEffect(() => {
    fetchStatusStatistics();
  }, [fetchStatusStatistics]);

  const handleMenuClick = (e: any) => {
    // Extract status from menu key
    const statusMap: Record<string, string> = {
      '/blog/draft': 'draft',
      '/blog/pending': 'pending',
      '/blog/approved': 'approved',
      '/blog/schedule': 'schedule',
      '/blog/published': 'published',
      '/blog/return': 'return',
      '/blog/trash': 'trash',
      '/blog/delete': 'delete',
      '/blog/mine': 'mine',
      '/blog/list': '', // All posts - no specific status filter
    };

    const status = statusMap[e.key] || null;

    // Navigate to blog with status as query param
    if (status) {
      navigate(`/blog?status=${status}`);
    } else {
      navigate('/blog');
    }
  };

  // Get current path and determine selected menu key based on status filter
  const getSelectedMenuKey = () => {
    if (!statusFilter) return '/blog/list';

    // Reverse mapping from status to menu key
    const reverseStatusMap: Record<string, string> = {
      draft: '/blog/draft',
      pending: '/blog/pending',
      approved: '/blog/approved',
      schedule: '/blog/schedule',
      published: '/blog/published',
      return: '/blog/return',
      trash: '/blog/trash',
      delete: '/blog/delete',
      mine: '/blog/mine',
    };

    return reverseStatusMap[statusFilter] || '/blog/list';
  };

  const selectedMenuKey = getSelectedMenuKey();

  const menuItems = [
    {
      key: '/blog/list',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <UnorderedListOutlined className="mr-2" />
            Tất cả bài viết
          </span>
          <Badge count={statusCounts.total} />
        </div>
      ),
    },

    {
      key: '/blog/mine',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <FileOutlined className="mr-2" />
            Bài viết của tôi
          </span>
        </div>
      ),
    },
    {
      key: '/blog/draft',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <FileTextOutlined className="mr-2" />
            Bài viết nháp
          </span>
          <Badge count={statusCounts.draft} />
        </div>
      ),
    },
    {
      key: '/blog/return',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <RollbackOutlined className="mr-2" />
            Bài viết trả lại
          </span>
          <Badge count={statusCounts.return} />
        </div>
      ),
    },

    {
      key: '/blog/pending',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <ClockCircleOutlined className="mr-2" />
            Bài chờ biên tập
          </span>
          <Badge count={statusCounts.pending} />
        </div>
      ),
    },
    {
      key: '/blog/schedule',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <ClockCircleOutlined className="mr-2" />
            Bài viết hẹn giờ xuất bản
          </span>
          <Badge count={statusCounts.schedule} />
        </div>
      ),
    },
    {
      key: '/blog/approved',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <CheckCircleOutlined className="mr-2" />
            Bài viết chờ xuất bản
          </span>
          <Badge count={statusCounts.approved} />
        </div>
      ),
    },
    {
      key: '/blog/published',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <EyeOutlined className="mr-2" />
            Bài viết đã xuất bản
          </span>
          <Badge count={statusCounts.published} />
        </div>
      ),
    },
    {
      key: '/blog/trash',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <RestOutlined className="mr-2" />
            Bài viết đã gỡ
          </span>
          <Badge count={statusCounts.trash} />
        </div>
      ),
    },
    {
      key: '/blog/delete',
      label: (
        <div className="flex justify-between items-center">
          <span className="flex items-center">
            <DeleteOutlined className="mr-2" />
            Bài viết đã xóa
          </span>
          <Badge count={statusCounts.delete} />
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* Back button về dashboard */}

      <div className="flex">
        <div
          style={{
            width: '250px',
            position: 'sticky',
            top: '0',
            height: '100vh',
            overflowY: 'auto',
          }}
        >
          <Menu
            theme="light"
            mode="inline"
            selectedKeys={[selectedMenuKey]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: '100%' }}
          />
        </div>

        <div style={{ flex: 1, paddingLeft: '14px' }}>
          <List key={`blog_${statusFilter}`} statusFilter={statusFilter} />
        </div>
      </div>
    </div>
  );
}
