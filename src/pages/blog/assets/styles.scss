.form-blog-detail {
  .ant-card {
    margin-bottom: 10px;
    border-radius: 10px;
  }
}

.tag-description {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.65);
}

.tag-container {
  margin-bottom: 16px;
}

.tag-list {
  margin-bottom: 8px;
}

.tag-input {
  display: inline-block;
  margin-right: 8px;
}

.tag-count {
  display: inline-block;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.category-container {
  margin-bottom: 16px;
}

.category-attach {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.category-list {
  margin-bottom: 12px;
}

.category-item {
  margin-bottom: 8px;
}

.create-category-btn {
  color: #1890ff;
  padding-left: 0;

  &:hover {
    background: transparent;
    color: #40a9ff;
  }
}

// Styles cho tab Overview
.overview-container {
  margin-bottom: 16px;
}

.overview-section {
  margin-bottom: 20px;
}

.overview-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  position: relative;
}

.overview-switch {
  position: absolute;
  right: 0;
}

.upload-container {
  width: 100%;
  margin-top: 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
  background-color: #fafafa;
  text-align: center;
  padding: 16px 0;
}

.upload-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 86px;
  color: #1890ff;
}

.upload-box {
  width: 100%;

  .ant-upload {
    width: 100%;
    height: auto;
    margin: 0;
  }
}

.date-picker-full {
  width: 100%;
}

.select-full {
  width: 100%;
}

.textarea-full {
  width: 100%;
  resize: none;
}

.char-count,
.post-count {
  margin-left: auto;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-weight: normal;
}

.select-post-btn {
  width: 100%;
  text-align: left;
  border: 1px dashed #d9d9d9;
  background-color: #fafafa;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}
