import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Popconfirm,
  Popover,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useCursorPagination } from '../../../components/pagination';
import CursorPagination from '../../../components/pagination/cursor-pagination';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { cleanParams } from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE, MODULE_GROUP, MODULE_POPUP } from '../config';
import { CrawlLogModal } from '../form/modal';
import useCrawlLogStore from '../store';
import { CrawlLog } from '../type';
import { CrawlLogSearch } from './search';

// Thêm hàm changeBrowserLocationtạm thời
const changeBrowserLocation = (
  navigate: any,
  pathname: string,
  params: any,
) => {
  const queryString = new URLSearchParams(params).toString();
  navigate(`${pathname}?${queryString}`);
};

function CrawlLogList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useCrawlLogStore();
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<CrawlLog[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();
  const [total, setTotal] = useState<number>(0);
  const { afterKey, isNext, isBack, setNextCursor, goNext, goBack } =
    useCursorPagination({
      defaultLimit: 10,
    });
  const fetchData = useCallback(
    async (payload?: any) => {
      const params = {
        ...query,
        ...filters,
        ...payload,
      };

      const cleanedParams = cleanParams(params);
      //changeBrowserLocation(navigate, pathname, cleanedParams);

      const response = await getItems(cleanedParams);
      if (response.status.success) {
        setItems(response.data);
        setTotal(response.meta.total);
        setNextCursor(response.meta.cursors.after);
      } else {
        message.error(response.status.message);
      }
    },
    [filters, query, navigate, pathname],
  );

  const handleFilters = (values: any) => {
    logger('[filters]', { filters, values });
    setFilters(values);
  };

  useEffect(() => {
    fetchData({ after: afterKey });
  }, [fetchData, afterKey]);

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      if (MODULE_POPUP) {
        setIdCurrent(record.id);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/${record.id}`);
      }
    } else if (action === 'add') {
      if (MODULE_POPUP) {
        setIdCurrent(undefined);
        setShowModal(true);
      } else {
        navigate(`/${MODULE_GROUP}/create`);
      }
    }
  };

  const handleModal = () => {
    setShowModal(false);
    fetchData();
  };

  const columns = [
    // {
    //   title: t('id'),
    //   dataIndex: 'id',
    //   key: 'id',
    //   render: (dom: any, record: any) => (
    //     <span
    //       className="text-blue-600 cursor-pointer"
    //       onClick={() => navigate(`/${MODULE_GROUP}/${record.id}`)}
    //     >
    //       {dom}
    //     </span>
    //   ),
    // },
    {
      title: t('form.module'),
      dataIndex: 'module',
      key: 'module',
    },
    {
      title: t('form.action'),
      dataIndex: 'action',
      key: 'action',
    },
    {
      title: t('form.message'),
      dataIndex: 'message',
      key: 'message',
    },
    {
      title: t('form.payload'),
      dataIndex: 'payload',
      key: 'payload',
      render: (payload: any) => {
        if (payload === null) return null;
        return (
          <Popover
            content={
              <pre
                style={{ margin: 0, maxWidth: '900px', whiteSpace: 'pre-wrap' }}
              >
                {payload}
              </pre>
            }
            title={t('form.payloadDetail')}
            trigger="click"
          >
            <QuestionCircleOutlined style={{ cursor: 'pointer' }} />
          </Popover>
        );
      },
    },
    {
      title: t('list.actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('list.deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('list.btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div className="">
      <div className="bg-gray flex justify-between p-4">
        <div className="text-xl font-bold w-full md:w-auto">{t('module')}</div>
        <div className="flex gap-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('list.btnAdd')}
          </Button>

          {/* <Button
            type="dashed"
            icon={<ImportOutlined />}
            onClick={() => handleActions('add', null)}
          >
            {t('btnImport')}
          </Button>

          <Dropdown menu={{
            items: [
              {
                key: '1',
                label: <a href="#">Chức năng 1</a>,
              },
              {
                key: '2',
                label: <a href="#">Chức năng 2</a>,
              },
              {
                key: '3',
                label: <a href="#">Chức năng 3</a>,
              },
            ]
          }} trigger={['click']}>
            <Button icon={<EllipsisOutlined />} />
          </Dropdown> */}
        </div>
      </div>
      <CrawlLogSearch
        query={query}
        loading={loading}
        onChange={handleFilters}
      ></CrawlLogSearch>
      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <CursorPagination
              total={total}
              isNext={isNext}
              isBack={isBack}
              goNext={goNext}
              goBack={goBack}
            />
          </Col>
        </Row>
        {MODULE_POPUP && (
          <CrawlLogModal
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></CrawlLogModal>
        )}
      </Space>
    </div>
  );
}
export { CrawlLogList };
