import { SaveOutlined } from '@ant-design/icons';
import { Button, Form, message, Select } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SaveBarTop } from '../../../components/save-bar';
import ConsoleService from '../../../services/console.service';
import { createItem, getItem, updateItem } from '../api';
import { MODULE } from '../config';
import useBlogStore from '../store';
import { HotelProduct } from '../type';

const FormItem = Form.Item;
const { Option } = Select;

interface IndexFormProps {
  onChange: (reload: boolean) => void;
  id?: string;
}

const FormPolicy: React.FC<IndexFormProps> = ({ onChange, id }) => {
  const { t } = useTranslation(MODULE);
  const logger = ConsoleService.register(MODULE);
  const { loading } = useBlogStore();
  const [form] = Form.useForm();
  const [isNew, setIsNew] = useState<boolean>(false);
  const [item, setItem] = useState<HotelProduct>();
  const initForm: any = {
    // name: "Tour abc",
    // customerMin: 1,
    // customerMax: 11,
    // fromCountry: "66b3a561193ba5acf1ba4f1a",
    // fromCity: "66b3a56e193ba5acf1ba4f22",
    // fromDistrict: "66b3a578193ba5acf1ba4f2e",
    // toCountry: "66b4d78124fcba4a47f50ff6",
    // toCity: "66b4d79f24fcba4a47f51007",
    // toDistrict: "66b4db4733419eaf0343861c",
    // type: "66b4e5716078dce4f3d577ff",
    // kind: "66b4e5836078dce4f3d5780a",
    // dateTotal: "66b4e5966078dce4f3d57811",
    // branch: "66b3a59b193ba5acf1ba4f62",
    // note: "abc"
  };
  const [formValues, setFormValues] = useState<HotelProduct>();

  useEffect(() => {
    if (['create', undefined].includes(id)) {
      setIsNew(true);
    } else if (id) {
      setIsNew(false);
      getItemData(id);
    }
  }, [id]);

  const formatData = (item: HotelProduct) => {
    logger(item);

    return item;
  };

  const getItemData = async (_id: string) => {
    const res = await getItem(_id);
    if (res.status.success) {
      setItem(res.data);
      setFormValues(res.data);
      form.setFieldsValue(formatData(res.data));
    } else {
      message.error(res.status.message);
    }
  };

  const formatValues = (values: HotelProduct) =>
    // if(values.customerMin) values.customerMin = parseInt(values.customerMin)
    values;

  const onFinish = async (values: HotelProduct) => {
    try {
      let res;
      if (isNew) {
        res = await createItem(values);
        if (res.status.success) {
          message.success(t('addSuccess'));
        }
      } else {
        res = await updateItem(id!, values);
        if (res.status.success) {
          message.success(t('updateSuccess'));
        }
      }
      if (!res.status.success) {
        message.error(res.status.message);
      } else {
        setItem(res.data);
        form.resetFields();
        onChange(true);
      }
    } catch (error) {
      logger('Error submitting form', error);
      message.error(
        _.get(error, 'response.data.message.0') || t('submitError'),
      );
    }
  };

  const handleValuesChange = (newValue: any, allValues: any) => {
    logger(newValue);
    logger(allValues);
    setFormValues(allValues);
  };

  return (
    <Form
      form={form}
      name="form"
      layout="vertical"
      onFinish={onFinish}
      onValuesChange={handleValuesChange}
      autoComplete="off"
      initialValues={initForm}
    >
      <SaveBarTop>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          icon={<SaveOutlined />}
          size="large"
        >
          {isNew ? t('btnSave') : t('btnUpdate')}
        </Button>
      </SaveBarTop>
    </Form>
  );
};

export default FormPolicy;
