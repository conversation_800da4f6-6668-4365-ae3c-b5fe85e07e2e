import { Tabs } from 'antd';
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import { MODULE } from '../config';
import CostEstimate from './cost-estimate-index';
import FormBasic from './form-basic';
import FormGallery from './form-gallery';
import FormInfo from './form-info';
import FormPolicy from './form-policy';
import ProgramList from './program-list';

const Detail: React.FC = (props) => {
  const { id } = useParams();
  const logger = ConsoleService.register(MODULE);
  const navigate = useNavigateTenant();
  const [tab, setTab] = useState('OVERVIEW');

  const handleChangeForm = (status: any) => {
    logger('[handleChangeForm]', status);
    navigate(`/${MODULE}/`);
  };

  const handleTab = (tab: string) => {
    setTab(tab);
  };

  return (
    <div>
      <Tabs
        tabPosition="top"
        onChange={handleTab}
        type="card"
        style={{ margin: 0 }}
        items={[
          {
            label: `Tổng quan`,
            key: 'OVERVIEW',
            children: ``,
          },
          {
            label: `Hình ảnh`,
            key: 'IMAGES',
            children: <></>,
          },
          {
            label: `Quy định chính sách`,
            key: 'POLICIES',
            children: ``,
          },
          {
            label: `Loại phòng`,
            key: 'ROOM_TYPES',
            children: ``,
          },
          {
            label: `Bảng giá`,
            key: 'PRICING',
            children: ``,
          },
          {
            label: `Phụ phí`,
            key: 'ADDITIONAL_FEES',
            children: ``,
          },
          {
            label: `Hợp đồng`,
            key: 'CONTRACT',
            children: ``,
          },
        ]}
      />

      <div className="bg-white">
        {tab === 'OVERVIEW' && (
          <FormBasic onChange={handleChangeForm} id={id}></FormBasic>
        )}
        {tab === 'POLICIES' && (
          <FormPolicy onChange={handleChangeForm} id={id}></FormPolicy>
        )}
        {tab === 'IMAGES' && (
          <FormGallery onChange={handleChangeForm} id={id}></FormGallery>
        )}

        {tab === 'CHUONG_TRINH_TOUR' && <ProgramList></ProgramList>}
        {tab === 'THONG_TIN_TOUR' && (
          <FormInfo onChange={handleChangeForm} id={id}></FormInfo>
        )}
        {tab === 'BANG_CHIET_TINH' && <CostEstimate></CostEstimate>}
      </div>
    </div>
  );
};

export default Detail;
