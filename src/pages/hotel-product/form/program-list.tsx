import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Dropdown,
  message,
  Pagination,
  Popconfirm,
  Row,
  Space,
  Table,
} from 'antd';
import queryString from 'query-string';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { useEffectOnce } from 'react-use';
import { useNavigateTenant } from '../../../hooks';
import ConsoleService from '../../../services/console.service';
import {
  //changeBrowserLocation,
  getPageNumber,
} from '../../../services/utils.service';
import { deleteItem, getItems } from '../api';
import '../assets/styles.scss';
import { MODULE } from '../config';
import useHotelProductStore from '../store';
import { HotelProduct } from '../type';
import ModalForm from './program-modal';

export default function ProgramList() {
  const logger = ConsoleService.register(MODULE);
  const { t } = useTranslation(MODULE);
  const navigate = useNavigateTenant();
  const { pathname, search } = useLocation();
  const query = queryString.parse(search);
  const { loading } = useHotelProductStore();
  const [pagination, setPagination] = useState<any>({
    page: 1,
    limit: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [filters, setFilters] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [items, setItems] = useState<HotelProduct[]>([]);
  const [idCurrent, setIdCurrent] = useState<string | undefined>();

  async function fetchData(payload?: any) {
    const params = {
      ...pagination,
      ...query,
      ...filters,
      ...payload,
    };
    //changeBrowserLocation(navigate, pathname, params);
    const response = await getItems(params);
    if (response.status.success) {
      setItems(response.data);
      setTotal(response.meta.total);
    } else {
      message.error(response.status.message);
    }
  }

  const onPagingChange = (page: number, limit: number) => {
    logger('[page]', { page, limit });
    setPagination({ page, limit });
    fetchData({ page, limit });
  };

  useEffectOnce(() => {
    // fetchData();
  });

  const handleDelete = async (id: string) => {
    const res = await deleteItem(id);
    if (res.status.success) {
      message.success(t('deleteSuccess'));
      fetchData();
    } else {
      message.error(res.status.message);
    }
  };

  const handleActions = (action: string, record: any) => {
    if (action === 'edit') {
      setIdCurrent(record.id);
      setShowModal(true);
    } else if (action === 'add') {
      setIdCurrent(undefined);
      setShowModal(true);
    } else if (action === 'save') {
      // Chức năng lưu sẽ được thêm sau
      console.log('Save action will be implemented later');
    }
  };

  const handleTableChange = (page: number, pageSize: number) => {
    onPagingChange(page, pageSize);
  };

  const handleModal = (values: any) => {
    setShowModal(false);
    // fetchData();
    setItems((items) => [...items, ...[values]]);
  };

  const columns = [
    {
      title: t('id'),
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      render: (dom: any, record: any) => (
        <span
          className="text-blue-600 cursor-pointer"
          onClick={() => navigate(`/${MODULE}/${record.id}`)}
        >
          {dom}
        </span>
      ),
    },
    {
      title: t('Ngày'),
      dataIndex: 'day',
      key: 'day',
    },
    {
      title: t('Hành trình'),
      dataIndex: 'name',
      key: 'name',
    },

    {
      title: t('Trang thái'),
      dataIndex: 'active',
      key: 'active',
      render: (dom: any, record: any) => (
        <span>{dom ? t('statusActive') : t('statusDeActive')}</span>
      ),
    },
    {
      title: t('actions'),
      dataIndex: '',
      key: 'action',
      render: (dom: any, record: any) => (
        <Dropdown.Button
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <Popconfirm
                    placement="top"
                    title={t('deleteConfirm')}
                    onConfirm={() => handleDelete(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <DeleteOutlined /> {t('btnDelete')}
                  </Popconfirm>
                ),
              },
            ],
          }}
          onClick={() => handleActions('edit', record)}
        >
          <EditOutlined /> {t('btnEdit')}
        </Dropdown.Button>
      ),
    },
  ];

  return (
    <div>
      <div className="bg-[#fff] flex justify-between p-4">
        <div className="text-xl font-bold">
          Chương trình tour Trung Quốc: Lệ Giang - Shangrila
        </div>
        <div className="gap-4">
          <Button
            type="default"
            icon={<PlusCircleOutlined />}
            onClick={() => handleActions('add', null)}
            className="mr-2"
          >
            {t('btnAdd')}
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={() => handleActions('save', null)}
          >
            {t('btnSave')}
          </Button>
        </div>
      </div>

      <Space direction="vertical" className="bg-white w-full gap-0">
        <Table
          rowKey="_id"
          loading={loading}
          columns={columns}
          dataSource={items}
          pagination={false}
        ></Table>
        <Row justify="end" className="p-4">
          <Col>
            <Pagination
              defaultCurrent={getPageNumber(query, 'page', 1)}
              total={total}
              defaultPageSize={pagination.limit}
              showSizeChanger
              showTitle={false}
              onChange={handleTableChange}
            />
          </Col>
        </Row>
        {showModal && (
          <ModalForm
            showModal={showModal}
            onChange={handleModal}
            id={idCurrent}
          ></ModalForm>
        )}
      </Space>
    </div>
  );
}
