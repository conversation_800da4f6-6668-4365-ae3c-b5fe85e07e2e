@use './_variables.scss' as *;

.ant-tabs-nav {
  margin: 0 !important;
}
.ant-layout {
  // background: #e2e4e9;
  background: inherit;
}
.ant-table-wrapper .ant-table-thead > tr > th,
.ant-table-wrapper .ant-table-thead > tr > td {
  color: #1f2229;
  font-weight: bold;
  background: $bg-gray;
  border-bottom: 1px solid #f0f0f0;
}
.ant-form-item .ant-form-item-label > label {
  color: #1f2229;
  font-size: 14px;
}

// .ant-modal .ant-modal-header {
//   padding: 16px 20px 16px 20px !important;
//   background-color: #f1f2f4;
// }

// .ant-modal .ant-modal-content {
//   padding: 0;
// }

// .ant-modal .ant-modal-title {
//   color: #1f2229;
//   font-size: 16px;
//   font-weight: 500;
//   line-height: 22px;
//   max-height: 40px;
//   text-overflow: ellipsis;
// }
// .ant-modal .ant-modal-header {
//   padding: 16px 20px 0 20px;
//   margin-bottom: 10px;
// }
// .ant-modal .ant-modal-body {
//   padding: 0;
// }
// .form_modal {
//   .form_content {
//     padding: 0 20px 20px 20px;
//   }
//   .form_footer {
//     width: 100%;
//     height: 56px;
//     padding: 12px 16px;
//     background: $bg-gray;
//     border-top: 1px solid #d3d7de;

//     display: flex;
//     justify-content: flex-end;
//   }
// }

.ant-collapse-header {
  background-color: #fff;
}
