import { useNavigate } from 'react-router-dom';
import { useGetPath } from './useGetPath';

/**
 * Custom hook that combines useGetPath and useNavigate for easier navigation with tenant context
 * @returns An object containing navigate and getPath functions
 */
export const useNavigateTenant = () => {
  const navigate = useNavigate();
  const getPath = useGetPath();

  /**
   * Navigate to a route with tenant context
   * @param to - The path to navigate to (relative to tenant)
   * @param options - Navigation options (same as useNavigate options)
   */
  const navigateTo = (
    to: string,
    options?: { replace?: boolean; state?: any },
  ) => {
    const path = getPath(to);
    navigate(path, options);
  };

  return navigateTo;
};

export default useNavigateTenant;
