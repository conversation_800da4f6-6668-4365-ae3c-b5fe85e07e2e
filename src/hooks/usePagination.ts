import { useState } from 'react';

export interface Pagination {
  current: number;
  pageSize: number;
  total?: number;
  nextCursor?: string;
  prevCursor?: string;
  hasMore?: boolean;
}

export const usePagination = (initialState?: Partial<Pagination>) => {
  const [pagination, setPagination] = useState<Pagination>({
    current: initialState?.current || 1,
    pageSize: initialState?.pageSize || 10,
    total: initialState?.total,
    nextCursor: initialState?.nextCursor,
    prevCursor: initialState?.prevCursor,
    hasMore: initialState?.hasMore,
  });

  const setCurrentPage = (page: number) => {
    setPagination({
      ...pagination,
      current: page,
    });
  };

  const setPageSize = (pageSize: number) => {
    setPagination({
      ...pagination,
      pageSize,
      current: 1, // Reset về trang đầu tiên khi thay đổi kích thước trang
    });
  };

  const resetPagination = () => {
    setPagination({
      current: 1,
      pageSize: pagination.pageSize,
      total: undefined,
      nextCursor: undefined,
      prevCursor: undefined,
      hasMore: undefined,
    });
  };

  return {
    pagination,
    setPagination,
    setCurrentPage,
    setPageSize,
    resetPagination,
  };
};
