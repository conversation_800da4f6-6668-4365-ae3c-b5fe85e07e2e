import React, { useEffect, useState } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuthStore } from '../pages/auth/auth';
import { tenantOnboardingApi } from '../pages/tenant-onboarding/api';
import { OnboardingStatus } from '../pages/tenant-onboarding/type';
import tenantService from '../services/tenant.service';

const ProtectedRoute: React.FC = () => {
  const { isLoggedIn } = useAuthStore();
  const location = useLocation();
  const [onboardingStatus, setOnboardingStatus] =
    useState<OnboardingStatus | null>(null);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!isLoggedIn) {
        setIsCheckingOnboarding(false);
        return;
      }

      try {
        const response = await tenantOnboardingApi.getOnboardingStatus();
        if (response.data && (response.data as any).onboarding_status) {
          setOnboardingStatus((response.data as any).onboarding_status);
        }
      } catch (error) {
        console.error('Failed to check onboarding status:', error);
        // If API fails, assume onboarding is completed to avoid blocking user
        setOnboardingStatus(OnboardingStatus.COMPLETED);
      } finally {
        setIsCheckingOnboarding(false);
      }
    };

    checkOnboardingStatus();
  }, [isLoggedIn]);

  if (!isLoggedIn) {
    return <Navigate to="/auth/login" replace />;
  }

  // Show loading while checking onboarding status
  if (isCheckingOnboarding) {
    return <div>Loading...</div>;
  }

  // Check onboarding status and redirect if needed
  const isOnOnboardingPage = location.pathname === '/tenant-onboarding';
  const needsOnboarding =
    onboardingStatus === OnboardingStatus.NOT_STARTED ||
    onboardingStatus === OnboardingStatus.IN_PROGRESS;

  if (needsOnboarding && !isOnOnboardingPage) {
    return <Navigate to="/tenant-onboarding" replace />;
  }

  // If onboarding is completed but user is on onboarding page, redirect to tenant selection
  if (onboardingStatus === OnboardingStatus.COMPLETED && isOnOnboardingPage) {
    return <Navigate to="/auth/tenant-selection" replace />;
  }

  // Check if user has selected a tenant (only for non-onboarding pages)
  if (!isOnOnboardingPage && isLoggedIn && !tenantService.hasTenantSelected()) {
    return <Navigate to="/auth/tenant-selection" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
