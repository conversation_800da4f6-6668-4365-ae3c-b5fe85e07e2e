import { TenantOnboardingForm } from '../pages/tenant-onboarding';
import { RouterItem } from './type';

// Placeholder components for list and detail views
const TenantOnboardingList = () => <div>Tenant Onboarding List</div>;
const TenantOnboardingDetail = () => <div>Tenant Onboarding Detail</div>;

const routes: RouterItem[] = [
  {
    path: 'tenant-onboarding',
    name: 'tenant-onboarding',
    permissions: ['tenant.onboarding.view'],
    icon: 'RocketOutlined',
    component: TenantOnboardingList,
    hideChildrenInMenu: true,
  },
  {
    path: 'tenant-onboarding/setup',
    name: 'tenant-onboarding.setup',
    permissions: ['tenant.onboarding.create'],
    component: TenantOnboardingForm,
    hideMenu: true,
  },
  {
    path: 'tenant-onboarding/create',
    name: 'tenant-onboarding.create',
    permissions: ['tenant.onboarding.create'],
    component: TenantOnboardingForm,
    hideMenu: true,
  },
  {
    path: 'tenant-onboarding/:id',
    name: 'tenant-onboarding.detail',
    permissions: ['tenant.onboarding.view'],
    component: TenantOnboardingDetail,
    hideMenu: true,
  },
];

export default routes;
