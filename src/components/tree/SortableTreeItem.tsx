import React, { CSSProperties } from 'react';
import classNames from 'classnames';
import type { UniqueIdentifier } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { Item } from './components';
import './SortableTreeItem.css';

interface Props {
  id: UniqueIdentifier;
  value: React.ReactNode;
  depth: number;
  position?: number;
  indentationWidth: number;
  indicator?: boolean;
  disableInteraction?: boolean;
  disableSelection?: boolean;
  collapsed?: boolean;
  onCollapse?(): void;
  onRemove?(): void;
  onEdit?(): void;
  wrapperRef?(node: HTMLLIElement): void;
  style?: CSSProperties;
  clone?: boolean;
  childCount?: number;
}

export function SortableTreeItem({
  id,
  depth,
  position,
  clone,
  collapsed,
  disableSelection,
  disableInteraction,
  indentationWidth,
  indicator,
  value,
  onCollapse,
  onRemove,
  onEdit,
  style,
  wrapperRef,
  childCount,
}: Props) {
  const {
    attributes,
    isDragging,
    isSorting,
    listeners,
    setDraggableNodeRef,
    setDroppableNodeRef,
    transform,
    transition,
  } = useSortable({
    id,
  });
  const translatedTransform = {
    ...transform,
    x: (transform?.x ?? 0) + indentationWidth * depth,
  };

  const sortableItemStyle = {
    transform: CSS.Transform.toString(translatedTransform),
    transition,
    '--spacing': `${indentationWidth * depth}px`,
    ...(style ?? {}),
  } as React.CSSProperties;

  return (
    <Item
      ref={setDroppableNodeRef}
      wrapperRef={wrapperRef}
      id={id}
      value={value}
      position={position}
      dragging={isDragging}
      sorting={isSorting}
      handle
      handleProps={{
        ref: setDraggableNodeRef,
        ...attributes,
        ...listeners,
      }}
      clone={clone}
      childCount={childCount}
      style={sortableItemStyle}
      onCollapse={onCollapse}
      onRemove={onRemove}
      onEdit={onEdit}
      collapsed={collapsed}
    />
  );
}
