import React, { useEffect } from 'react';
import classNames from 'classnames';
import type { DraggableSyntheticListeners } from '@dnd-kit/core';
import type { Transform } from '@dnd-kit/utilities';

import { Handle, Remove, Edit } from './components';

// Sử dụng cách import CSS thông thường thay vì CSS module để tránh lỗi
import './Item.css';

// Tạo một đối tượng styles giả để sử dụng thay cho CSS module
const styles = {
  Wrapper: 'item-wrapper',
  fadeIn: 'fade-in',
  sorting: 'sorting',
  dragOverlay: 'drag-overlay',
  Item: 'item',
  dragging: 'dragging',
  withHandle: 'with-handle',
  disabled: 'disabled',
  color: 'color',
  ItemContent: 'item-content',
  ItemPosition: 'item-position',
  Actions: 'actions',
  Edit: 'edit',
  Remove: 'remove',
};

export interface Props {
  dragOverlay?: boolean;
  color?: string;
  disabled?: boolean;
  dragging?: boolean;
  handle?: boolean;
  handleProps?: any;
  height?: number;
  index?: number;
  fadeIn?: boolean;
  transform?: Transform | null;
  listeners?: DraggableSyntheticListeners;
  sorting?: boolean;
  style?: React.CSSProperties;
  transition?: string | null;
  wrapperStyle?: React.CSSProperties;
  value: React.ReactNode;
  position?: number;
  id?: string | number;
  childCount?: number;
  collapsed?: boolean;
  clone?: boolean;
  wrapperRef?: (node: HTMLLIElement) => void;
  onRemove?(): void;
  onEdit?(): void;
  onCollapse?(): void;
  renderItem?(args: {
    dragOverlay: boolean;
    dragging: boolean;
    sorting: boolean;
    index: number | undefined;
    fadeIn: boolean;
    listeners: DraggableSyntheticListeners;
    ref: React.Ref<HTMLElement>;
    style: React.CSSProperties | undefined;
    transform: Props['transform'];
    transition: Props['transition'];
    value: Props['value'];
  }): React.ReactElement;
}

export const Item = React.memo(
  React.forwardRef<HTMLLIElement, Props>(
    (
      {
        color,
        dragOverlay,
        dragging,
        disabled,
        fadeIn,
        handle,
        handleProps,
        height,
        index,
        listeners,
        onRemove,
        onEdit,
        onCollapse,
        renderItem,
        sorting,
        style,
        transition,
        transform,
        value,
        position,
        wrapperStyle,
        wrapperRef,
        id,
        childCount,
        collapsed,
        clone,
        ...props
      },
      ref,
    ) => {
      useEffect(() => {
        if (!dragOverlay) {
          return;
        }

        document.body.style.cursor = 'grabbing';

        return () => {
          document.body.style.cursor = '';
        };
      }, [dragOverlay]);

      return renderItem ? (
        renderItem({
          dragOverlay: Boolean(dragOverlay),
          dragging: Boolean(dragging),
          sorting: Boolean(sorting),
          index,
          fadeIn: Boolean(fadeIn),
          listeners,
          ref,
          style,
          transform,
          transition,
          value,
        })
      ) : (
        <li
          className={classNames(
            styles.Wrapper,
            fadeIn && styles.fadeIn,
            sorting && styles.sorting,
            dragOverlay && styles.dragOverlay,
          )}
          style={
            {
              ...wrapperStyle,
              transition: [transition, wrapperStyle?.transition]
                .filter(Boolean)
                .join(', '),
              '--translate-x': transform
                ? `${Math.round(transform.x)}px`
                : undefined,
              '--translate-y': transform
                ? `${Math.round(transform.y)}px`
                : undefined,
              '--scale-x': transform?.scaleX
                ? `${transform.scaleX}`
                : undefined,
              '--scale-y': transform?.scaleY
                ? `${transform.scaleY}`
                : undefined,
              '--index': index,
              '--color': color,
            } as React.CSSProperties
          }
          ref={ref}
        >
          <div
            className={classNames(
              styles.Item,
              dragging && styles.dragging,
              handle && styles.withHandle,
              dragOverlay && styles.dragOverlay,
              disabled && styles.disabled,
              color && styles.color,
            )}
            style={style}
            data-cypress="draggable-item"
            {...(!handle ? listeners : undefined)}
            tabIndex={!handle ? 0 : undefined}
          >
            <div className={styles.ItemContent}>
              {value}
              {position !== undefined && (
                <span className={styles.ItemPosition}>Vị trí: {position}</span>
              )}
            </div>
            <span className={styles.Actions}>
              {onEdit ? (
                <Edit className={styles.Edit} onClick={onEdit} />
              ) : null}
              {!clone && onRemove ? (
                <Remove className={styles.Remove} onClick={onRemove} />
              ) : null}
              {handle ? <Handle {...handleProps} {...listeners} /> : null}
            </span>
          </div>
        </li>
      );
    },
  ),
);
