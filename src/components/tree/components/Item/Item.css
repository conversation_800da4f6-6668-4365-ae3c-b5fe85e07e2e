$font-weight: 400;
$background-color: #fff;
$border-color: #efefef;
$text-color: #333;
$handle-color: rgba(0, 0, 0, 0.25);
$box-shadow-border: 0 0 0 calc(1px / var(--scale-x, 1)) rgba(63, 63, 68, 0.05);
$box-shadow-common: 0 1px calc(3px / var(--scale-x, 1)) 0 rgba(34, 33, 81, 0.15);
$box-shadow: $box-shadow-border, $box-shadow-common;
$focused-outline-color: #4c9ffe;

@keyframes pop {
  0% {
    transform: scale(1);
    box-shadow: var(--box-shadow);
  }
  100% {
    transform: scale(var(--scale));
    box-shadow: var(--box-shadow-picked-up);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.item-wrapper {
  list-style: none;
  box-sizing: border-box;
  padding: 5px 0;
  transform: translate3d(var(--translate-x, 0), var(--translate-y, 0), 0)
    scaleX(var(--scale-x, 1)) scaleY(var(--scale-y, 1));
  transform-origin: 0 0;
  touch-action: manipulation;
}

.item-wrapper.fade-in {
  animation: fadeIn 500ms ease;
}

.item-wrapper.sorting {
  transition: transform 250ms ease;
}

.item-wrapper.drag-overlay {
  z-index: 999;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 10px 15px;
  padding-right: 5px;
  background-color: #fff;
  box-shadow:
    0 0 0 1px rgba(63, 63, 68, 0.05),
    0 1px 3px 0 rgba(34, 33, 81, 0.15);
  border-radius: 4px;
  position: relative;
  z-index: 0;
  min-height: 40px;
}

.item.with-handle {
  padding-left: 5px;
}

.item.color:before {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  height: 100%;
  width: 3px;
  display: block;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  background-color: var(--color);
}

.item-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.item-position {
  font-size: 11px;
  color: #666;
  margin-top: 3px;
}

.item.drag-overlay {
  cursor: inherit;
  box-shadow:
    0 0 0 1px rgba(63, 63, 68, 0.05),
    0 1px 3px 0 rgba(34, 33, 81, 0.15);
}

.item.dragging:not(.drag-overlay) {
  opacity: 0.5;
  z-index: 0;
}

.item.disabled {
  color: #999;
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.item:focus {
  outline: none;
  box-shadow: 0 0 0 2px #4c9ffe;
}

.item:not(.with-handle) {
  cursor: grab;
  touch-action: manipulation;
}

.actions {
  display: flex;
  align-items: center;
}

.edit {
  margin-right: 5px;
}

.remove {
  margin-right: 5px;
}
