import { Col, Row } from 'antd';
import { t } from 'i18next';
import { ReactNode } from 'react';

interface LayoutProps {
  children: ReactNode;
  title: string;
}

export default function FormGroup({ children, title }: LayoutProps) {
  return (
    <Row gutter={16}>
      <Col xs={24} lg={6}>
        <div className="font-semibold text-lg">{t(title)}</div>
      </Col>
      <Col xs={24} lg={18}>
        <Row gutter={16}>{children}</Row>
      </Col>
    </Row>
  );
}
