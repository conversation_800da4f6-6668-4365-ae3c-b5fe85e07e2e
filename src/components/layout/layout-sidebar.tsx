import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import { Button, Drawer, Layout, Menu } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMediaQuery } from 'react-responsive';
import { Outlet } from 'react-router-dom';
import { useNavigateTenant } from '../../hooks';
import { convertRoutesToMenuItems, routes } from '../../router';
import { UserProfile } from '../menu-top';

const { Header, Sider, Content } = Layout;

const LayoutSideBar: React.FC = () => {
  console.log('[LayoutSideBar]');
  const [collapsed, setCollapsed] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const navigate = useNavigateTenant();
  const { t } = useTranslation('menu');

  const isMobile = useMediaQuery({ maxWidth: 767 });
  const menuItems = convertRoutesToMenuItems(t, routes);

  const handleMenuClick = (e: any) => {
    navigate(e.key);
    if (isMobile) {
      setDrawerVisible(false);
    }
  };

  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {isMobile ? (
        <>
          <Drawer
            title="Menu"
            placement="left"
            closable={false}
            onClose={toggleDrawer}
            open={drawerVisible}
            style={{ padding: 0, height: '100vh' }}
          >
            <Menu
              theme="light"
              mode="inline"
              defaultSelectedKeys={['1']}
              items={menuItems}
              onClick={handleMenuClick}
              style={{ height: '100%' }}
            />
          </Drawer>
          <Button
            type="text"
            icon={<MenuFoldOutlined />}
            onClick={toggleDrawer}
            style={{
              fontSize: '16px',
              width: 32,
              height: 32,
              position: 'absolute',
              top: 16,
              left: 16,
              zIndex: 1000,
            }}
          />
        </>
      ) : (
        <Sider trigger={null} collapsible collapsed={collapsed} theme="light">
          <div className="demo-logo-vertical" />
          <Menu
            theme="light"
            mode="inline"
            defaultSelectedKeys={['1']}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: '100%' }}
          />
        </Sider>
      )}
      <Layout>
        <Header
          style={{
            padding: 0,
            background: '#fff',
            display: 'flex',
            justifyContent: isMobile ? 'end' : 'space-between',
            alignItems: 'center',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            zIndex: 1000,
          }}
        >
          {!isMobile && (
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
              }}
            />
          )}
          <UserProfile></UserProfile>
        </Header>
        <Content
          style={{
            margin: '12px',
            padding: 0,
            minHeight: 'calc(100vh - 112px)',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default LayoutSideBar;
