import { Drawer, Layout } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Outlet, useLocation } from 'react-router-dom';
import useUserStore from '../../pages/user/store';
import { convertRoutesToMenuItems, routes } from '../../router';
import { LauncherMenu } from '../laucher';
import { UserProfile } from '../menu-top';
import NotificationComponent from '../notification';
import WebsiteSelector from '../website-selector';
import './assets/style.scss';
const { Header, Content } = Layout;

const LayoutLaucher: React.FC = () => {
  const location = useLocation();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const { t } = useTranslation('menu');
  const { getProfile, getPermissions } = useUserStore();

  useEffect(() => {
    getProfile();
    //getPermissions();
  }, [getProfile, getPermissions]);

  const menuItems = convertRoutesToMenuItems(t, routes);

  //console.log(routes);

  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  const onChangeLauncher = (action: string) => {
    if (action === 'router') {
      setDrawerVisible(!drawerVisible);
    }
  };

  return (
    <Layout
      className={
        location.pathname === '/' ? 'layout-laucher' : 'layout-default'
      }
    >
      <Drawer
        title="Menu"
        placement="top"
        closable
        onClose={toggleDrawer}
        open={drawerVisible}
        height="auto"
      >
        <LauncherMenu items={menuItems} onChange={onChangeLauncher} />
      </Drawer>
      <Layout>
        <Header
          style={{
            padding: 0,
            background: '#fff',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            zIndex: 1000,
          }}
        >
          <div className="flex items-center">
            <span
              onClick={toggleDrawer}
              className="w-[56px] h-[56px] flex justify-center items-center cursor-pointer icon-launcher-menu"
            ></span>
            <WebsiteSelector />
          </div>
          <div className="flex items-center">
            <NotificationComponent />
            <UserProfile />
          </div>
        </Header>
        <Content
          style={{
            margin: 0,
            padding: 0,
            minHeight: '100vh',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default LayoutLaucher;
