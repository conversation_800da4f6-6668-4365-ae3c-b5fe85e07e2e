import React, { useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../pages/auth/auth';

const LayoutAuth: React.FC = () => {
  const isLoggedIn = useAuthStore((state) => state.isLoggedIn);
  const location = useLocation();

  useEffect(() => {
    if (isLoggedIn && location.pathname !== '/') {
      window.location.href = '/';
    }
  }, [isLoggedIn, location.pathname]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Outlet />
    </div>
  );
};

export default LayoutAuth;
