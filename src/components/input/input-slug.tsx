import { Button, Input } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import slugify from 'slugify';

interface InputSlugProps {
  form: FormInstance;
  name: string;
  value?: string;
  initialValue?: string; // Hỗ trợ backward compatibility
  sourceField?: string; // Trường để tạo slug tự động (ví dụ: 'title')
  isEditMode?: boolean; // Có phải đang ở chế độ chỉnh sửa không
}

const InputSlug: React.FC<InputSlugProps> = ({
  form,
  name,
  value = '',
  initialValue = '',
  sourceField = 'title',
  isEditMode = false,
}) => {
  // Sử dụng value hoặc initialValue, ưu tiên value
  const inputValue = value || initialValue;
  const { t } = useTranslation('components');
  const [isEditing, setIsEditing] = useState(false);
  const [slugValue, setSlugValue] = useState('');

  useEffect(() => {
    // Set initial value in form and generate initial slug
    form.setFieldsValue({ [name]: inputValue });
    setSlugValue(slugify(inputValue, { lower: true }));
  }, [form, name, inputValue]);

  // Theo dõi thay đổi của sourceField để tự động cập nhật slug khi tạo mới
  useEffect(() => {
    if (!isEditMode && sourceField) {
      // Đăng ký listener cho form để theo dõi thay đổi
      const interval = setInterval(() => {
        const currentSourceValue = form.getFieldValue(sourceField);
        const currentSlugValue = form.getFieldValue(name);

        // Chỉ tự động cập nhật khi:
        // 1. Không ở chế độ chỉnh sửa
        // 2. Không đang edit slug
        // 3. Có giá trị sourceField
        // 4. Slug hiện tại trống hoặc khác với slug được tạo từ sourceField
        if (currentSourceValue && !isEditing && !isEditMode) {
          const expectedSlug = slugify(currentSourceValue, { lower: true });
          if (!currentSlugValue || currentSlugValue !== expectedSlug) {
            setSlugValue(expectedSlug);
            form.setFieldsValue({ [name]: expectedSlug });
          }
        }
      }, 300);

      return () => clearInterval(interval);
    }
  }, [form, name, sourceField, isEditMode, isEditing]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    setIsEditing(false);
    // Save the slug back to the form
    setSlugValue(slugify(slugValue, { lower: true }));
    form.setFieldsValue({ [name]: slugValue });
  };

  const handleReset = () => {
    form.setFieldsValue({ [name]: inputValue });
    setSlugValue(slugify(inputValue, { lower: true }));
    setIsEditing(false);
  };

  const handleGenerateFromTitle = () => {
    const sourceValue = form.getFieldValue(sourceField);
    if (sourceValue) {
      const newSlug = slugify(sourceValue, { lower: true });
      setSlugValue(newSlug);
      form.setFieldsValue({ [name]: newSlug });
    }
  };

  return (
    <div className="flex gap-3">
      <Input
        className=""
        value={slugValue}
        onChange={(e) => {
          const { value } = e.target;
          setSlugValue(value);
        }}
        placeholder=""
        disabled={isEditMode ? !isEditing : false}
      />
      {/* Chỉ hiển thị nút Edit khi ở edit mode */}
      {isEditMode && (
        <>
          {isEditing ? (
            <>
              <Button type="primary" onClick={handleSave}>
                Lưu
              </Button>
              <Button onClick={handleReset}>Đặt lại</Button>
              <Button onClick={handleGenerateFromTitle}>
                Lấy theo {sourceField === 'title' ? 'tiêu đề' : sourceField}
              </Button>
            </>
          ) : (
            <Button onClick={handleEdit}>Chỉnh sửa</Button>
          )}
        </>
      )}
    </div>
  );
};

export default InputSlug;
