import React from 'react';
import { Button, ButtonProps } from 'antd';
import { Link } from 'react-router-dom';
import { useGetPath } from '../../hooks';

interface ButtonLinkProps extends Omit<ButtonProps, 'href'> {
  /** Đường dẫn tương đối, sẽ được kết hợp với tenant_id để tạo URL đầy đủ */
  to: string;
  /** Nếu true, sẽ sử dụng đường dẫn chính xác như đã cung cấp mà không thêm tenant_id */
  absolute?: boolean;
}

/**
 * Component ButtonLink kết hợp Button của Ant Design với Link của React Router
 * để tạo button có thể điều hướng đến URL với tenant_id đúng.
 */
export const ButtonLink: React.FC<ButtonLinkProps> = ({
  to,
  absolute = false,
  children,
  ...buttonProps
}) => {
  const getPath = useGetPath();

  // Tạo URL đầy đủ dựa trên đường dẫn tương đối và tenant_id
  const fullPath = absolute ? to : getPath(to);

  return (
    <Link to={fullPath}>
      <Button {...buttonProps}>{children}</Button>
    </Link>
  );
};
