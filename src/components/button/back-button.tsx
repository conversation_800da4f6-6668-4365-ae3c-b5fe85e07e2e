import { ArrowLeftOutlined } from '@ant-design/icons';
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNavigateTenant } from '../../hooks';

export interface BackButtonProps {
  /** Đích đến khi click back button */
  destination?: 'dashboard' | 'list' | 'back' | string;
  /** Ẩn button khi mode popup */
  hideInPopup?: boolean;
  /** Kiểm tra xem có phải mode popup không */
  isPopupMode?: boolean;
  /** Custom className */
  className?: string;
  /** Custom onClick handler */
  onClick?: () => void;
}

const BackButton: React.FC<BackButtonProps> = ({
  destination = 'back',
  hideInPopup = true,
  isPopupMode = false,
  className = 'cursor-pointer',
  onClick,
}) => {
  const navigateTenant = useNavigateTenant();
  const navigate = useNavigate();
  const location = useLocation();

  // Ẩn button nếu đang ở mode popup và hideInPopup = true
  if (hideInPopup && isPopupMode) {
    return null;
  }

  const handleClick = () => {
    if (onClick) {
      onClick();
      return;
    }

    // Xử lý navigation dựa trên destination
    switch (destination) {
      case 'dashboard':
        navigateTenant('/');
        break;
      case 'list': {
        // Dynamic navigation - sử dụng location state hoặc fallback về blog list
        const fromPath = location.state?.from || '/blog';
        navigateTenant(fromPath);
        break;
      }
      case 'back':
        // Sử dụng browser back hoặc fallback về blog list
        if (window.history.length > 1) {
          navigate(-1);
        } else {
          navigateTenant('/blog');
        }
        break;
      default:
        // Nếu destination là custom path
        if (destination.startsWith('/')) {
          // Absolute path - sử dụng navigateTenant để thêm tenant context
          navigateTenant(destination);
        } else {
          // Relative path hoặc external URL
          navigate(destination);
        }
        break;
    }
  };

  return <ArrowLeftOutlined className={className} onClick={handleClick} />;
};

export default BackButton;
