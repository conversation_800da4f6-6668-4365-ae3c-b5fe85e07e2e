import { useCallback, useMemo } from 'react';
import RichTextEditor from 'reactjs-tiptap-editor';

// Import extensions individually for better tree-shaking and to stay current with latest version
import { BaseKit } from 'reactjs-tiptap-editor';
import { Clear } from 'reactjs-tiptap-editor/clear';
import { FontFamily } from 'reactjs-tiptap-editor/fontfamily';
import { FormatPainter } from 'reactjs-tiptap-editor/formatpainter';
import { History } from 'reactjs-tiptap-editor/history';
import { SearchAndReplace } from 'reactjs-tiptap-editor/searchandreplace';
import { TableOfContents } from 'reactjs-tiptap-editor/tableofcontent';

// Import CSS
import 'easydrawer/styles.css';
import 'katex/dist/katex.min.css';
import 'prism-code-editor-lightweight/layout.css';
import 'prism-code-editor-lightweight/themes/github-dark.css';
import 'reactjs-tiptap-editor/style.css';
import './editor.css';

// Constants
const DEFAULT_EDITOR_CONTENT = '';
const DEBOUNCE_DELAY = 300;
const CHARACTER_LIMIT = 50_000;

// Utility functions
const convertBase64ToBlob = (base64: string): Blob => {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)![1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
};

const debounce = <T extends (...args: any[]) => void>(
  func: T,
  wait: number,
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

interface EditorProps {
  onChange?: (html: string) => void;
  value?: string;
  theme?: 'light' | 'dark';
  disabled?: boolean;
  placeholder?: string;
  characterLimit?: number;
  debounceDelay?: number;
}

export function Editor({
  onChange,
  value,
  theme = 'light',
  disabled = false,
  placeholder,
  characterLimit = CHARACTER_LIMIT,
  debounceDelay = DEBOUNCE_DELAY,
}: EditorProps) {
  // Memoized extensions configuration
  const extensions = useMemo(
    () => [
      BaseKit.configure({
        placeholder: {
          showOnlyCurrent: true,
          ...(placeholder && { placeholder }),
        },
        characterCount: { limit: characterLimit },
      }),
      History,
      SearchAndReplace,
      TableOfContents,
      FormatPainter.configure({ spacer: true }),
      Clear,
      FontFamily,
      // Heading.configure({ spacer: true }),
      // FontSize,
      // Bold,
      // Italic,
      // TextUnderline,
      // Strike,
      // MoreMark,
      // Katex,
      // Emoji,
      // Color.configure({ spacer: true }),
      // Highlight,
      // BulletList,
      // OrderedList,
      // TextAlign.configure({ types: ['heading', 'paragraph'], spacer: true }),
      // Indent,
      // LineHeight,
      // TaskList.configure({ spacer: true, taskItem: { nested: true } }),
      // Link,
      // Image.configure({
      //   upload: (files: File) => {
      //     return new Promise((resolve) => {
      //       setTimeout(() => {
      //         resolve(URL.createObjectURL(files));
      //       }, 500);
      //     });
      //   },
      // }),
      // Video.configure({
      //   upload: (files: File) => {
      //     return new Promise((resolve) => {
      //       setTimeout(() => {
      //         resolve(URL.createObjectURL(files));
      //       }, 500);
      //     });
      //   },
      // }),
      // Blockquote,
      // SlashCommand,
      // HorizontalRule,
      // Code.configure({ toolbar: false }),
      // CodeBlock,
      // ColumnActionButton,
      // Table,
      // Iframe,
      // ExportPdf.configure({ spacer: true }),
      // ImportWord.configure({
      //   upload: (files: File[]) => {
      //     const f = files.map((file) => ({
      //       src: URL.createObjectURL(file),
      //       alt: file.name,
      //     }));
      //     return Promise.resolve(f);
      //   },
      // }),
      // ExportWord,
      // TextDirection,
      // Mention,
      // Attachment.configure({
      //   upload: (file: any) => {
      //     // fake upload return base 64
      //     const reader = new FileReader();
      //     reader.readAsDataURL(file);

      //     return new Promise((resolve) => {
      //       setTimeout(() => {
      //         const blob = convertBase64ToBlob(reader.result as string);
      //         resolve(URL.createObjectURL(blob));
      //       }, 300);
      //     });
      //   },
      // }),
      // Excalidraw,
      // Mermaid.configure({
      //   upload: (file: any) => {
      //     // fake upload return base 64
      //     const reader = new FileReader();
      //     reader.readAsDataURL(file);

      //     return new Promise((resolve) => {
      //       setTimeout(() => {
      //         const blob = convertBase64ToBlob(reader.result as string);
      //         resolve(URL.createObjectURL(blob));
      //       }, 300);
      //     });
      //   },
      // }),
      // Drawer.configure({
      //   upload: (file: any) => {
      //     // fake upload return base 64
      //     const reader = new FileReader();
      //     reader.readAsDataURL(file);

      //     return new Promise((resolve) => {
      //       setTimeout(() => {
      //         const blob = convertBase64ToBlob(reader.result as string);
      //         resolve(URL.createObjectURL(blob));
      //       }, 300);
      //     });
      //   },
      // }),
      // Twitter,
    ],
    [characterLimit, placeholder],
  );

  // Optimized change handler with debouncing
  const handleEditorContentChange = useCallback(
    (newHtml: string) => {
      onChange?.(newHtml);
      console.log('handleEditorContentChange', newHtml);
    },
    [onChange],
  );

  // Memoized debounced handler
  const debouncedHandleEditorContentChange = useMemo(
    () => debounce(handleEditorContentChange, debounceDelay),
    [handleEditorContentChange, debounceDelay],
  );

  // Use debounced handler for better performance
  const finalChangeHandler =
    debounceDelay > 0
      ? debouncedHandleEditorContentChange
      : handleEditorContentChange;

  // Memoized content to prevent unnecessary re-renders
  const editorContent = useMemo(() => value || DEFAULT_EDITOR_CONTENT, [value]);

  return (
    <RichTextEditor
      output="html"
      content={editorContent}
      onChangeContent={finalChangeHandler}
      extensions={extensions}
      dark={theme === 'dark'}
      disabled={disabled}
    />
  );
}
