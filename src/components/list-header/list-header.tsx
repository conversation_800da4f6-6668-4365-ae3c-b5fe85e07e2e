import React from 'react';
import { Typography, Space, Button } from 'antd';
import { PlusCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title } = Typography;

interface ListHeaderProps {
  title: string;
  showAddButton?: boolean;
  addButtonText?: string;
  onAddClick?: () => void;
  module?: string;
  customActions?: React.ReactNode;
}

const ListHeader: React.FC<ListHeaderProps> = ({
  title,
  showAddButton = true,
  addButtonText,
  onAddClick,
  module,
  customActions,
}) => {
  const { t } = useTranslation(module);
  const defaultAddText = addButtonText || (module ? t('btnAdd') : 'Add');

  return (
    <div className="bg-gray flex justify-between p-4">
      <div className="text-xl font-bold">{title}</div>
      <div className="gap-4">
        {customActions}
        {showAddButton && (
          <Button
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={onAddClick}
          >
            {defaultAddText}
          </Button>
        )}
      </div>
    </div>
  );
};

export default ListHeader;
