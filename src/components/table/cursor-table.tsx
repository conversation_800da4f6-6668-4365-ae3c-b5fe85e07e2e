import { Col, Row, Space, Table } from 'antd';
import React from 'react';
import CursorPagination from '../pagination/cursor-pagination';

interface CursorTableProps {
  children?: React.ReactNode;
  onChange: (
    startKey: any,
    before?: any,
    after?: any,
    extra?: { action: string },
  ) => void;
  cursors: {
    before: any;
    after: any;
  };

  [key: string]: any;
}

const CursorTable: React.FC<CursorTableProps> = ({
  children,
  onChange,
  cursors,
  ...restProps
}) => (
  <Space direction="vertical" style={{ width: '100%' }}>
    <Table {...restProps} pagination={{ position: ['none', 'none'] }}>
      {children}
    </Table>

    <Row justify="end" className="p-4">
      <Col>
        <CursorPagination
          before={cursors?.before}
          onChange={onChange}
          after={cursors?.after}
          total={0}
        />
      </Col>
    </Row>
  </Space>
);

export default CursorTable;
