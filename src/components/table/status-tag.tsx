import React from 'react';
import { Tag } from 'antd';

interface StatusTagProps {
  value: string;
  statusList: { label: string; value: string; color: string }[];
}

const StatusTag: React.FC<StatusTagProps> = ({ value, statusList }) => {
  const status = statusList.find((status) => status.value === value);
  return status ? (
    <Tag color={status.color}>{status.label}</Tag>
  ) : (
    <Tag>{value}</Tag>
  );
};

export default StatusTag;
