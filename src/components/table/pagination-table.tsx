import { Col, Pagination, Row, Space, Table } from 'antd';
import React from 'react';

interface PaginationTableProps {
  children?: React.ReactNode;
  onChange: (page: number, pageSize: number) => void;
  total: number;

  [key: string]: any;
}

const PaginationTable: React.FC<PaginationTableProps> = ({
  children,
  onChange,
  total,
  ...restProps
}) => (
  <Space direction="vertical" className="bg-white w-full gap-0">
    <Table {...restProps} pagination={{ position: ['none', 'none'] }}>
      {children}
    </Table>

    <Row justify="end" className="p-4">
      <Col>
        <Pagination
          defaultCurrent={1}
          total={total}
          defaultPageSize={10}
          showSizeChanger
          showTitle={false}
          onChange={onChange}
        />
      </Col>
    </Row>
  </Space>
);

export default PaginationTable;
