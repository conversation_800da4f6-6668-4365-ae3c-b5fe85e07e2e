import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom'; // thêm phần này để dùng navigate
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';
import { useGetPath } from '../../hooks';
import { MenuItem } from '../../router';
import './assets/style.scss';

interface LauncherMenuProps {
  items: MenuItem[];
  onChange(action: string): void;
}

export const LauncherMenu: React.FC<LauncherMenuProps> = ({
  items,
  onChange,
}: LauncherMenuProps) => {
  const { t } = useTranslation('menu');
  const getPath = useGetPath();

  // State and refs for swipe detection
  const [isDragging, setIsDragging] = useState(false);
  const dragStartPos = useRef<{ x: number; y: number } | null>(null);
  const dragThreshold = 10; // Minimum distance to consider as drag

  const allItems = [
    {
      key: getPath('/'),
      icon: <div className="menu-icon menu-all"></div>,
      label: t('viewAll'),
    },
    ...items,
  ];

  // Handle touch/mouse start events
  const handlePointerStart = (
    e: React.PointerEvent | React.MouseEvent | React.TouchEvent,
  ) => {
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    dragStartPos.current = { x: clientX, y: clientY };
    setIsDragging(false);
  };

  // Handle touch/mouse move events
  const handlePointerMove = (
    e: React.PointerEvent | React.MouseEvent | React.TouchEvent,
  ) => {
    if (!dragStartPos.current) return;

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    const deltaX = Math.abs(clientX - dragStartPos.current.x);
    const deltaY = Math.abs(clientY - dragStartPos.current.y);

    // If movement exceeds threshold, consider it a drag
    if (deltaX > dragThreshold || deltaY > dragThreshold) {
      setIsDragging(true);
    }
  };

  // Handle touch/mouse end events
  const handlePointerEnd = () => {
    // Reset drag state after a short delay to prevent immediate clicks
    setTimeout(() => {
      setIsDragging(false);
      dragStartPos.current = null;
    }, 100);
  };

  return (
    <div className="flex flex-col items-start justify-start select-none">
      <div className="flex flex-row flex-wrap gap-4">
        {allItems &&
          allItems.map((menu, i) => (
            <Link
              to={menu.key}
              key={i}
              className="flex flex-col items-center text-white hover:bg-[#f2f2f2] rounded-lg cursor-pointer"
              onClick={(e) => {
                if (isDragging) {
                  e.preventDefault();
                  return;
                }
                onChange('router');
              }}
              onPointerDown={handlePointerStart}
              onPointerMove={handlePointerMove}
              onPointerUp={handlePointerEnd}
              onTouchStart={handlePointerStart}
              onTouchMove={handlePointerMove}
              onTouchEnd={handlePointerEnd}
              onMouseDown={handlePointerStart}
              onMouseMove={handlePointerMove}
              onMouseUp={handlePointerEnd}
            >
              <div className="hover:bg-white/10 hover:rounded-lg p-4 flex flex-col items-center">
                <div className="text-4xl text-black">{menu.icon}</div>
                <span className="mt-2 text-[13px] text-black">
                  {menu.label}
                </span>
              </div>
            </Link>
          ))}
      </div>
    </div>
  );
};
