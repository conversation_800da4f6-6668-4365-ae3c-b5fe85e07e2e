.slick-list {
  overflow: inherit;
}

.slick-dots li.slick-active button:before {
  color: #374151;
}

.slick-dots li button:before {
  font-size: 10px;
  color: #9ca3af;
}

.icon-launcher-menu {
  background: url(./images/menu-laucher.svg) center center no-repeat !important;
}

.menu-icon {
  min-width: 36px;
  min-height: 36px;

  height: 36px;
  display: block;
  background-position: center;
  background-repeat: no-repeat;
}
.menu-all {
  background: url(./images/menu-all.svg) center center no-repeat !important;
}
.launcher-filter-btn {
  border-radius: 20px;
  height: 38px;
  line-height: 38px;
  padding: 0 16px;
  margin: 0 4px;
  border: 1px solid rgba(156, 163, 175, 0.5);
  color: #374151;
  opacity: 0.8;
  font-size: 14px;
  user-select: none;
  font-weight: 400;
  background-color: #f9fafb;
}
.launcher-filter-btn:hover {
  opacity: 1;
  cursor: pointer;
  background-color: #e5e7eb;
  border: 1px solid rgba(107, 114, 128, 0.6);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.slick-list {
  overflow: hidden;
}
