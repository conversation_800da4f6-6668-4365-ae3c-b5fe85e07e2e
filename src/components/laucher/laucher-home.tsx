import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';
import { convertRoutesToMenuItems, routes } from '../../router';
import './assets/style.scss';

function LaucherHome() {
  // All hooks must be called at the top level

  const { t } = useTranslation('menu');

  const [isDragging, setIsDragging] = useState(false);

  const dragStartPos = useRef<{ x: number; y: number } | null>(null);

  // Constants and derived values
  const menuItems = convertRoutesToMenuItems(t, routes);
  const menuPerPage = 25;
  const totalMenu = Math.ceil(menuItems.length / menuPerPage);
  const dragThreshold = 10; // Minimum distance to consider as drag

  const settings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
  };

  const groupMenu = [];
  for (let i = 0; i < totalMenu; i++) {
    groupMenu.push(
      menuItems.slice(i * menuPerPage, i * menuPerPage + menuPerPage),
    );
  }

  // Handle touch/mouse start events
  const handlePointerStart = (
    e: React.PointerEvent | React.MouseEvent | React.TouchEvent,
  ) => {
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    dragStartPos.current = { x: clientX, y: clientY };
    setIsDragging(false);
  };

  // Handle touch/mouse move events
  const handlePointerMove = (
    e: React.PointerEvent | React.MouseEvent | React.TouchEvent,
  ) => {
    if (!dragStartPos.current) return;

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    const deltaX = Math.abs(clientX - dragStartPos.current.x);
    const deltaY = Math.abs(clientY - dragStartPos.current.y);

    // If movement exceeds threshold, consider it a drag
    if (deltaX > dragThreshold || deltaY > dragThreshold) {
      setIsDragging(true);
    }
  };

  // Handle touch/mouse end events
  const handlePointerEnd = () => {
    // Reset drag state after a short delay to prevent immediate clicks
    setTimeout(() => {
      setIsDragging(false);
      dragStartPos.current = null;
    }, 100);
  };

  return (
    <div className="flex flex-col items-center justify-center select-none bg-white min-h-screen">
      <div className="mb-8 flex justify-start w-full max-w-screen-lg px-8">
        <div className="launcher-filter-btn">Tất cả</div>
        <div className="launcher-filter-btn">Bài viết</div>
      </div>
      <Slider {...settings} className="w-full max-w-screen-lg">
        {groupMenu.map((group, index) => (
          <div key={index} className="w-full">
            <div className="grid grid-cols-5 grid-rows-4 gap-4">
              {group.map((menu, i) => (
                <Link
                  to={menu.key}
                  key={i}
                  className="flex flex-col items-center text-gray-700 cursor-pointer"
                  onClick={(e) => {
                    if (isDragging) {
                      e.preventDefault();
                    }
                  }}
                  onPointerDown={handlePointerStart}
                  onPointerMove={handlePointerMove}
                  onPointerUp={handlePointerEnd}
                  onTouchStart={handlePointerStart}
                  onTouchMove={handlePointerMove}
                  onTouchEnd={handlePointerEnd}
                  onMouseDown={handlePointerStart}
                  onMouseMove={handlePointerMove}
                  onMouseUp={handlePointerEnd}
                >
                  <div className="hover:bg-gray-100 hover:rounded-lg p-4 flex flex-col items-center transition-colors duration-200">
                    <div className="text-8xl">{menu.icon}</div>
                    <span className="mt-2 text-lg">{menu.label}</span>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </Slider>
    </div>
  );
}

export { LaucherHome };
