import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import { message, Upload } from 'antd';
import React, { useState } from 'react';
import { apiService } from '../../services/api.service';
import ConsoleService from '../../services/console.service';

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const getBase64 = (img: FileType, callback: (url: string) => void) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
};

const beforeUpload = (file: FileType) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('You can only upload JPG/PNG file!');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must be smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};

interface Props {
  onChange: (file: UploadFile) => void;
}

const AvatarUpload: React.FC<Props> = () => {
  const logger = ConsoleService.register('AvatarUpload');
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | undefined>();

  const handleChange: UploadProps['onChange'] = (info) => {
    logger(info.file.status);
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }

    if (info.file.status === 'done') {
      // Convert file to base64 and upload via apiService
      getBase64(info.file.originFileObj as FileType, (url) => {
        setImageUrl(url); // Set preview image
      });
      setLoading(false);
    }
  };

  const uploadImage = async (options: any) => {
    const { onSuccess, onError, file, onProgress } = options;

    const formData = new FormData();
    const config = {
      'content-type': 'multipart/form-data',
    };
    formData.append('file', file);
    try {
      const response = await apiService.post(
        '/api/admin/v1/media-file/avatar',
        formData,
        config,
      );

      onSuccess('Ok');
      logger(response);
      setImageUrl(
        'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
      ); // Assuming the API returns the URL of the uploaded image
      message.success('Upload successful!');
    } catch (err) {
      logger('Error: ', err);
      message.error('Upload failed!');
      onError(err);
    } finally {
      setLoading(false);
    }
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <Upload
      name="avatar"
      listType="picture-circle"
      className="avatar-upload"
      showUploadList={false}
      beforeUpload={beforeUpload}
      customRequest={uploadImage}
      onChange={handleChange}
    >
      {imageUrl ? (
        <img src={imageUrl} alt="avatar" style={{ width: '100%' }} />
      ) : (
        uploadButton
      )}
    </Upload>
  );
};

export default AvatarUpload;
