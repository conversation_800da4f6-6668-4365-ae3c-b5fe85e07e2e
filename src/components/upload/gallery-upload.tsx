import { PlusOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { Image, Upload, message } from 'antd';
import React, { useState } from 'react';
import ConsoleService from '../../services/console.service';
import { uploadGallery } from './api';

type FileType = UploadFile['originFileObj'];

const getBase64 = (file: FileType | undefined): Promise<string> =>
  new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error('File is undefined'));
      return;
    }
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

interface GalleryUploadProps {
  onChange: (file: UploadFile) => void;
}

const GalleryUpload: React.FC<GalleryUploadProps> = ({ onChange }) => {
  const logger = ConsoleService.register('GalleryUpload');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    onChange(newFileList[0]);
  };

  const uploadImages = async (options: any) => {
    const { onSuccess, onError, file, onProgress } = options;

    const formData = new FormData();
    const config = {
      'content-type': 'multipart/form-data',
    };

    if (Array.isArray(file)) {
      file.forEach((f: any) => {
        formData.append('files', f); // Append mỗi tệp với key 'files'
      });
    } else {
      formData.append('file', file);
    }

    try {
      const response = await uploadGallery(formData, config);

      onSuccess('Ok');
      logger(response);

      setFileList((prevList) =>
        prevList.map((item) => {
          if (Array.isArray(file)) {
            const matchedFile = file.find((f: any) => f.uid === item.uid);
            if (matchedFile) {
              return {
                ...item,
                url: response?.data?.url,
                status: 'done',
              };
            }
            return item;
          }
          return item.uid === file.uid
            ? {
                ...item,
                url: response?.data?.url,
                status: 'done',
              }
            : item;
        }),
      );
    } catch (err) {
      logger('Error: ', err);
      message.error('Upload failed!');
      onError(err);
    }
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <>
      <Upload
        listType="picture-card"
        fileList={fileList}
        onPreview={handlePreview}
        onChange={handleChange}
        customRequest={uploadImages}
        multiple // Cho phép chọn nhiều tệp
      >
        {fileList.length >= 8 ? null : uploadButton}
      </Upload>
      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
    </>
  );
};

export default GalleryUpload;
