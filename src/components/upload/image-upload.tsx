import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import type { GetProp, UploadProps } from 'antd';
import { message, Upload } from 'antd';
import React, { useEffect, useState } from 'react';
import ConsoleService from '../../services/console.service';
import { uploadGallery } from './api';
import { MediaFile } from './type';

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const getBase64 = (img: FileType, callback: (url: string) => void) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
};

const beforeUpload = (file: FileType) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('You can only upload JPG/PNG file!');
  }

  return isJpgOrPng;
};

interface Props {
  onChange: (file: any) => void;
  value: string | undefined;
}

const ImageUpload: React.FC<Props> = ({ onChange, value }) => {
  const logger = ConsoleService.register('AvatarUpload');
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | undefined>();

  useEffect(() => {
    if (value) {
      logger(value);
      setImageUrl(value);
    }
  }, [value]);

  const handleChange: UploadProps['onChange'] = (info) => {
    logger(info.file.status);
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }

    if (info.file.status === 'done') {
      // Convert file to base64 and upload via apiService
      getBase64(info.file.originFileObj as FileType, (url) => {
        setImageUrl(url); // Set preview image
      });
      setLoading(false);
    }
  };

  const uploadImage = async (options: any) => {
    const { onSuccess, onError, file } = options;

    // Let the browser set Content-Type automatically for FormData
    const config = {};

    try {
      const response = await uploadGallery(
        {
          file: file,
          is_public: true,
        },
        config,
      );

      if (response.status.success) {
        onSuccess('Ok');
        logger(response);

        // Transform the response data to include legacy fields for compatibility
        const transformedData: MediaFile = {
          ...response.data,
          _id: String(response.data.id),
          filename: file.name,
          original_filename: file.name,
          public_url: response.data.url, // API returns 'url' field
          mimetype: file.type,
          content_type: file.type,
          size: file.size,
          media_type: file.type.startsWith('image/') ? 'image' : 'file',
        };

        onChange(transformedData);
        setImageUrl(response.data.url); // Use the 'url' field from API
        message.success('Upload successful!');
      } else {
        message.error(response.status.message);
      }
    } catch (err) {
      logger('Error: ', err);
      message.error('Upload failed!');
      onError(err);
    } finally {
      setLoading(false);
    }
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <Upload
      name="avatar"
      listType="picture-card"
      className="avatar-upload"
      showUploadList={false}
      beforeUpload={beforeUpload}
      customRequest={uploadImage}
      onChange={handleChange}
    >
      {imageUrl ? (
        <img src={imageUrl} alt="avatar" style={{ width: '100%' }} />
      ) : (
        uploadButton
      )}
    </Upload>
  );
};

export default ImageUpload;
