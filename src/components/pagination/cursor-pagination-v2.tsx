import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

interface CursorPaginationControlProps {
  onBack: () => void;
  onNext: () => void;
  disabledBack: boolean;
  disabledNext: boolean;
  loading: boolean;
  isFirstPage?: boolean; // Flag để xác định trang đầu tiên, được truyền từ component cha
}

const CursorPaginationV2: React.FC<CursorPaginationControlProps> = ({
  onBack,
  onNext,
  disabledBack,
  disabledNext,
  loading,
  isFirstPage = false, // Mặc định là false nếu không được truyền vào
}) => {
  console.log('isFirstPage', isFirstPage);
  console.log('disabledBack', disabledBack);
  console.log('disabledNext', disabledNext);

  return (
    <div style={{ display: 'flex', gap: '10px' }}>
      <Button
        onClick={onBack}
        disabled={disabledBack || loading}
        icon={<LeftOutlined />}
      >
        Back
      </Button>
      <Button
        onClick={onNext}
        disabled={disabledNext || loading}
        icon={<RightOutlined />}
      >
        Next
      </Button>
    </div>
  );
};

export default CursorPaginationV2;
