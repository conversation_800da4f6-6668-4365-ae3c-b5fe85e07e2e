import { Button, Space } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import ConsoleService from '../../services/console.service';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

interface CursorPaginationProps {
  total?: number;
  goBack?: () => void;
  goNext?: () => void;
  isBack?: boolean;
  isNext?: boolean;
  showTotal?: boolean;
}

const CursorPagination: React.FC<CursorPaginationProps> = ({
  total,
  goBack,
  goNext,
  isBack,
  isNext,
  showTotal = true,
}) => {
  const { t } = useTranslation('pagination');
  const logger = ConsoleService.register('cursor-pagination');
  logger({
    total,
    isBack,
    isNext,
  });
  return (
    <Space direction="horizontal">
      {showTotal && <span>{t('totalItems', { total })}</span>}
      <Button
        onClick={goBack}
        disabled={!isBack}
        className="mr-1"
        icon={<LeftOutlined />}
      ></Button>
      <Button
        onClick={goNext}
        disabled={!isNext}
        icon={<RightOutlined />}
      ></Button>
    </Space>
  );
};

export default CursorPagination;
