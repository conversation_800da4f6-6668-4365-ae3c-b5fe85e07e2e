import { Space, Typography } from 'antd';
import React from 'react';
import { BackButton } from '../button';

const { Title } = Typography;

interface FormHeaderProps {
  title: string;
  showBackButton?: boolean;
  backDestination?: string;
}

const FormHeader: React.FC<FormHeaderProps> = ({
  title,
  showBackButton = true,
  backDestination = 'list',
}) => {
  return (
    <div
      style={{
        marginBottom: 24,
        paddingBottom: 16,
        borderBottom: '1px solid #f0f0f0',
      }}
    >
      <Space align="center">
        {showBackButton && <BackButton destination={backDestination} />}
        <Title level={3} style={{ margin: 0 }}>
          {title}
        </Title>
      </Space>
    </div>
  );
};

export default FormHeader;
