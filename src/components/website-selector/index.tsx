import {
  CheckOutlined,
  DownOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Avatar, Button, Dropdown, Input, List, Typography } from 'antd';
import React, { useState } from 'react';
import './style.scss';

const { Text } = Typography;

// D<PERSON> liệu mock cho danh sách các website
const mockWebsites = [
  {
    id: 1,
    name: 'Blog của MAX',
    role: 'Chủ sở hữu',
    thumbnail:
      'https://static.wixstatic.com/media/72c0b2_8cc123dc4dd1422484bc32f6dbe52568~mv2.jpg/v1/fill/w_120,h_120,al_c,q_80,usm_0.66_1.00_0.01,enc_auto/72c0b2_8cc123dc4dd1422484bc32f6dbe52568~mv2.jpg',
    isSelected: true,
  },
  {
    id: 2,
    name: '<PERSON> <PERSON><PERSON> hà<PERSON>',
    role: '<PERSON><PERSON> sở hữu',
    thumbnail:
      'https://static.wixstatic.com/media/72c0b2_52396ec2c1544d96a4e73f4ee4be5304~mv2.jpg/v1/fill/w_120,h_120,al_c,q_80,usm_0.66_1.00_0.01,enc_auto/72c0b2_52396ec2c1544d96a4e73f4ee4be5304~mv2.jpg',
    isSelected: false,
  },
  {
    id: 3,
    name: 'Trang cá nhân',
    role: 'Quản trị viên',
    thumbnail:
      'https://static.wixstatic.com/media/72c0b2_95b83bfcc924409d9dce380a87158a77~mv2.jpg/v1/fill/w_120,h_120,al_c,q_80,usm_0.66_1.00_0.01,enc_auto/72c0b2_95b83bfcc924409d9dce380a87158a77~mv2.jpg',
    isSelected: false,
  },
];

const WebsiteSelector: React.FC = () => {
  const [websites, setWebsites] = useState(mockWebsites);
  const [searchValue, setSearchValue] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const selectedWebsite =
    websites.find((site) => site.isSelected) || websites[0];

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleSelectWebsite = (id: number) => {
    setWebsites(
      websites.map((site) => ({
        ...site,
        isSelected: site.id === id,
      })),
    );
    setDropdownVisible(false);
  };

  const handleCreateNew = () => {
    console.log('Tạo trang web mới');
    setDropdownVisible(false);
  };

  const handleViewAll = () => {
    console.log('Xem tất cả trang web');
    setDropdownVisible(false);
  };

  const filteredWebsites = websites.filter((site) =>
    site.name.toLowerCase().includes(searchValue.toLowerCase()),
  );

  // Define the dropdown content render function
  const renderDropdownContent = () => (
    <div className="website-dropdown">
      <div className="search-section">
        <Input
          prefix={<SearchOutlined />}
          placeholder="Tìm kiếm tên trang web..."
          value={searchValue}
          onChange={handleSearchChange}
          className="search-input"
        />
        <Button
          type="primary"
          icon={<PlusOutlined />}
          className="create-button"
          onClick={handleCreateNew}
        >
          Tạo trang web mới
        </Button>
      </div>

      <List
        className="website-list"
        itemLayout="horizontal"
        dataSource={filteredWebsites}
        renderItem={(site) => (
          <List.Item
            className={site.isSelected ? 'selected' : ''}
            onClick={() => handleSelectWebsite(site.id)}
          >
            <List.Item.Meta
              avatar={<Avatar shape="square" size={48} src={site.thumbnail} />}
              title={site.name}
              description={`Vai trò: ${site.role}`}
            />
            {site.isSelected && <CheckOutlined className="check-icon" />}
          </List.Item>
        )}
      />

      <div className="view-all">
        <Button type="link" onClick={handleViewAll}>
          Đến Tất cả trang web
        </Button>
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={renderDropdownContent}
      open={dropdownVisible}
      onOpenChange={setDropdownVisible}
      trigger={['click']}
      placement="bottomLeft"
      arrow
    >
      <div className="website-selector">
        <Avatar shape="square" size={32} src={selectedWebsite.thumbnail} />
        <span className="website-name">{selectedWebsite.name}</span>
        <DownOutlined className="dropdown-icon" />
      </div>
    </Dropdown>
  );
};

export default WebsiteSelector;
