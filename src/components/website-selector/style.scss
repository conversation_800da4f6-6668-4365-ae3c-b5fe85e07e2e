.website-selector {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 15px;
  height: 56px;
  border-right: 1px solid #f0f0f0;

  .website-name {
    margin: 0 10px;
    font-weight: 500;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dropdown-icon {
    color: #999;
    font-size: 12px;
  }
}

.website-dropdown {
  width: 400px;
  background-color: #fff;
  box-shadow:
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border-radius: 4px;

  .search-section {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .search-input {
      margin-bottom: 12px;
    }

    .create-button {
      width: 100%;
    }
  }

  .website-list {
    max-height: 350px;
    overflow-y: auto;

    .ant-list-item {
      padding: 10px 16px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f5f5;
      }

      &.selected {
        background-color: #f0f5ff;
      }

      .ant-list-item-meta-title {
        margin-bottom: 4px;
        font-weight: 500;
      }

      .ant-list-item-meta-description {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }

      .check-icon {
        color: #1890ff;
      }
    }
  }

  .view-all {
    text-align: center;
    padding: 10px 0;
    border-top: 1px solid #f0f0f0;

    button {
      padding: 0;
    }
  }
}
