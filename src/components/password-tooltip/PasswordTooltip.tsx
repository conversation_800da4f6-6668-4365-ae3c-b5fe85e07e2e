import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import React from 'react';
import './PasswordTooltip.scss';

interface PasswordRequirement {
  text: string;
  test: (password: string) => boolean;
}

interface PasswordTooltipProps {
  password: string;
  children: React.ReactElement;
  requirements?: PasswordRequirement[];
}

const defaultRequirements: PasswordRequirement[] = [
  {
    text: 'Có cả chữ hoa và chữ thường',
    test: (password: string) =>
      /[a-z]/.test(password) && /[A-Z]/.test(password),
  },
  {
    text: '<PERSON><PERSON> ký hiệu đặc biệt (#$&)',
    test: (password: string) => /[#$&]/.test(password),
  },
  {
    text: 'Độ dài từ 8 ký tự trở lên',
    test: (password: string) => password.length >= 8,
  },
];

const PasswordTooltip = React.forwardRef<
  HTMLInputElement,
  PasswordTooltipProps
>(({ password, children, requirements = defaultRequirements }, ref) => {
  const evaluatedRequirements = requirements.map((req) => ({
    text: req.text,
    satisfied: req.test(password),
  }));

  const tooltipContent = (
    <div className="p-2">
      <div className="text-sm font-medium mb-2 text-gray-700">
        Gợi ý mật khẩu mạnh
      </div>
      <div className="text-xs text-gray-600 mb-3">Nên có các yếu tố sau:</div>
      <div className="space-y-2">
        {evaluatedRequirements.map((req, index) => (
          <div key={index} className="flex items-center space-x-2">
            {req.satisfied ? (
              <CheckOutlined className="text-green-500 text-xs" />
            ) : (
              <CloseOutlined className="text-gray-400 text-xs" />
            )}
            <span
              className={`text-xs ${
                req.satisfied ? 'text-green-600' : 'text-gray-500'
              }`}
            >
              {req.text}
            </span>
          </div>
        ))}
      </div>
    </div>
  );

  // Clone children và truyền ref để đảm bảo Tooltip có thể truy cập DOM element
  const clonedChildren = React.cloneElement(children, {
    ...children.props,
    ref,
  });

  return (
    <Tooltip
      title={tooltipContent}
      placement="right"
      trigger="focus"
      overlayClassName="password-tooltip"
      color="white"
      overlayStyle={{
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
      }}
    >
      {clonedChildren}
    </Tooltip>
  );
});

PasswordTooltip.displayName = 'PasswordTooltip';

export default PasswordTooltip;
