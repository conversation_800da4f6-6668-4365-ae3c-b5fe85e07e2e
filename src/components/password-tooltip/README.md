# PasswordTooltip Component

A React component that provides a tooltip with password strength validation for form inputs.

## Features

- Shows password requirements with visual indicators (checkmarks/crosses)
- Customizable requirements
- Beautiful tooltip design matching the application's UI
- Real-time validation feedback
- Vietnamese language support

## Usage

### Basic Usage

```tsx
import { PasswordTooltip } from '../../../components/password-tooltip';
import { useState } from 'react';

const [password, setPassword] = useState('');

<PasswordTooltip password={password}>
  <Input.Password onChange={(e) => setPassword(e.target.value)} />
</PasswordTooltip>;
```

### Custom Requirements

```tsx
const customRequirements = [
  {
    text: 'At least 12 characters',
    test: (password: string) => password.length >= 12,
  },
  { text: 'Contains numbers', test: (password: string) => /\d/.test(password) },
  {
    text: 'Contains special characters',
    test: (password: string) => /[!@#$%^&*]/.test(password),
  },
];

<PasswordTooltip password={password} requirements={customRequirements}>
  <Input.Password onChange={(e) => setPassword(e.target.value)} />
</PasswordTooltip>;
```

## Props

| Prop         | Type                  | Required | Default              | Description                            |
| ------------ | --------------------- | -------- | -------------------- | -------------------------------------- |
| password     | string                | Yes      | -                    | The current password value to validate |
| children     | React.ReactElement    | Yes      | -                    | The input element to wrap with tooltip |
| requirements | PasswordRequirement[] | No       | Default requirements | Custom validation requirements         |

## Default Requirements

1. **Chữ hoa và chữ thường** - Contains both uppercase and lowercase letters
2. **Ký hiệu (#$&)** - Contains special characters (#, $, &)
3. **Mật khẩu dài hơn (tối thiểu 8 ký tự)** - At least 8 characters long

## Styling

The component uses CSS classes for styling:

- `.password-tooltip` - Main tooltip container
- Custom Tailwind classes for internal elements

## Dependencies

- Ant Design (Tooltip, Icons)
- React
- Tailwind CSS (for styling)
