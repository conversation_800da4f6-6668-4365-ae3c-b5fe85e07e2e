import { Affix } from 'antd';
import React, { ReactNode } from 'react';

interface SaveBarTopProps {
  children: ReactNode;
  group?: string;
  action?: string;
  icon?: ReactNode;
}

const SaveBarBottom: React.FC<SaveBarTopProps> = ({
  children,
  group,
  action,
  icon,
}) => (
  <Affix offsetBottom={0}>
    <div
      className="bg-white flex w-full p-3 justify-between"
      style={{ boxShadow: 'rgba(17, 17, 26, 0.1) 0px 1px 0px' }}
    >
      <div className="flex gap-6">
        <div className="flex justify-center items-center">{icon}</div>
        <div className="">
          <div className="">{group}</div>
          <div className="font-bold text-xl">{action}</div>
        </div>
      </div>
      <div className="">{children}</div>
    </div>
  </Affix>
);

export default SaveBarBottom;
