import * as Icons from '@ant-design/icons';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Dropdown, MenuProps } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../../pages/auth/auth';
import {
  PasswordModal,
  ProfileModal,
  TotpModal,
} from '../../pages/user/components';
import useUserStore from '../../pages/user/store';

export const UserProfile: React.FC = () => {
  const { logout } = useAuthStore();
  const { t } = useTranslation('menu');
  const { getProfile, profile } = useUserStore();
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showTotpModal, setShowTotpModal] = useState(false);
  useEffect(() => {
    getProfile();
  }, [getProfile]);

  const handleLogout = () => {
    logout();
  };

  const handleProfile = () => {
    setShowProfileModal(true);
  };
  const handleProfileModal = (status: boolean) => {
    setShowProfileModal(status);
  };

  const handlePasswordModal = () => {
    setShowPasswordModal(false);
  };

  const handleTotpModal = (status: boolean) => {
    setShowTotpModal(status);
  };

  const handleUpdateTelegram = () => {};
  const items: MenuProps['items'] = [
    {
      key: 'profile',
      label: (
        <span onClick={() => handleProfile()}>
          <UserOutlined /> {t('Thông tin cá nhân')}{' '}
        </span>
      ),
    },
    {
      key: 'changePassword',
      label: (
        <span onClick={() => setShowPasswordModal(true)}>
          <Icons.LockOutlined /> {t('Đổi mật khẩu')}{' '}
        </span>
      ),
    },
    {
      key: 'configTotp',
      label: (
        <span onClick={() => setShowTotpModal(true)}>
          <Icons.SafetyOutlined /> {t('Xác thực hai yếu tố')}{' '}
        </span>
      ),
    },
    {
      key: 'updateTelegram',
      label: (
        <span onClick={() => handleUpdateTelegram()}>
          <Icons.EditOutlined /> {t('Cập nhật Telegram ID')}{' '}
        </span>
      ),
    },
    {
      key: 'logout',
      label: (
        <span onClick={() => handleLogout()}>
          <LogoutOutlined /> {t('Đăng xuất')}{' '}
        </span>
      ),
    },
  ];

  return (
    <>
      <Dropdown menu={{ items }} trigger={['hover']} placement="bottomRight">
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            marginRight: '16px',
            cursor: 'pointer',
          }}
        >
          <Avatar icon={<UserOutlined />} style={{ marginRight: '8px' }} />
          <span>{profile?.full_name}</span>
        </div>
      </Dropdown>
      {showProfileModal && (
        <ProfileModal
          showModal={showProfileModal}
          onChange={handleProfileModal}
        ></ProfileModal>
      )}
      {showPasswordModal && (
        <PasswordModal
          showModal={showPasswordModal}
          onChange={handlePasswordModal}
        ></PasswordModal>
      )}
      {showTotpModal && (
        <TotpModal
          showModal={showTotpModal}
          onChange={handleTotpModal}
        ></TotpModal>
      )}
    </>
  );
};
