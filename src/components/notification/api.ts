import { ApiResponse, apiService } from '../../services/api.service';

// Notification types
export interface Notification {
  id: number;
  title: string;
  description: string;
  time: string;
  read: boolean;
  avatar?: string;
  type?: string;
}

export interface NotificationListResponse {
  status: any;
  data: Notification[];
  meta: {
    next_cursor?: string;
    has_more: boolean;
  };
}

export interface NotificationDetailResponse {
  status: any;
  data: Notification;
}

export interface UnreadCountResponse {
  status: any;
  data: { unread_count: number };
}

const BASE_URL = '/api/admin/v1/notification-users';

// Get notifications (with cursor pagination)
export async function getNotifications(
  params?: any,
): Promise<NotificationListResponse> {
  const response = await apiService.get<NotificationListResponse>(
    BASE_URL,
    params,
  );
  return response.data;
}

// Get notification detail
export async function getNotificationDetail(
  id: number,
): Promise<NotificationDetailResponse> {
  const response = await apiService.get<NotificationDetailResponse>(
    `${BASE_URL}/${id}`,
  );
  return response.data;
}

// Mark notification as read
export async function markNotificationRead(
  id: number,
): Promise<ApiResponse<any>> {
  const response = await apiService.put<ApiResponse<any>>(
    `${BASE_URL}/${id}/read`,
    {},
  );
  return response.data;
}

// Get unread notification count
export async function getUnreadCount(): Promise<UnreadCountResponse> {
  const response = await apiService.get<UnreadCountResponse>(
    `${BASE_URL}/unread-count`,
  );
  return response.data;
}

// Create notification
export async function createNotification(
  payload: any,
): Promise<NotificationDetailResponse> {
  const response = await apiService.post<NotificationDetailResponse>(
    BASE_URL,
    payload,
  );
  return response.data;
}

// Delete notification
export async function deleteNotification(
  id: number,
): Promise<ApiResponse<any>> {
  const response = await apiService.delete<ApiResponse<any>>(
    `${BASE_URL}/${id}`,
  );
  return response.data;
}
