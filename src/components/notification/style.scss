.notification-icon {
  padding: 0 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 56px;
}

.notification-dropdown {
  min-width: 300px;
  max-width: 400px;
  background-color: #fff;
  box-shadow:
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border-radius: 4px;

  .notification-header {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;

    a {
      color: #1890ff;
      font-size: 12px;
    }
  }

  .notification-list {
    max-height: 400px;
    overflow-y: auto;
    background-color: #fff;

    .ant-list-item {
      padding: 12px 16px;
      transition: background-color 0.3s;
      background-color: #fff;

      &:hover {
        background-color: #f5f5f5;
      }

      &.unread {
        background-color: #e6f7ff;
      }

      .notification-time {
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
      }
    }
  }

  .notification-footer {
    text-align: center;
    padding: 10px;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;

    a {
      color: #1890ff;
    }

    .load-more-btn {
      padding: 0;
      height: auto;

      &:hover {
        background: transparent;
      }
    }
  }
}
