#!/bin/bash

# Script to list all .ts and .tsx files and save to logs directory
# Supports Windows (Git Bash/WSL), macOS, and Ubuntu

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "ubuntu"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "mac"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        echo "windows"
    elif [[ -n "$WSL_DISTRO_NAME" ]] || [[ -n "$WSLENV" ]]; then
        echo "wsl"
    else
        echo "unknown"
    fi
}

# Function to get project root directory
get_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Go up one level from scripts directory to get project root
    echo "$(dirname "$script_dir")"
}

# Function to create logs directory if it doesn't exist
ensure_logs_dir() {
    local logs_dir="$1/logs"

    if [[ ! -d "$logs_dir" ]]; then
        print_info "Creating logs directory: $logs_dir"
        mkdir -p "$logs_dir"
        if [[ $? -eq 0 ]]; then
            print_success "Logs directory created successfully"
        else
            print_error "Failed to create logs directory"
            return 1
        fi
    else
        print_info "Logs directory already exists: $logs_dir"
    fi
}

# Function to find .ts and .tsx files based on OS
find_ts_files() {
    local project_root="$1"
    local os_type="$2"

    print_info "Searching for .ts and .tsx files in: $project_root"
    print_info "Detected OS: $os_type"

    case "$os_type" in
    "ubuntu" | "wsl")
        # Ubuntu/WSL - use find with GNU options, exclude node_modules
        find "$project_root" -type f \( -name "*.ts" -o -name "*.tsx" \) -not -path "*/node_modules/*" | sort
        ;;
    "mac")
        # macOS - use find with BSD options, exclude node_modules
        find "$project_root" -type f \( -name "*.ts" -o -name "*.tsx" \) -not -path "*/node_modules/*" | sort
        ;;
    "windows")
        # Windows (Git Bash/MSYS2) - use find with basic options, exclude node_modules
        find "$project_root" -type f \( -name "*.ts" -o -name "*.tsx" \) -not -path "*/node_modules/*" | sort
        ;;
    *)
        # Fallback for unknown OS, exclude node_modules
        print_warning "Unknown OS detected, using basic find command"
        find "$project_root" -type f \( -name "*.ts" -o -name "*.tsx" \) -not -path "*/node_modules/*" | sort
        ;;
    esac
}

# Function to generate file statistics
generate_stats() {
    local file_list="$1"
    local total_files=$(echo "$file_list" | wc -l)
    local total_size=0

    # Calculate total size (without colored output)
    while IFS= read -r file; do
        if [[ -f "$file" ]]; then
            local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0")
            total_size=$((total_size + file_size))
        fi
    done <<<"$file_list"

    # Convert bytes to human readable format
    local human_size=""
    if [[ $total_size -gt 1073741824 ]]; then
        human_size=$(echo "scale=2; $total_size / 1073741824" | bc 2>/dev/null || echo "$((total_size / 1073741824))")
        human_size="${human_size} GB"
    elif [[ $total_size -gt 1048576 ]]; then
        human_size=$(echo "scale=2; $total_size / 1048576" | bc 2>/dev/null || echo "$((total_size / 1048576))")
        human_size="${human_size} MB"
    elif [[ $total_size -gt 1024 ]]; then
        human_size=$(echo "scale=2; $total_size / 1024" | bc 2>/dev/null || echo "$((total_size / 1024))")
        human_size="${human_size} KB"
    else
        human_size="${total_size} bytes"
    fi

    echo "Total TypeScript files: $total_files"
    echo "Total size: $human_size ($total_size bytes)"
}

# Function to save file list with metadata
save_file_list() {
    local project_root="$1"
    local file_list="$2"
    local os_type="$3"
    local output_file="$4"

    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local hostname=$(hostname 2>/dev/null || echo "unknown")
    local user=$(whoami 2>/dev/null || echo "unknown")

    # Create header
    cat >"$output_file" <<EOF
# TypeScript Files List Report
# Generated on: $timestamp
# System: $os_type
# Host: $hostname
# User: $user
# Project Root: $project_root
# 
# $(generate_stats "$file_list")
#
# ========================================
# File List:
# ========================================

EOF

    # Add file list with relative paths
    echo "$file_list" | while IFS= read -r file; do
        if [[ -f "$file" ]]; then
            # Convert to relative path from project root
            local rel_path=$(realpath --relative-to="$project_root" "$file" 2>/dev/null ||
                python3 -c "import os.path; print(os.path.relpath('$file', '$project_root'))" 2>/dev/null ||
                echo "$file")
            echo "$rel_path"
        fi
    done >>"$output_file"
}

# Main function
main() {
    print_info "Starting TypeScript files sync list generation..."

    # Detect OS
    local os_type=$(detect_os)

    # Get project root
    local project_root=$(get_project_root)
    print_info "Project root: $project_root"

    # Ensure logs directory exists
    ensure_logs_dir "$project_root"
    if [[ $? -ne 0 ]]; then
        exit 1
    fi

    # Generate output filename with timestamp
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local output_file="$project_root/logs/ts_files_list_${timestamp}.txt"

    # Find all .ts and .tsx files
    print_info "Scanning for .ts and .tsx files..."
    local file_list=$(find_ts_files "$project_root" "$os_type")

    if [[ -z "$file_list" ]]; then
        print_warning "No .ts or .tsx files found in the project"
        exit 0
    fi

    # Count files
    local file_count=$(echo "$file_list" | wc -l)
    print_success "Found $file_count TypeScript files"

    # Calculate and display statistics
    print_info "Calculating file statistics..."
    local stats=$(generate_stats "$file_list")

    # Save to file
    print_info "Saving file list to: $output_file"
    save_file_list "$project_root" "$file_list" "$os_type" "$output_file"

    if [[ $? -eq 0 ]]; then
        print_success "File list saved successfully!"
        print_info "Output file: $output_file"

        # Show first few files as preview
        print_info "Preview (first 10 files):"
        echo "$file_list" | head -10 | while IFS= read -r file; do
            local rel_path=$(realpath --relative-to="$project_root" "$file" 2>/dev/null ||
                python3 -c "import os.path; print(os.path.relpath('$file', '$project_root'))" 2>/dev/null ||
                echo "$file")
            echo "  - $rel_path"
        done

        if [[ $file_count -gt 10 ]]; then
            print_info "... and $((file_count - 10)) more files"
        fi

    else
        print_error "Failed to save file list"
        exit 1
    fi
}

# Run main function
main "$@"
