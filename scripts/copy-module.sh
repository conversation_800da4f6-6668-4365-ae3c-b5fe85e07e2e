#!/bin/bash
#docs/dev/copy-module.sh "user" "User" "tour-customer" "TourCustomer"
newName=$3
newClass=$4
oldName=${1:-"module-template"}
oldClass=${2:-"ModuleTemplate"}

# Copy the directory
cp -R "src/pages/$oldName" "src/pages/$newName"

# Replace content within files and rename files
find "src/pages/$newName" -type f | while read -r file; do
    # Replace content within the file
    sed -i '' -e "s/$oldClass/$newClass/g" \
              -e "s/$oldName/$newName/g" \
              -e "s/${oldName//-/_}/${newName//-/_}/g" "$file"

    # Replace the filename if it contains the specified strings
    baseName=$(basename "$file")
    dirName=$(dirname "$file")
    newFileName=$(echo "$baseName" | sed -e "s/$oldClass/$newClass/g" \
                                         -e "s/$oldName/$newName/g" \
                                         -e "s/${oldName//-/_}/${newName//-/_}/g")
    mv "$file" "$dirName/$newFileName"
done

# Rename files if necessary
find "src/pages/$newName" -type f | while read -r file; do
    baseName=$(basename "$file")
    dirName=$(dirname "$file")
    newFileName=$(echo "$baseName" | sed -e "s/$oldName/$newName/g" \
                                         -e "s/${oldName//-/_}/${newName//-/_}/g")
    if [ "$baseName" != "$newFileName" ]; then
        mv "$file" "$dirName/$newFileName"
    fi
done
