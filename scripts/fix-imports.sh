#!/bin/bash

# This script fixes all the duplicate imports of useNavigateTenant and changes them to import from the hooks index file

# Find all files with duplicate imports
echo "Fixing duplicate imports..."
find . -type f -name "*.tsx" -o -name "*.ts" | xargs grep -l "import useNavigateTenant from '../../../hooks/useNavigateTenant';\s*import useNavigateTenant from" | while read file; do
    echo "Fixing duplicate imports in $file"
    # Replace duplicate imports with a single import from hooks index
    sed -i '' 's/import useNavigateTenant from '\''\.\.\/\.\.\/\.\.\/hooks\/useNavigateTenant'\'';\s*import useNavigateTenant from '\''\.\.\/\.\.\/\.\.\/hooks\/useNavigateTenant'\'';/import { useNavigateTenant } from '\''\.\.\/\.\.\/\.\.\/hooks'\'';/g' "$file"
done

# Find all files with direct imports
echo "Fixing direct imports..."
find . -type f -name "*.tsx" -o -name "*.ts" | xargs grep -l "import useNavigateTenant from '../../../hooks/useNavigateTenant';" | while read file; do
    echo "Fixing direct import in $file"
    # Replace direct imports with imports from hooks index
    sed -i '' 's/import useNavigateTenant from '\''\.\.\/\.\.\/\.\.\/hooks\/useNavigateTenant'\'';/import { useNavigateTenant } from '\''\.\.\/\.\.\/\.\.\/hooks'\'';/g' "$file"
done

echo "All imports fixed successfully!"
