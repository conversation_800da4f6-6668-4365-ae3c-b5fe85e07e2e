#!/bin/bash

# Check if a directory argument is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <target_directory>"
  exit 1
fi

TARGET_DIR="$1"

# Check if the provided argument is a directory
if [ ! -d "$TARGET_DIR" ]; then
  echo "Error: $TARGET_DIR is not a directory."
  exit 1
fi

# Set environment variables for i18next-parser
export I18NEXT_INPUT="$TARGET_DIR/**/*.{js,jsx,ts,tsx}"
export I18NEXT_OUTPUT="$TARGET_DIR/i18n"

# Ensure the output directory exists
mkdir -p "$I18NEXT_OUTPUT"

# Run i18next-parser with the specified configuration
i18next --config i18next-parser.config.js