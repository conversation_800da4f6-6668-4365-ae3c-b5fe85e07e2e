#!/bin/bash

for entry in src/pages/*; do
  if [ -f "$entry" ]; then
    echo "$entry is a file"
    # Commands for files
  elif [ -d "$entry" ]; then
    echo "$entry is a directory"
    I18NEXT_INPUT="$entry/**/*.{js,jsx,ts,tsx}"
    I18NEXT_OUTPUT="$entry/i18n"
    echo "Processing with input: $I18NEXT_INPUT and output: $I18NEXT_OUTPUT"
    I18NEXT_INPUT="$I18NEXT_INPUT" I18NEXT_OUTPUT="$I18NEXT_OUTPUT" i18next --config i18next-parser.config.js
  fi
done