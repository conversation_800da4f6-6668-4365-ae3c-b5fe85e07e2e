---
description: 
globs: 
alwaysApply: false
---
# api rule
- Thêm try catch
export const verifyOtp = async (
  payload: VerifyOtpRequest,
): Promise<ApiResponse<LoginResponse>> => {
  try {
    const response = await apiService.post<any>(
      '/api/admin/v1/auth/verify-otp',
      payload,
    );
    return response.data;
  } catch (error: any) {
    if (error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};