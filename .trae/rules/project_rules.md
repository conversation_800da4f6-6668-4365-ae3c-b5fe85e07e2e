# Project Rules for Trae AI

## Project Configuration

- **Base URL**: http://localhost:9200
- **Default Login Credentials**:
  - Email: <EMAIL>
  - Password: 12345678

## UI Testing & Development Guidelines

- **UI Testing**: Always use MCP browser-tools to check console logs on the UI at http://localhost:9200
- **Code Quality Check**: Run `npx --no-install lint-staged` after completing any task and fix any errors that appear
- **Console Monitoring**: Regularly check browser console for JavaScript errors and warnings during development

## Project Overview

This is a multi-tenant React/TypeScript application built with RSBuild, using Ant Design for UI components and following a modular architecture pattern.

## Technology Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: RSBuild
- **UI Library**: Ant Design (antd)
- **Styling**: Tailwind CSS + SCSS + Styled Components
- **State Management**: Zustand + MobX
- **Routing**: React Router DOM v6
- **HTTP Client**: Axios
- **Internationalization**: i18next + react-i18next
- **Editor**: TipTap (reactjs-tiptap-editor, wncms-tiptap)
- **Testing**: Not configured yet
- **Linting**: ESLint + Prettier

## Project Structure Rules

### 1. Folder Organization

```
src/
├── components/     # Reusable UI components
├── pages/         # Page-level components (feature modules)
├── router/        # Route definitions and modules
├── services/      # API services and utilities
├── hooks/         # Custom React hooks
├── i18n/          # Internationalization files
├── assets/        # Static assets (SCSS, images)
├── utils/         # Utility functions
└── types.global.ts # Global TypeScript types
```

### 2. Component Guidelines

#### Component Structure

- Each component should have its own folder with an `index.ts` barrel export
- Use TypeScript for all components
- Follow React functional component pattern with hooks
- Use Ant Design components as base UI elements

#### Naming Conventions

- Components: PascalCase (e.g., `UserProfile`, `BlogList`)
- Files: kebab-case for folders, PascalCase for component files
- Hooks: camelCase starting with "use" (e.g., `useNavigateTenant`)
- Services: camelCase ending with "Service" (e.g., `apiService`)

### 3. Routing Architecture

#### Module-Based Routing

- Each feature module has its own router file in `src/router/`
- Router files follow pattern: `module-{feature}.ts`
- Use lazy loading for page components
- Route structure supports multi-tenant architecture with `/dashboard/:tenant_id` prefix

#### Route Definition Structure

```typescript
export interface RouterItem {
  path: string;
  name: string;
  component?: React.ComponentType<any>;
  permissions?: string[];
  icon?: string;
  hideMenu?: boolean;
  hideChildrenInMenu?: boolean;
  isGroup?: boolean;
  parentKey?: string;
  routes?: RouterItem[];
}
```

#### Authentication & Authorization

- Protected routes use `<ProtectedRoute />` wrapper
- Tenant-specific routes use `<TenantRoute />` wrapper
- Permission-based access control using `permissions` array in route config

### 4. API Service Patterns

#### Service Architecture

- Use `apiService` from `src/services/api.service.ts` for HTTP requests
- All API responses follow standardized interface:
  ```typescript
  interface ApiResponse<T = object> {
    status: Status;
    data: T;
    errors?: string[] | Record<string, string | string[]>;
  }
  ```
- Support for both cursor and offset pagination
- Automatic tenant_id injection in request headers

#### Service Guidelines

- Create dedicated service files for each feature module
- Use TypeScript interfaces for request/response types
- Implement proper error handling and loading states
- Use React hooks for data fetching (consider React Query in future)

### 5. State Management

#### Zustand for Global State

- Use Zustand for global application state
- MobX for complex local component state
- Avoid prop drilling by using appropriate state management

#### Local State Guidelines

- Use React hooks (useState, useReducer) for local component state
- Custom hooks for reusable stateful logic
- Context API for feature-specific shared state

### 6. Styling Guidelines

#### CSS Architecture

- Tailwind CSS for utility-first styling
- SCSS modules for component-specific styles
- Styled Components for dynamic styling
- Ant Design theme customization

#### Style Organization

- Global styles in `src/assets/scss/`
- Component-specific styles co-located with components
- Use CSS-in-JS for theme-dependent styling

### 7. Internationalization (i18n)

#### Translation Structure

- Translation files in `public/locales/{language}/`
- Use `react-i18next` hooks for translations
- Support for multiple languages (en, vi)
- Feature-specific translation namespaces

### 8. Multi-Tenant Architecture

#### Tenant Management

- Tenant ID extracted from URL parameters
- Automatic tenant context injection
- Tenant-specific API requests
- Tenant selection flow for users

#### URL Structure

- Protected routes: `/dashboard/:tenant_id/feature`
- Auth routes: `/auth/login`, `/auth/tenant-selection`
- Automatic redirects for unauthorized access

### 9. Development Guidelines

#### Code Quality

- Use TypeScript strictly (no `any` where possible)
- Follow ESLint configuration (see `eslint.config.mjs`)
- Use Prettier for code formatting
- Implement proper error boundaries

#### Performance

- Use React.lazy for code splitting
- Implement proper loading states
- Optimize bundle size with tree shaking
- Use React.memo for expensive components

#### Testing (To Be Implemented)

- Unit tests for utility functions
- Component testing with React Testing Library
- Integration tests for critical user flows
- E2E tests for main application features

### 10. Module Development Patterns

#### Creating New Feature Modules

1. Create page components in `src/pages/{feature}/`
2. Create router configuration in `src/router/module-{feature}.ts`
3. Implement CRUD operations (List, Form, Detail views)
4. Add proper TypeScript interfaces
5. Implement internationalization
6. Add proper permissions and access control

#### Common Page Patterns

- **List Pages**: Data tables with pagination, filters, actions
- **Form Pages**: Create/Edit forms with validation
- **Detail Pages**: Read-only view with related data
- **Router Pages**: Feature-level routing component

### 11. Environment Configuration

#### Environment Variables

- Use `.env` files for configuration
- Prefix React env vars with `REACT_APP_`
- Configure in `rsbuild.config.ts` with dotenv-webpack
- Support for different environments (dev, staging, prod)

### 12. Build and Deployment

#### Build Configuration

- RSBuild for modern build tooling
- Support for SCSS, TypeScript, React
- Environment-specific builds
- Proper asset optimization

## Best Practices Summary

1. **Consistency**: Follow established patterns for new features
2. **TypeScript**: Use strict typing throughout the application
3. **Modularity**: Keep features self-contained and reusable
4. **Performance**: Implement lazy loading and code splitting
5. **Accessibility**: Use Ant Design's built-in accessibility features
6. **Internationalization**: Support multiple languages from the start
7. **Security**: Implement proper authentication and authorization
8. **Testing**: Write tests for critical functionality (to be implemented)
9. **Documentation**: Document complex business logic and API interfaces
10. **Version Control**: Use meaningful commit messages and PR descriptions

## Code Examples

### Creating a New Feature Module

```typescript
// src/router/module-example.ts
import { ExampleList, ExampleForm, ExampleDetail } from '../pages/example';
import { RouterItem } from './index';

export const routes: RouterItem[] = [
  {
    path: 'example',
    name: 'example',
    component: ExampleList,
    permissions: ['EXAMPLE.LIST'],
    icon: 'ExampleOutlined',
    hideMenu: false,
    hideChildrenInMenu: true,
    routes: [
      {
        path: 'example/create',
        name: 'create',
        component: ExampleForm,
        permissions: ['EXAMPLE.CREATE'],
        hideMenu: true,
      },
      {
        path: 'example/:id',
        name: 'detail',
        component: ExampleDetail,
        permissions: ['EXAMPLE.DETAIL'],
        hideMenu: true,
      },
      {
        path: 'example/edit/:id',
        name: 'edit',
        component: ExampleForm,
        permissions: ['EXAMPLE.UPDATE'],
        hideMenu: true,
      },
    ],
  },
];
```

### API Service Pattern

```typescript
// src/services/example.service.ts
import { apiService, ApiResponse, ApiResponsePagination } from './api.service';

export interface ExampleItem {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export const exampleService = {
  getList: (params?: any): Promise<ApiResponsePagination<ExampleItem[]>> =>
    apiService.get('/examples', { params }),

  getById: (id: string): Promise<ApiResponse<ExampleItem>> =>
    apiService.get(`/examples/${id}`),

  create: (data: Partial<ExampleItem>): Promise<ApiResponse<ExampleItem>> =>
    apiService.post('/examples', data),

  update: (
    id: string,
    data: Partial<ExampleItem>,
  ): Promise<ApiResponse<ExampleItem>> =>
    apiService.put(`/examples/${id}`, data),

  delete: (id: string): Promise<ApiResponse<void>> =>
    apiService.delete(`/examples/${id}`),
};
```

This ruleset should help Trae AI understand your project structure, conventions, and best practices for maintaining consistency across the codebase.
